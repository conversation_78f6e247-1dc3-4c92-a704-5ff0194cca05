import{d as v,o as s,c as t,a as e,t as n,F as l,P as c,Q as f,p as y,e as C}from"./main-dd669ad8.js";import{_ as N}from"./_plugin-vue_export-helper-361d09b5.js";const d=a=>(y("data-v-aed669f9"),a=a(),C(),a),S={class:"show-time"},g={class:"date"},k={class:"schedule-box"},w={class:"timetable-title"},x={class:"run-time"},D=d(()=>e("div",{class:"title"},[e("span",null,"\u6267\u884C\u65F6\u95F4")],-1)),F={class:"time"},I=d(()=>e("div",{class:"divider"},null,-1)),B={class:"control"},b=d(()=>e("div",{class:"title"},[e("span",null,"\u7EC4\u63A7\u540D\u79F0")],-1)),E={class:"name"},L=v({__name:"NewCalendar",props:{currentData:null},setup(a){return(T,z)=>{var r,i;return s(),t("div",S,[e("div",g,[e("h2",null,n((r=a.currentData)==null?void 0:r.date),1)]),e("div",k,[(s(!0),t(l,null,c((i=a.currentData)==null?void 0:i.programList,(o,u)=>(s(),t("div",{class:f(["timetable-item",o.repeatType]),key:u+"timetable"},[e("div",w,[e("span",null,n(o.programName),1)]),(s(!0),t(l,null,c(o.schedule,(_,p)=>(s(),t("div",{class:"run-item",key:p+"schedule"},[e("div",x,[D,e("div",F,[e("span",null,n(_.runTime),1)])]),I,e("div",B,[b,e("div",E,[(s(!0),t(l,null,c(_.controlSet,(m,h)=>(s(),t("span",{key:h+"control"},n(m.controlSetName),1))),128))])])]))),128))],2))),128))])])}}});const Q=N(L,[["__scopeId","data-v-aed669f9"]]);export{Q as default};
