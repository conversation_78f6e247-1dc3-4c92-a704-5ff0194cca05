import m from"./Control-b45f96c3.js";import f from"./Login-07c9e807.js";import{d,r as l,h as p,o as b,B as v,w as a,b as e}from"./main-dd669ad8.js";import{_ as g}from"./_plugin-vue_export-helper-361d09b5.js";import"./index.vue_vue_type_script_setup_true_name_ProTable_lang-ce2538eb.js";import"./useSelection-ccce70fc.js";import"./product-327919f5.js";import"./monitor-1739c0cb.js";const x=d({__name:"index",setup(k){const r=l("login"),o=l(),t=l(),c=n=>{n.props.name=="control"?o.value&&o.value.getList():n.props.name=="login"&&t.value&&t.value.getList()};return(n,s)=>{const _=p("el-tab-pane"),i=p("el-tabs");return b(),v(i,{class:"tab-style",modelValue:r.value,"onUpdate:modelValue":s[0]||(s[0]=u=>r.value=u),onTabClick:c,type:"border-card"},{default:a(()=>[e(_,{name:"login",label:"\u767B\u5F55\u65E5\u5FD7"},{default:a(()=>[e(f,{ref_key:"loginRef",ref:t},null,512)]),_:1}),e(_,{name:"control",label:"\u63A7\u5236\u65E5\u5FD7"},{default:a(()=>[e(m,{ref_key:"controlRef",ref:o},null,512)]),_:1})]),_:1},8,["modelValue"])}}});const A=g(x,[["__scopeId","data-v-d30f57e7"]]);export{A as default};
