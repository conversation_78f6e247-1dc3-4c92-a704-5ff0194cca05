import B from"./LineChart-11127dd5.js";import g from"./BarChart-a2925762.js";import{d as E,a3 as V,r as k,h as r,o as u,c,a as d,b as t,w as o,i as _,F as D,B as b,y as v,t as n,R as N,p as S,e as I}from"./main-dd669ad8.js";import{_ as R}from"./_plugin-vue_export-helper-361d09b5.js";import"./cloneDeep-174273b5.js";import"./index-2ff848b4.js";const T=a=>(S("data-v-4a7f1f85"),a=a(),I(),a),z={class:"device-chart"},A={class:"points-chart"},L={key:1,class:"no-data"},U={class:"points-table"},j=T(()=>d("div",{class:"points-table__header"},"\u70B9\u4F4D\u4FE1\u606F",-1)),q={style:{color:"#FF4D4F"}},G={style:{color:"#52C41A"}},H=E({__name:"index",props:{trendData:{default:()=>[]},mode:{default:"1"}},emits:["update:mode"],setup(a,{emit:w}){const C=a;V(()=>C.mode,s=>{s&&(i.value=s)});const i=k("1"),F=s=>w("update:mode",s);return(s,p)=>{var f;const m=r("el-radio-button"),x=r("el-radio-group"),l=r("el-table-column"),y=r("el-table");return u(),c("div",z,[d("div",A,[t(x,{modelValue:i.value,"onUpdate:modelValue":p[0]||(p[0]=e=>i.value=e),class:"chart-mode",onChange:F},{default:o(()=>[t(m,{label:"1"},{default:o(()=>[_("\u6298\u7EBF\u56FE")]),_:1}),t(m,{label:"0"},{default:o(()=>[_("\u67F1\u72B6\u56FE")]),_:1})]),_:1},8,["modelValue"]),(f=a.trendData)!=null&&f.length?(u(),c(D,{key:0},[a.mode==="1"?(u(),b(B,{key:0,data:a.trendData},null,8,["data"])):v("",!0),a.mode==="0"?(u(),b(g,{key:1,data:a.trendData},null,8,["data"])):v("",!0)],64)):(u(),c("div",L,"\u6682\u65E0\u6570\u636E"))]),d("div",U,[j,t(y,{data:a.trendData,border:""},{default:o(()=>[t(l,{prop:"codeName",label:"\u70B9\u4F4D","show-overflow-tooltip":"true"},{default:o(e=>{var h;return[_(n((h=e.row)!=null&&h.aliasCodeName?e.row.aliasCodeName:e.row.codeName),1)]}),_:1}),t(l,{prop:"description",label:"\u63CF\u8FF0","show-overflow-tooltip":"true"}),t(l,{prop:"deviceName",label:"\u6240\u5C5E\u8BBE\u5907","show-overflow-tooltip":"true"}),t(l,{label:"\u66F2\u7EBF\u989C\u8272",width:"100"},{default:o(e=>[d("div",{style:N({backgroundColor:e.row.color,width:"20px",height:"20px",display:"inline-block",borderRadius:"4px"})},null,4)]),_:1}),t(l,{prop:"maxValue",label:"\u6700\u5927\u503C",width:"100"},{default:o(e=>[d("span",q,n(e.row.max),1)]),_:1}),t(l,{prop:"minValue",label:"\u6700\u5C0F\u503C",width:"100"},{default:o(e=>[d("span",G,n(e.row.min),1)]),_:1}),t(l,{prop:"avgValue",label:"\u5E73\u5747\u503C",width:"100"},{default:o(e=>[d("span",null,n(e.row.average),1)]),_:1})]),_:1},8,["data"])])])}}});const X=R(H,[["__scopeId","data-v-4a7f1f85"]]);export{X as default};
