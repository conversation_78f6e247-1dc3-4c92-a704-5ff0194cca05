import{d as s,x as l,o,c as t,a as c,j as r,Q as i,R as u}from"./main-dd669ad8.js";const m=["xlink:href"],d=s({name:"SvgIcon"}),_=s({...d,props:{name:null,prefix:{default:"icon"},iconStyle:null,iconClass:null},setup(e){const n=e,a=l(()=>`#${n.prefix}-${n.name}`);return(f,p)=>(o(),t("svg",{class:i(e.iconClass),style:u(e.iconStyle),"aria-hidden":"true"},[c("use",{"xlink:href":r(a)},null,8,m)],6))}});export{_};
