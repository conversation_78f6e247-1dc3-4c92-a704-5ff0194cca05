import{S as e,U as o,V as n}from"./main-dd669ad8.js";const r=window.__DEBUGGER_VIEW_?"":n,a=t=>e.post(r+o+"/control/set/query",t),l=t=>e.post(r+o+"/control/set/query/config",t),u=t=>e.post(r+o+"/control/set/query/relationship",t),c=t=>e.post(r+o+"/control/set/delete",t),d=t=>e.post(r+o+"/control/set/add",t),p=t=>e.post(r+o+"/control/set/update",t),i=t=>e.post(r+o+"/control/set/run",t),g=t=>e.post(r+o+"/control/set/action/history/query",t,{headers:{noLoading:!0}}),m=t=>e.post(r+o+"/control/set/history/query",t,{headers:{noLoading:!0}}),h=()=>e.getFileText(r+o+"/control/set/get/uuid"),y=(t=1)=>e.getFileText(r+o+"/control/set/get/uuids",{Num:t}),T=t=>e.post(r+o+"/control/schedule/program/query",t),q=t=>e.post(r+o+"/control/schedule/program/query/relationship",t),b=t=>e.post(r+o+"/control/schedule/program/delete",t),k=t=>e.post(r+o+"/control/schedule/program/add",t),C=t=>e.post(r+o+"/control/schedule/program/update",t),L=t=>e.post(r+o+"/control/schedule/program/enable",t),G=t=>e.post(r+o+"/control/schedule/program/query/config",t),R=t=>e.post(r+o+"/control/schedule/calendar/query",t),f=t=>e.post(r+o+"/control/schedule/calendar/add",t),U=t=>e.post(r+o+"/control/schedule/calendar/update",t),_=t=>e.post(r+o+"/control/linkage/query",t),D=t=>e.post(r+o+"/control/linkage/query/config",t),x=t=>e.post(r+o+"/control/linkage/delete",t),E=t=>e.post(r+o+"/control/linkage/add",t),I=t=>e.post(r+o+"/control/linkage/update",t);export{y as a,l as b,d as c,m as d,g as e,a as f,h as g,c as h,D as i,I as j,E as k,_ as l,x as m,R as n,f as o,U as p,u as q,i as r,k as s,C as t,p as u,G as v,L as w,T as x,q as y,b as z};
