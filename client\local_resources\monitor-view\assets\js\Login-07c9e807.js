import{d as n,aJ as g,r as a,aj as c,o as p,c as _,b as m}from"./main-dd669ad8.js";import{_ as d}from"./index.vue_vue_type_script_setup_true_name_ProTable_lang-ce2538eb.js";import{k as f}from"./monitor-1739c0cb.js";import{_ as b}from"./_plugin-vue_export-helper-361d09b5.js";import"./useSelection-ccce70fc.js";const h={class:"login-log table-box"},B=n({name:"LoginLog"}),F=n({...B,setup(T,{expose:i}){const{t:l}=g(),o=a(),r=a({}),u=async e=>{if(e.loginTimeRange&&e.loginTimeRange.length&&(e.loginTimeBegin=e.loginTimeRange[0],e.loginTimeEnd=e.loginTimeRange[1],delete e.loginTimeRange),window.__DEBUGGER_VIEW_)return{data:[]};try{let t=await f(e);if(t)return{data:t.data}}catch{}},s=[{type:"index",label:l("common.table.xuhao"),width:60},{prop:"loginTimeRange",label:"\u767B\u5F55\u65F6\u95F4",isShow:!1,isOnlySearch:!0,search:{el:"date-picker",span:2,props:{type:"datetimerange",valueFormat:"x"}}},{prop:"loginIp",label:"\u767B\u5F55IP",width:180,search:{el:"input",span:1}},{prop:"loginStatus",label:"\u767B\u5F55\u72B6\u6001",width:200,enum:[{value:1,label:"\u767B\u51FA"},{value:2,label:"\u767B\u5F55"}]},{prop:"loginTime",label:"\u767B\u5F55\u65F6\u95F4",customFormat(e){return c(e)},width:180},{prop:"loginType",label:"\u767B\u5F55\u7C7B\u578B",align:"left"},{prop:"loginUsername",label:"\u767B\u5F55\u7528\u6237",align:"left"}];return i({getList:()=>{o.value.getTableList()}}),(e,t)=>(p(),_("div",h,[m(d,{ref_key:"proTable",ref:o,"highlight-current-row":"",columns:s,border:!1,searchCol:{xs:1,sm:2,md:2,lg:3,xl:5},"select-id":"createTime","init-param":r.value,requestApi:u},null,8,["init-param"])]))}});const R=b(F,[["__scopeId","data-v-4c69b5da"]]);export{R as default};
