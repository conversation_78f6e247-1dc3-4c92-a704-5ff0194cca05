import{af as r,aL as a,ak as c}from"./main-dd669ad8.js";const u=(s,t,o,n="warning",l=!1)=>new Promise((m,e)=>{r.confirm(o,a.global.t("common.message.tishi"),{confirmButtonText:a.global.t("common.button.queding"),cancelButtonText:a.global.t("common.button.quxiao"),type:n,draggable:!0}).then(async()=>{if(!await s(t))return e(!1);!l&&c({type:"success",message:a.global.t("common.message.optionMsg")}),m(!0)}).catch(()=>{e(!1)})});export{u};
