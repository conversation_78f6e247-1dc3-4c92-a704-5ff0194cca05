import{d as M,u as L,r as j,K as N,L as h,g as F,h as u,o as n,c,b as s,w as r,M as D,T as P,p as V,e as B,a as e,N as S,O as y,F as k,P as x,t as p,i as U}from"./main-dd669ad8.js";import{_ as A}from"./index.vue_vue_type_script_setup_true_name_SvgIcon_lang-c82a9164.js";import{s as $,g as G,a as H}from"./project-54c43e68.js";import{_ as K}from"./_plugin-vue_export-helper-361d09b5.js";const W=""+new URL("../png/project-logo-d908e92a.png",import.meta.url).href,v=i=>(V("data-v-92c13415"),i=i(),B(),i),q={class:"home"},z=v(()=>e("header",null,[e("p",null,"\u76D1\u63A7\u573A\u666F\u4E2D\u5FC3")],-1)),J=v(()=>e("span",null,"\u5217\u8868",-1)),Q=v(()=>e("span",null,"\u89C6\u56FE",-1)),X={class:"projects-box"},Y={class:"projects-list"},Z={class:"project-info"},ee=["src"],oe={class:"info-box"},te={class:"title"},se={class:"desc"},ae={class:"btn"},ne={class:"projects-card"},ce=["onClick"],re=["src"],ie={class:"info-box"},le=["title"],_e={class:"desc"},de=M({name:"home"}),ue=M({...de,setup(i){const f=L(),l=j("1"),a=j([]),O=Math.random(),g=o=>{o.target.src=W},I=o=>o.logo&&o.logo.startsWith("data:image/")?o.logo:H(o.projectID,O),_=async(o,d=!0)=>{window.history.replaceState(null,"",window.location.pathname+"?id="+o.projectId),!window.__IS_DEMO_ADMINISTRATOR_&&d&&(D(),D("projectID")),window.__IS_DEMO_ADMINISTRATOR_&&await $(o.projectId),f.push({path:P})},T=async()=>{try{let o=await G();o&&(a.value=o.data)}catch(o){console.error(o)}};return N(()=>{h("projectID")&&h()&&!window.__IS_DEMO_ADMINISTRATOR_&&_({projectId:h("projectID")||""},!1)}),F(async()=>{await T(),a.value.length===1?_(a.value[0]):a.value.length<1&&f.replace("/no-permission")}),(o,d)=>{const w=u("el-radio-button"),C=u("el-radio-group"),R=u("el-button"),b=u("el-main");return n(),c("div",q,[z,s(b,{class:"own-main"},{default:r(()=>[s(C,{modelValue:l.value,"onUpdate:modelValue":d[0]||(d[0]=t=>l.value=t),class:"view-mode"},{default:r(()=>[s(w,{value:"1"},{default:r(()=>[s(A,{class:"radio-icon",name:"item-list"}),J]),_:1}),s(w,{value:"2"},{default:r(()=>[s(A,{class:"radio-icon",name:"item-card"}),Q]),_:1})]),_:1},8,["modelValue"]),e("div",X,[S(e("div",Y,[(n(!0),c(k,null,x(a.value,(t,m)=>(n(),c("div",{class:"list-item",key:m+"listitem"},[e("div",Z,[e("img",{src:I(t.content),alt:"LOGO",onError:g},null,40,ee),e("div",oe,[e("p",te,p(t.name),1),e("p",se,p(t.description),1)])]),e("div",ae,[s(R,{type:"primary",onClick:E=>_(t)},{default:r(()=>[U("\u8FDB\u5165")]),_:2},1032,["onClick"])])]))),128))],512),[[y,l.value==="1"]]),S(e("div",ne,[(n(!0),c(k,null,x(a.value,(t,m)=>(n(),c("div",{class:"card-item",key:m+"carditem",onClick:E=>_(t)},[e("img",{src:I(t.content),alt:"LOGO",onError:g},null,40,re),e("div",ie,[e("p",{class:"title",title:t.name},p(t.name),9,le),e("p",_e,p(t.description),1)])],8,ce))),128))],512),[[y,l.value==="2"]])])]),_:1})])}}});const fe=K(ue,[["__scopeId","data-v-92c13415"]]);export{fe as default};
