import{d as ae,f as x,r as c,h as i,o as m,B,w as d,a as oe,i as I,c as N,b as t,F as V,P as y,aA as ue,aK as te,ak as de}from"./main-dd669ad8.js";import{g as R,a as se}from"./product-327919f5.js";import{a as ne}from"./device-b598f94e.js";import{b as ie}from"./project-54c43e68.js";import{c as q}from"./cloneDeep-174273b5.js";import{_ as re}from"./_plugin-vue_export-helper-361d09b5.js";const ce={class:"dialog-footer"},me=ae({__name:"PointEdit",props:{groupMode:{default:"1"}},emits:["editPoint","customEditPoint"],setup(j,{expose:O,emit:z}){const K=j,k=(a,l)=>{const s=(g,p)=>(g=Math.ceil(g),p=Math.floor(p),Math.floor(Math.random()*(p-g+1))+g);let n=s(a,l),u=s(a,l),_=s(a,l);return"rgb("+n+","+u+","+_+")"},e=x({isShow:!0,color:k(0,255),className:"",classId:"",deviceName:"",deviceId:"",codeName:"",code:"",flag:!1,areaID:"",aliasCodeName:"",description:"",unit:""}),H=x({className:[{required:!0,message:"\u8BF7\u9009\u62E9\u8BBE\u5907\u539F\u578B",trigger:"change"}],areaID:[{required:!0,message:"\u8BF7\u9009\u62E9\u8BBE\u5907\u533A\u57DF",trigger:"change"}],deviceName:[{required:!0,message:"\u8BF7\u9009\u62E9\u8BBE\u5907\u540D\u79F0",trigger:"change"}],codeName:[{required:!0,message:"\u8BF7\u9009\u62E9\u8BBE\u5907\u70B9\u4F4D",trigger:"change"}]}),v=c(),F=c(!1),J={add:"\u6DFB\u52A0\u5C5E\u6027",edit:"\u7F16\u8F91\u5C5E\u6027",detail:"\u5C5E\u6027\u8BE6\u60C5"},r=c("add"),f=c(),D=c(0),Q=(a,l,s)=>{var n;F.value=!0,r.value=a,f.value=q(l),D.value=s!=null?s:((n=l==null?void 0:l.point)==null?void 0:n.length)||0,W(),X(),te(()=>{if(v.value&&v.value.resetFields(),r.value!=="add"){let u=q(l.point[D.value]);Object.assign(e,u),L({classID:u==null?void 0:u.classId,name:u==null?void 0:u.className}),M({id:u==null?void 0:u.deviceId,name:u==null?void 0:u.deviceName}),T({code:u==null?void 0:u.code,name:u==null?void 0:u.codeName})}})},w=c([]),P=c([]),U=c([]),W=async()=>{let a=[];try{let l=await R({classType:"product"});l&&(a=a.concat(l.data));let s=await R({classType:"point"});s&&(a=a.concat(s.data)),w.value=[...a]}catch{}},A=c([]),X=async()=>{try{let a=await ie();a.result&&a.result.resultCode==="0"&&(A.value=a.data&&a.data[0].childTree)}catch{}},L=a=>{e.classId=a.classID,e.className=a.name,e.deviceId&&e.deviceName&&(e.deviceId="",e.deviceName=""),S()},Y=()=>{e.code&&e.codeName&&(e.code="",e.codeName=""),e.deviceId&&e.deviceName&&(e.deviceId="",e.deviceName=""),Z(),S()},Z=async()=>{try{let a=await se([e.classId]);a.result&&a.result.resultCode==="0"&&(P.value=a.data[0].attribute)}catch{}},S=async()=>{var a;try{const l={classID:e.classId,areaId:(a=e.areaID)!=null&&a.length?e.areaID[e.areaID.length-1]:void 0,pageNum:1,pageSize:9999};let s=await ne(l,!0);s.result&&s.result.resultCode==="0"&&(U.value=s.data.data)}catch{}},M=a=>{e.deviceId=a.id,e.deviceName=a.name},T=a=>{e.code=a.code,e.codeName=a.name},E=()=>{Object.assign(e,{isShow:!0,color:k(0,255),className:"",classId:"",deviceName:"",deviceId:"",codeName:"",code:"",flag:!1,areaID:"",aliasCodeName:"",description:"",unit:""}),v.value&&v.value.clearValidate(),F.value=!1},$=()=>{v.value.validate(async(a,l)=>{var s;if(a){if((s=f.value.point)!=null&&s.length||(f.value.point=[]),f.value.point.some((u,_)=>u.deviceId===e.deviceId&&u.code===e.code?r.value==="edit"?_!=D.value:!0:!1)){de.warning("\u4E0D\u53EF\u914D\u7F6E\u76F8\u540C\u70B9\u4F4D\u4FE1\u606F");return}f.value.point[D.value]=e,z(K.groupMode==="1"?"editPoint":"customEditPoint",f.value,"point")}})};return O({openDialog:Q,handleCancel:E}),(a,l)=>{const s=i("el-switch"),n=i("el-form-item"),u=i("el-col"),_=i("el-color-picker"),g=i("el-row"),p=i("el-option"),C=i("el-select"),G=i("el-cascader"),b=i("el-input"),ee=i("el-form"),h=i("el-button"),le=i("el-dialog");return m(),B(le,{modelValue:F.value,"onUpdate:modelValue":l[11]||(l[11]=o=>F.value=o),title:J[r.value],onClose:E,"close-on-click-modal":!1,width:"600px",draggable:""},{footer:d(()=>[oe("span",ce,[r.value==="detail"?(m(),B(h,{key:0,type:"info",onClick:E},{default:d(()=>[I("\u5173\u95ED")]),_:1})):(m(),N(V,{key:1},[t(h,{type:"info",onClick:E},{default:d(()=>[I("\u53D6\u6D88")]),_:1}),t(h,{type:"primary",onClick:$},{default:d(()=>[I("\u786E\u5B9A")]),_:1})],64))])]),default:d(()=>[t(ee,{model:e,onSubmit:l[10]||(l[10]=ue(()=>{},["prevent"])),rules:H,ref_key:"formRef",ref:v,"label-width":"80px",disabled:r.value==="detail"},{default:d(()=>[t(g,{gutter:24},{default:d(()=>[t(u,{span:12},{default:d(()=>[t(n,{label:"\u7EBF\u6761\u53EF\u89C1"},{default:d(()=>[t(s,{modelValue:e.isShow,"onUpdate:modelValue":l[0]||(l[0]=o=>e.isShow=o)},null,8,["modelValue"])]),_:1})]),_:1}),t(u,{span:12},{default:d(()=>[t(n,{label:"\u7EBF\u6761\u989C\u8272"},{default:d(()=>[t(_,{modelValue:e.color,"onUpdate:modelValue":l[1]||(l[1]=o=>e.color=o),"color-format":"rgb"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),t(n,{label:"\u8BBE\u5907\u539F\u578B",prop:"className"},{default:d(()=>[t(C,{modelValue:e.className,"onUpdate:modelValue":l[2]||(l[2]=o=>e.className=o),placeholder:"\u8BF7\u9009\u62E9\u8BBE\u5907\u539F\u578B","value-key":"name",onChange:L,filterable:""},{default:d(()=>[(m(!0),N(V,null,y(w.value,o=>(m(),B(p,{key:o.classID,label:o.name,value:o},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(n,{label:"\u8BBE\u5907\u533A\u57DF",prop:"areaID"},{default:d(()=>[t(G,{options:A.value,modelValue:e.areaID,"onUpdate:modelValue":l[3]||(l[3]=o=>e.areaID=o),props:{value:"id",label:"name",children:"childTree",checkStrictly:!0},placeholder:"\u8BF7\u9009\u62E9\u8BBE\u5907\u533A\u57DF",onChange:Y,"popper-class":"area-popper"},null,8,["options","modelValue"])]),_:1}),t(n,{label:"\u8BBE\u5907\u540D\u79F0",prop:"deviceName"},{default:d(()=>[t(C,{modelValue:e.deviceName,"onUpdate:modelValue":l[4]||(l[4]=o=>e.deviceName=o),placeholder:"\u8BF7\u9009\u62E9\u8BBE\u5907\u540D\u79F0","value-key":"id",onChange:M,filterable:""},{default:d(()=>[(m(!0),N(V,null,y(U.value,o=>(m(),B(p,{key:o.id,label:o.name,value:o},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(n,{label:"\u8BBE\u5907\u7F16\u7801"},{default:d(()=>[t(b,{modelValue:e.deviceId,"onUpdate:modelValue":l[5]||(l[5]=o=>e.deviceId=o),disabled:""},null,8,["modelValue"])]),_:1}),t(n,{label:"\u8BBE\u5907\u70B9\u4F4D",prop:"codeName"},{default:d(()=>[t(C,{modelValue:e.codeName,"onUpdate:modelValue":l[6]||(l[6]=o=>e.codeName=o),placeholder:"\u8BF7\u9009\u62E9\u8BBE\u5907\u70B9\u4F4D","value-key":"code",onChange:T},{default:d(()=>[(m(!0),N(V,null,y(P.value,o=>(m(),B(p,{key:o.code,label:o.name,value:o},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(n,{label:"\u70B9\u4F4D\u522B\u540D"},{default:d(()=>[t(b,{modelValue:e.aliasCodeName,"onUpdate:modelValue":l[7]||(l[7]=o=>e.aliasCodeName=o),maxlength:64,placeholder:r.value!=="detail"?"\u8BF7\u8F93\u5165\u70B9\u4F4D\u522B\u540D":""},null,8,["modelValue","placeholder"])]),_:1}),t(n,{label:"\u63CF\u8FF0"},{default:d(()=>[t(b,{modelValue:e.description,"onUpdate:modelValue":l[8]||(l[8]=o=>e.description=o),maxlength:128,placeholder:r.value!=="detail"?"\u8BF7\u8F93\u5165\u63CF\u8FF0":""},null,8,["modelValue","placeholder"])]),_:1}),t(n,{label:"\u5355\u4F4D"},{default:d(()=>[t(b,{modelValue:e.unit,"onUpdate:modelValue":l[9]||(l[9]=o=>e.unit=o),maxlength:64,placeholder:r.value!=="detail"?"\u8BF7\u8F93\u5165\u5355\u4F4D":""},null,8,["modelValue","placeholder"])]),_:1})]),_:1},8,["model","rules","disabled"])]),_:1},8,["modelValue","title"])}}});const Fe=re(me,[["__scopeId","data-v-1b570537"]]);export{Fe as default};
