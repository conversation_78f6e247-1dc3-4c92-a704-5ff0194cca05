import{d as q,u as j,A as G,G as $,z as H,x as B,r as u,f as I,g as J,h as s,o as x,c as O,b as t,w as o,j as _,B as Q,y as W,a as X,i as E,k as Y,l as Z,F as ee,C as ae,D as te,E as oe,I as se,J as ne}from"./main-dd669ad8.js";import{T as re,K as le}from"./keepAlive-0c595e2b.js";import{C as ue}from"./crypto-5816c22c.js";import{_ as ce}from"./_plugin-vue_export-helper-361d09b5.js";const ie={class:"login-btn"},de=q({__name:"LoginForm",setup(pe){const A=j(),P=re(),S=le(),C=G(),h=$();let{id:K}=H();const f=B(()=>h.loginConfig),i=u(),N=I({username:[{required:!0,message:"\u8BF7\u8F93\u5165\u7528\u6237\u540D",trigger:"blur"}],password:[{required:!0,message:"\u8BF7\u8F93\u5165\u5BC6\u7801",trigger:"blur"}],captcha:[{required:!0,message:"\u8BF7\u8F93\u5165\u9A8C\u8BC1\u7801",trigger:"blur"}]}),d=u(!1),n=I({username:"",password:"",projectID:K}),g=u(""),D=B(()=>ne(g.value)),F=e=>{!e||e.validate(async a=>{if(!!a){d.value=!0;try{let r={...n},c=r.password;v.value&&(c=new ue(V.value).Encrypt(r.password,b.value)),f.value.captcha&&(r.captchaId=g.value),await ae({...r,password:c}),await C.getUserInfo(),C.setMenuHidden(),P.closeMultipleTab(),S.setKeepAliveName(),h.setLoginEnableVoice(),A.push("/")}catch{p()}finally{d.value=!1}}})},T=e=>{!e||(e.resetFields(),p())},v=u(!1),V=u(),b=u(""),U=async()=>{try{let e=await te();if(e){const a=e.data;v.value=a.isOpen===0,V.value=a.subValue}}catch{}},z=async()=>{try{let e=await oe();if(e){const a=e.data;b.value=a}}catch{}},p=async()=>{try{let e=await se();if(e){const a=e.data;g.value=a}}catch{}};return J(async()=>{await U(),v.value&&z(),f.value.captcha&&p(),document.onkeydown=e=>{if(e=window.event||e,e.code==="Enter"||e.code==="enter"||e.code==="NumpadEnter"){if(d.value)return;F(i.value)}}}),(e,a)=>{const r=s("user"),c=s("el-icon"),m=s("el-input"),w=s("el-form-item"),L=s("lock"),y=s("el-col"),M=s("el-image"),R=s("el-form"),k=s("el-button");return x(),O(ee,null,[t(R,{ref_key:"loginFormRef",ref:i,model:n,rules:N,size:"large"},{default:o(()=>[t(w,{prop:"username"},{default:o(()=>[t(m,{modelValue:n.username,"onUpdate:modelValue":a[0]||(a[0]=l=>n.username=l),placeholder:"\u7528\u6237\u540D"},{prefix:o(()=>[t(c,{class:"el-input__icon"},{default:o(()=>[t(r)]),_:1})]),_:1},8,["modelValue"])]),_:1}),t(w,{prop:"password"},{default:o(()=>[t(m,{type:"password",modelValue:n.password,"onUpdate:modelValue":a[1]||(a[1]=l=>n.password=l),placeholder:"\u5BC6\u7801","show-password":""},{prefix:o(()=>[t(c,{class:"el-input__icon"},{default:o(()=>[t(L)]),_:1})]),_:1},8,["modelValue"])]),_:1}),_(f).captcha?(x(),Q(w,{key:0,prop:"captcha"},{default:o(()=>[t(y,{span:10},{default:o(()=>[t(m,{modelValue:n.captcha,"onUpdate:modelValue":a[2]||(a[2]=l=>n.captcha=l),placeholder:"\u9A8C\u8BC1\u7801"},null,8,["modelValue"])]),_:1}),t(y,{span:2}),t(y,{span:12},{default:o(()=>[t(M,{src:_(D),style:{height:"48px",width:"auto",cursor:"pointer"},fit:"contain",onClick:p},null,8,["src"])]),_:1})]),_:1})):W("",!0)]),_:1},8,["model","rules"]),X("div",ie,[t(k,{icon:_(Y),round:"",onClick:a[3]||(a[3]=l=>T(i.value)),size:"large"},{default:o(()=>[E("\u91CD\u7F6E")]),_:1},8,["icon"]),t(k,{icon:_(Z),round:"",onClick:a[4]||(a[4]=l=>F(i.value)),size:"large",type:"primary",loading:d.value},{default:o(()=>[E(" \u767B\u5F55 ")]),_:1},8,["icon","loading"])])],64)}}});const ve=ce(de,[["__scopeId","data-v-a2cc5b2e"]]);export{ve as default};
