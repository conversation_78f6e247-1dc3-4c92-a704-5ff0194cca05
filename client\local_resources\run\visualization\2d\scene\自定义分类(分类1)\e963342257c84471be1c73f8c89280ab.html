<!DOCTYPE html>
<html style="height:100%;">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <link rel="icon" href="/supaiot/static/favicon.ico">
    <script src="/supaiot/static/js/jquery.min.js"></script>
    <script src="/supaiot/static/js/react.min.js"></script>
    <script src="/supaiot/static/js/react-dom.min.js"></script>
    <script src="/supaiot/static/js/JSXTransformer.js"></script>
    <script src="/supaiot/static/js/postdata.js"></script>
    <script src="/supaiot/static/js/deviceevent.js"></script>
    <script src="/supaiot/static/js/devicemanage.js"></script>
    <script src="/supaiot/static/js/devicedata.js"></script>
    <script src="/supaiot/static/js/controller.js"></script>
    <script src="/supaiot/static/js/echarts.min.js"></script>
    <script src="/supaiot/static/js/echarts-liquidfill.min.js"></script>
    <link href="/supaiot/static/css/symbolanimation.css" rel="stylesheet">
    <link href="/supaiot/static/css/element.css" rel="stylesheet">
    <style>
        .ecityosbutton {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100%;
            height: 100%;
        }

        body {
            height: 100%;
            margin: 0px;
            overflow: hidden;
            user-select: none;
        }

        #svg_div {
            visibility: hidden;
            width: 0px;
            height: 0px;
            display: flex;
        }

        #popup_devpan,
        #popup_frame {
            display: none;
            position: absolute;
            width: 200px;
            height: 240px;
            z-index: 48;
        }
        #popup_frame {
            background-color: white;
        }

        #popup_devpan div.header,
        #popup_frame div.header {
            position: absolute;
            background-color: #0a3c80;
            width: 100%;
            height: 40px;
            opacity: 1;
        }

        #popup_devpan div.close_btn,
        #popup_frame div.close_btn {
            width: 40px;
            height: 40px;
            position: absolute;
            top: 0;
            right: 0;
            cursor: pointer;
            background-color: #43ACF3;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        #popup_devpan iframe,
        #popup_frame iframe {
            position: absolute;
            top: 40px;
            width: 100%;
            height: 200px;
        }
    </style>
</head>

<body>
    <div id="svg_div"></div>
    <div id="popup_devpan">
        <div class="header"></div>
        <div class="close_btn">
            <img src="/supaiot/static/img/closeBtn_white.png" />
        </div>
        <iframe id="popup_devpan_ifr" src="about:blank" frameborder="no" border="0" marginwidth="0" marginheight="0"
            scrolling="no" allowtransparency="yes"></iframe>
        <script>
            $('#popup_devpan div.close_btn').mouseup(function (_ed) {
                $('#popup_devpan').css('display', 'none')
            })

            $('#popup_devpan div:first').mousedown(function (ed) {
                let old_x = parseInt($('#popup_devpan').css('left'))
                let old_y = parseInt($('#popup_devpan').css('top'))

                let ndiv = document.createElement('div')
                ndiv.setAttribute('id', '__temp_move_ndiv_1__')
                ndiv.style = 'opacity:0;position:absolute;top:0px;left:0px;width:' + document.body.clientWidth +
                    'px;height:' + document.body.clientHeight + 'px;z-index:' + (parseInt($('#popup_devpan')
                        .css('z-index')) + 1)
                document.body.appendChild(ndiv)

                let xdiv = document.createElement('div')
                xdiv.setAttribute('id', '__temp_move_xdiv_1__')
                xdiv.style = 'background-color:black;opacity:0.2;position:absolute;top:0px;left:0px;width:' +
                    document.body.clientWidth + 'px;height:' + document.body.clientHeight + 'px;z-index:' + (
                        parseInt($('#popup_devpan').css('z-index')) - 1)
                document.body.appendChild(xdiv)

                $(ndiv).mouseup(function (eu) {
                    $(ndiv).remove()
                    $(xdiv).remove()
                })

                $(ndiv).mousemove(function (em) {
                    $('#popup_devpan').css('left', old_x + em.pageX - ed.pageX)
                    $('#popup_devpan').css('top', old_y + em.pageY - ed.pageY)
                })
            })
        </script>
    </div>
    <div id="popup_frame">
        <div class="header"></div>
        <div class="close_btn">
            <img src="/supaiot/static/img/closeBtn_white.png" />
        </div>
        <iframe id="popup_frame_ifr" src="about:blank" frameborder="no" border="0" marginwidth="0" marginheight="0"
            scrolling="no" allowtransparency="yes"></iframe>
        <script>
            $('#popup_frame div.close_btn').mouseup(function (_ed) {
                $('#popup_frame').css('display', 'none');
                $('#popup_frame iframe:first').attr('src', '')
            })

            $('#popup_frame div:first').mousedown(function (ed) {
                let old_x = parseInt($('#popup_frame').css('left'))
                let old_y = parseInt($('#popup_frame').css('top'))

                let ndiv = document.createElement('div')
                ndiv.setAttribute('id', '__temp_move_ndiv_1__')
                ndiv.style = 'opacity:0;position:absolute;top:0px;left:0px;width:' + document.body.clientWidth +
                    'px;height:' + document.body.clientHeight + 'px;z-index:' + (parseInt($('#popup_frame')
                        .css('z-index')) + 1)
                document.body.appendChild(ndiv)

                let xdiv = document.createElement('div')
                xdiv.setAttribute('id', '__temp_move_xdiv_1__')
                xdiv.style = 'background-color:black;opacity:0.2;position:absolute;top:0px;left:0px;width:' +
                    document.body.clientWidth + 'px;height:' + document.body.clientHeight + 'px;z-index:' + (
                        parseInt($('#popup_frame').css('z-index')) - 1)
                document.body.appendChild(xdiv)

                $(ndiv).mouseup(function (eu) {
                    $(ndiv).remove()
                    $(xdiv).remove()
                })

                $(ndiv).mousemove(function (em) {
                    $('#popup_frame').css('left', old_x + em.pageX - ed.pageX)
                    $('#popup_frame').css('top', old_y + em.pageY - ed.pageY)
                })
            })
        </script>
    </div>
    <svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg" xmlns:svg="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" ecityosbackgroundcolor="" viewBox="0 0 1920 1080" id="symbolshow_real">
 <defs>
  
 </defs>
 
 
 
 
<g class="svg-pan-zoom_viewport"><rect fill="#ff0000" fill-opacity="0" height="1080" stroke="#000000" stroke-width="0" width="1920" x="0" y="0"></rect><g class="layer" layertitle="背景层">
  
 </g><g class="layer" layertitle="设备层">
  
 </g><g class="layer" layertitle="数据层">
  
 </g><g class="layer" layertitle="组件层">
  
  <g id="component_visual_device0" ecityoscomponenttype="device" transform="translate(318 224)"></g>
  <g id="component_visual_device1" ecityoscomponenttype="device" transform="translate(505 220)"></g>
  <g id="component_visual_device2" ecityoscomponenttype="device" transform="translate(690 219)"></g>
  <g id="component_visual_statistics3" ecityoscomponenttype="statistics"></g>
 </g></g></svg>
    <script src="/supaiot/static/js/svg-pan-zoom.js"></script>
    <script src="/supaiot/static/js/pagezoom.js"></script>
    <script src="/supaiot/static/js/scenehandle.js"></script>
    <script type="text/jsx" src="/supaiot/static/js/message-box.jsx"></script>
    <script type="text/jsx">
class _visualization_2d_symbol_sAbqduscW5zrfanf_图形1 extends React.Component{
        constructor(props){
            super(props);
            this.state = {
                state:{},
                quality:{},
                updatetime:{},
                config:{
                    information:{}
                },
                userChanged:false,
                
            };
        }
        componentDidMount(){
            attchReactObject(this);
            this.loadStyles();
        }
        componentWillUnmount(){
            dispatchReactObject(this);
        }
        componentDidUpdate(prevProps, prevState) {
            if(!this.state.userChanged){
                let newState = {};
                if(Object.keys(this.state.state).length>0){
                    newState.userChanged = true;
                }
                
                Object.keys(newState).length && this.setState(newState)
            }
        }
        loadStyles() {
            const styleString = ``;

            if(styleString == ""){ return; }
            const styleElement = document.createElement('style');
            if (styleElement.styleSheet) {
               styleElement.styleSheet.cssText = styleString;
            } else {
               styleElement.appendChild(document.createTextNode(styleString));
            }
            document.head.appendChild(styleElement);
        }
        condition( state,  updatetime, quality, config, flaglist ) {
            try{if(state["DI1"] == false){flaglist[0] = true;}
else if(state["DI1"] == true){flaglist[1] = true;}
else{}} catch(e){ console.error(e); }

        }
        handleChange(stateKey,event) {
            this.setState({
                [stateKey]: event.target.value,
            })
        }
        buttonFunc(stateKey,control,input_data,timeOut) {
            let id = this.props.deviceid;
            if(!id){return}
            let json = {};
            json.id = id;
            json.control = control;
            json.value = input_data;

            console.log("json  ",json);
            this.setState({
                [stateKey + "Disabled"]:true
            });
            if(timeOut){
                setTimeout(()=>{
                   this.setState({
                       [stateKey + "Disabled"]:false
                   });
                },timeOut)
            }
            subfetch(json,"",()=> {
                this.setState({
                    [stateKey + "Disabled"]: false, // 解除 select 禁用
                });
            });
        }
        buttonClick(stateKey,control,input_data,config,event){
            if(control == ""){
                return;
            }
            let confirm = config.confirm,
                msg = config.confirmMsg,
                timeout = config.timeout;
            if(confirm){
                Elconfirm(msg,"提示").then(()=>{
                    this.buttonFunc(stateKey,control,input_data,timeout)
                }).catch(()=>{console.log("canel")})
            }else{
                this.buttonFunc(stateKey,control,input_data,timeout)
            }
        }
        processData(value,mapping,flag){
            if (value === undefined || value === null) {
                return "--";
            }
            if(typeof value === 'object'){
                try{
                    return JSON.stringify(value)
                } catch(e){
                    return value + ''
                }
            }else{
                if(flag && typeof value === 'boolean'){
                    return mapping[value ? "ON" : "OFF"]
                }else{
                    return mapping[value] || (value + '')
                }
            }
        }
        render(){
            let flaglist = [];
            flaglist[0] = false;
flaglist[1] = false;

            this.condition(this.state.state, this.state.updatetime, this.state.quality, this.state.config, flaglist);
            let temp = [];
            temp.push(React.createElement("use",{style:{visibility:flaglist[0]?"visible":"hidden"},xlinkHref:"#20405785c0346478bbe26712d22f5c2d__symbol_sup__graph4",key:"状态graph4"}));
temp.push(React.createElement("use",{style:{visibility:flaglist[1]?"visible":"hidden"},xlinkHref:"#20405785c0346478bbe26712d22f5c2d__symbol_sup__graph5",key:"状态graph5"}));

            return React.createElement("g", null, temp);
        }
    }
</script><script type="text/jsx">
class _component_visual_statistics3 extends React.Component{
        constructor(props){
            super(props);
            this.state = {
                sValue:"--",
                isFetching:false
            };
            this.handleCustomQuery = this.handleCustomQuery.bind(this);
        }
        componentDidMount(){
            attchReactObject(this);
        }
        componentWillUnmount(){
            dispatchReactObject(this);
        }
        getParams() {
            const option = {"advancedModel":false,"whereClause":"","logic":"and","items":[{"field":"area_id","operator":"=","value":[]}]};
            if(option.advancedModel){
                return {advancedModel:true,whereClause:option.whereClause};
            }else{
                const result = {condition:{logic:option.logic,items:[]}};
                let items = option.items;
                const setItems = (arr) =>{
                    arr.forEach(v=> {
                        //区域id取最末的选中项
                        if(v.field === "area_id"){
                            v.value = v.value.pop()
                        }
                        //label重新设置格式
                        if(v.field === "device_label" || v.field === "class_label"){
                            let value = v.value;
                            if (value.length > 0){
                                v.value = {[value[0]]:value[1]}
                            }else{
                                v.value = {}
                            }
                        }
                        if(v.hasOwnProperty('items')){
                            setItems(v.items)
                        }
                    })
                }
                setItems(items)
                result.condition.items = items;
                return result;
            }
        }
        handleCustomQuery() {
            let params = this.getParams();
            let url = getUrlByTag("statistic");
            let _this = this;
            this.setState({isFetching:true})
            $.ajax({
               type: "POST",
               url: url,
               contentType:"application/json",
               data: JSON.stringify(params),
               success: function(data, status, request) {
                  if(data.result && data.result.resultCode == "0"){
                    const value = data.data.count;
                    _this.setState({sValue:value})
                  }
               },
               error: function(data) {
                  console.log(data)
               }
            }).always(()=>{
                this.setState({isFetching:false})
            });
        }
        render(){
            let beforeSvg = document.getElementById("graph1");
            let texttag = Array.from(beforeSvg.children).find(el=> el.tagName.toLowerCase() == 'text');

            let data = this.state.sValue;

            texttag.innerHTML = data;

            let temp = [];
            temp.push(
              React.createElement("use", {
                xlinkHref: "#graph1"
              })
            );
            return React.createElement("g", {  }, temp);
        }
    }
</script>
    
    <script type="text/jsx">
	class QueryScheduler {
		constructor(interval = 10000) {
			this.interval = interval;
			this.instances = new Set(); // 存储组件实例
			this.timer = null;
			this.bindWindowUnload()
		}
		// 绑定 window 事件 (浏览器关闭或刷新)
		bindWindowUnload() {
			window.addEventListener('beforeunload', () => {
			  this.clearAll(); // 强制清理所有资源
			},{once:true});
			// 兼容性处理（部分浏览器可能仅触发 unload）
			window.addEventListener('unload', () => {
			  this.clearAll();
			});
		}
		clearAll() {
			this.instances.clear();
			this.clearTimer(); // 取消定时器
		}
		// 注册组件实例
		register(instance) {
			this.instances.add(instance);
			instance.executeQuery();
			this.startTimerIfNeeded();
		}
		// 移除组件实例
		unregister(instance) {
			this.instances.delete(instance);
			if (this.instances.size === 0) {
				this.clearTimer();
			}
		}
		// 启动定时器（按需）
		startTimerIfNeeded() {
			if (!this.timer && this.instances.size > 0) {
				this.timer = setInterval(() => {
					this.instances.forEach(instance => {
						instance.executeQuery(); // 调取每个实例的查询方法
					});
				}, this.interval);
			}
		}
		// 清理定时器
		clearTimer() {
			if (this.timer) {
				clearInterval(this.timer);
				this.timer = null;
			}
		}
	}
	var queryScheduler = new QueryScheduler();
	var withQueryScheduler = (WrappedComponent) => {
		let HOC = class extends React.Component {
			constructor(props) {
				  super(props);
				  this.executeQuery = this.executeQuery.bind(this);
				  this.setWrappedRef = this.setWrappedRef.bind(this);
			}
			// 组件内部查询方法 (需被包装组件实现)
			executeQuery(){
				// 此处调用具体组件的实际查询逻辑
				if (this.wrappedInstance && this.wrappedInstance.handleCustomQuery && !this.wrappedInstance.state.isFetching) {
					this.wrappedInstance.handleCustomQuery();
				}
			}
			// 注册到全局管理器
			componentDidMount() {
				queryScheduler.register(this);
			}
			// 清理注销
			componentWillUnmount() {
				queryScheduler.unregister(this);
			}
			// 传递ref以获取真实组件实例
			setWrappedRef(ref){
				this.wrappedInstance = ref;
			}
			render() {
				return (
					<WrappedComponent
						ref={this.setWrappedRef}
						{...this.props}
					/>
				);
			}
		};
		return HOC;
	};		
	</script><script type="text/jsx">
	function getGroupLevel(){
		let authorityGroup = [],authorityLevel = 0;
        let urlGroup = getQueryVariable("authorityGroup"),
            urlLevel = getQueryVariable("authorityLevel");
        if(urlGroup){
			authorityGroup = urlGroup.toLowerCase().split(".");
		}
        if(urlLevel){
			if(Number(urlLevel) != NaN){
				authorityLevel = Number(urlLevel);
			}else{
				authorityLevel = 99999;
			}
		}
		return {authorityGroup,authorityLevel}
	}
	function getAuthBoolean(group,level){
        let result = true,permission = true;
		let data = getGroupLevel();
        let urlGroup = data.authorityGroup,
            urlLevel = data.authorityLevel;
        if(urlGroup.length > 0){
			permission = group.some(p=>urlGroup.includes(p.toLowerCase()));
		}
        if(permission){
            if(urlLevel <= level){
                result = true;
            }else{
                result = false;
            }
        }else{
            result = false;
        }
        return result;
    }
    function showNotAuthMessage(){
        let showWarningStr = "当前用户权限不足";
        let divDom = $('<div role="alert" class="el-message el-message--warning" style="top: 20px; z-index: 2000;"><p class="el-message__content">' + showWarningStr + '</p></div>');
        $("body").append(divDom);
        setTimeout(()=>{
            divDom.remove();
        },3000);  
    }
	var layerAuth = {};
	$('g[layertitle].layer').each(function() {
		var authorityGroup = $(this).attr('layer-authority-group');
		var authorityLevel = $(this).attr('layer-authority-level');
		var name = $(this).attr('layertitle');
		var layerdata = {};
		if(authorityGroup){
			layerdata.authorityGroup = authorityGroup.split(".");
		}else{
			layerdata.authorityGroup = [];
        }
        if(authorityLevel){
            layerdata.authorityLevel = Number(authorityLevel);
        }else{
            layerdata.authorityLevel = 999;
        }
        layerAuth[name] = layerdata;
	});
    for(let i in layerAuth){
        let data = layerAuth[i];
        let weight = getAuthBoolean(data.authorityGroup,data.authorityLevel);
        if(!weight){sceneHandle.showLayer(i,false)}
    }</script><script type="text/jsx">function appendComponentSvg() {
		let svgdatalist = [`<svg id="20405785c0346478bbe26712d22f5c2dsymbolshow_root" xmlns="http://www.w3.org/2000/svg" xmlns:svg="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" style="overflow:visible;visibility:visible;" width="111.21151733398438" height="112.71150207519531" ecityosbackgroundcolor="#FFF" ecityosname="图形1" viewBox="0 0 111.21151733398438 112.71150207519531"><defs></defs><g id="20405785c0346478bbe26712d22f5c2dsymbol_sup" ecityosname="图形1"><title>图形1</title><g id="20405785c0346478bbe26712d22f5c2d__symbol_sup__graph4" ecityosgroup="yes" customid="20405785c0346478bbe26712d22f5c2d__symbol_sup__graph4" transform="translate(-276.3943 -105.8942)" xmlns="http://www.w3.org/2000/svg"><desc>状态graph4</desc><circle fill="#FF0000" stroke="#000000" stroke-width="5" cx="332" cy="163" r="55.60575485229492" id="20405785c0346478bbe26712d22f5c2d__symbol_sup__graph1" customid="20405785c0346478bbe26712d22f5c2d__symbol_sup__graph1" fill-opacity="1" stroke-opacity="1"></circle></g><g id="20405785c0346478bbe26712d22f5c2d__symbol_sup__graph5" ecityosgroup="yes" customid="20405785c0346478bbe26712d22f5c2d__symbol_sup__graph5" fill-opacity="1" transform="translate(-276.3943 -105.8942)" xmlns="http://www.w3.org/2000/svg"><desc>状态graph5</desc><circle fill="#00ff7f" stroke="#000000" stroke-width="5" cx="332" cy="161.50000381469727" r="55.60575485229492" id="20405785c0346478bbe26712d22f5c2d__symbol_sup__graph3" customid="20405785c0346478bbe26712d22f5c2d__symbol_sup__graph3" fill-opacity="1" stroke-opacity="1"></circle></g></g></svg>`,
`<svg xmlns="http://www.w3.org/2000/svg" xmlns:svg="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" style="overflow:visible;visibility:hidden;"><g customid="graph1" ecityosgrouptype="statistics" id="graph1">
   <text customid="graph2" fill="#000000" font-family="serif" font-size="24" id="graph2" text-anchor="end" x="126" xml:space="preserve" y="421">###</text>
   <ecityosdata>%7B%22config%22%3A%7B%22fontFamily%22%3A%22serif%22%2C%22fontSize%22%3A24%2C%22color%22%3A%22%23000000%22%7D%2C%22option%22%3A%7B%22advancedModel%22%3Afalse%2C%22whereClause%22%3A%22%22%2C%22logic%22%3A%22and%22%2C%22items%22%3A%5B%7B%22field%22%3A%22area_id%22%2C%22operator%22%3A%22%3D%22%2C%22value%22%3A%5B%5D%7D%5D%7D%7D</ecityosdata>
  </g></svg>`,
];
for (let i = 0; i < svgdatalist.length; i++) {
			var o = document.createElement("div");
			o.innerHTML = svgdatalist[i];
			o.childNodes[0].style = "visibility:hidden;";
			document.getElementById("svg_div").append(o.childNodes[0]);
		}
	}
	appendComponentSvg();</script><script type="text/jsx">function run() {
        var symbolslist = [{"id":"component_visual_device0","type":"device","symbol":_visualization_2d_symbol_sAbqduscW5zrfanf_图形1,"panel":{"style":{"fitContent":true,"height":563,"popupMode":"center","width":450},"template":[{"enable":true,"value":"device-panel"}]},"config":{},"deviceid":"XcxwQJjPxAWqhE3u",},
{"id":"component_visual_device1","type":"device","symbol":_visualization_2d_symbol_sAbqduscW5zrfanf_图形1,"panel":{"style":{"fitContent":true,"height":563,"popupMode":"center","width":450},"template":[{"enable":true,"value":"device-panel"}]},"config":{},"deviceid":"Cl9U8qFVdnyXnLi4",},
{"id":"component_visual_device2","type":"device","symbol":_visualization_2d_symbol_sAbqduscW5zrfanf_图形1,"panel":{"style":{"fitContent":true,"height":563,"popupMode":"center","width":450},"template":[{"enable":true,"value":"device-panel"}]},"config":{},"deviceid":"H8UEWuwjMShpf5zc",},
{"id":"component_visual_statistics3","type":"statistics","symbol":_component_visual_statistics3,},
];
var deleteDevices =[];
var eventparamlist = [];
        let symbol, protos, id;
        for (let i = 0; i < symbolslist.length; i++) {
            symbol = symbolslist[i]["symbol"];
            if (!symbol) continue;
            protos = {};
            protos["type"] = symbolslist[i]["type"];
            protos["deviceid"] = symbolslist[i]["deviceid"];
            protos["clickMsg"] = symbolslist[i]["clickMsg"];
            symbolslist[i]["panel"] && (protos["panel"] = symbolslist[i]["panel"]);
			symbolslist[i]["config"] && (protos["config"] = symbolslist[i]["config"]);
            id = symbolslist[i]["id"];
			if(["statistics"].includes(protos["type"])){
				symbol = withQueryScheduler(symbol)
			}
            try {
				let comDom = document.getElementById(id)
                symbolslist[i].reactDom = ReactDOM.render(React.createElement(symbol, protos), comDom)
				if(protos["deviceid"] && deleteDevices.includes(protos["deviceid"])){
					addDeleteTip(comDom,protos["deviceid"],symbolslist[i])
				}
				if(protos["type"] === "pipeline"){
					let devs = pipedeviceArr[protos["deviceid"]];
					devs = new Set(devs);
					let isfind = deleteDevices.find(v=>devs.has(v));
					if(isfind){
						addDeleteTip(comDom,isfind,symbolslist[i])
					}
				}
            } catch (err) { console.log(err) }
        }
        sceneHandle && sceneHandle.setSymbols(symbolslist);
        window.addEventListener("load", function () {
            SEventMgr.paintButton(eventparamlist);
        })
		function addDeleteTip(comDom,deviceid,component){
			comDom.setAttribute("device-delete",true)
			component.reactDom.props["device-delete"] = true
			let showWarningStr = "设备" + deviceid + "已删除，异常组件已标识！";
			let divDom = $('<div role="alert" class="el-message el-message--warning" style="top: 20px; z-index: 2000;"><p class="el-message__content">'+showWarningStr+'</p></div>');
			$("body").append(divDom);
			setTimeout(()=>{
				divDom.remove();
			},10000);
			var s_root = ReactDOM.findDOMNode(component.reactDom);
			var bbox = transformedBoundingBox(s_root);	
			var o = document.createElementNS("http://www.w3.org/2000/svg", "rect");
			o.setAttribute("x", bbox.x-5);
			o.setAttribute("y", bbox.y-5);
			o.setAttribute("width", bbox.width+10);
			o.setAttribute("height", bbox.height+10);
			o.setAttribute("stroke","#FF0000");
			o.setAttribute("stroke-width","2px");
			o.setAttribute("fill","rgb(255 0 0 / 30%)");
			o.setAttribute("opacity", '1');
			o.setAttribute("stroke-dasharray", '3,3');
			o.setAttribute("vector-effect", 'non-scaling-stroke');
			s_root.append(o);
		}
    }
let devicelist = ["XcxwQJjPxAWqhE3u","Cl9U8qFVdnyXnLi4","H8UEWuwjMShpf5zc",];
    devicelist = [...new Set(devicelist)];
    
    for (let i = 0; i < devicelist.length; i++) {
        SSimulateData.addPointlist(devicelist[i]);
    }
    let pipedeviceArr = {};
    let pipeObj = {};
    for(let i in pipedeviceArr){
        pipedeviceArr[i] = [...new Set(pipedeviceArr[i])];
        for(let j = 0;j<pipedeviceArr[i].length;j++){
            if(pipeObj[pipedeviceArr[i][j]]){
                pipeObj[pipedeviceArr[i][j]].push(i);
            }else{
                pipeObj[pipedeviceArr[i][j]] = [i];
            }
        }
    }
    SSimulateData.addPipeArr(pipeObj);
	run();
    SSimulateData.interval();
	</script>
</body>

</html>dPipeArr(pipeObj);
	run();
    SSimulateData.interval();
	</script>
</body>

</html>