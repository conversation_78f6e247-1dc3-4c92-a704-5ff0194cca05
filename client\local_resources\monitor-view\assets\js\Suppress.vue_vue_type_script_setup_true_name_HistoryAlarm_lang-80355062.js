import{_ as f}from"./index.vue_vue_type_script_setup_true_name_ProTable_lang-ce2538eb.js";import{d as o,r,A as D,h as F,o as d,c as b,b as s,w as l,i as g,t as A,j as h,aj as E,ak as n}from"./main-dd669ad8.js";import{h as B,u as C}from"./monitor-1739c0cb.js";const I={class:"suppress-alarm table-box"},x=o({name:"HistoryAlarm"}),L=o({...x,setup(y,{expose:i}){const u=r(),p=r({});D();const c=[{type:"index",label:"\u5E8F\u53F7",width:60},{prop:"ID",label:"\u8BBE\u5907\u7F16\u7801",align:"left"},{prop:"name",label:"\u8BBE\u5907\u540D\u79F0",align:"left"},{prop:"stateName",label:"\u8BBE\u5907\u70B9\u4F4D",align:"left"},{prop:"type",label:"\u62A5\u8B66\u7C7B\u578B",align:"left",width:200,customFormat(e){switch(e){case"AI_H":return"\u9AD8\u9650";case"AI_HH":return"\u9AD8\u9AD8\u9650";case"AI_L":return"\u4F4E\u9650";case"AI_LL":return"\u4F4E\u4F4E\u9650";case"AI_CR":return"\u53D8\u5316\u7387";case"AI_DA":return"\u504F\u5DEE";case"DI_ST":case"EI_ST":return"\u72B6\u6001";case"DI_ON2OFF":case"DI_OFF2ON":return"\u53D8\u4F4D";default:return e}}},{prop:"value",label:"\u503C",align:"left",width:100},{prop:"validTime",label:"\u6291\u5236\u6709\u6548\u671F",align:"left",customFormat(e){return e===-1?"\u65E0\u9650\u671F":E(e*1e3)},width:180},{prop:"comment",label:"\u6291\u5236\u8BF4\u660E",align:"left"},{prop:"operation",label:"\u64CD\u4F5C",fixed:"right"}],m=async e=>{const a={ID:e.ID,state:e.state,type:e.type,value:e.value};let t=await C(a);t.result&&t.result.resultCode==="0"?(u.value.getTableList(),n.success("\u64CD\u4F5C\u6210\u529F")):n.error(t.result.resultError)};return i({getList:()=>{u.value.getTableList()}}),(e,a)=>{const t=F("el-button");return d(),b("div",I,[s(f,{ref_key:"proTable",ref:u,"highlight-current-row":"",columns:c,border:!1,pagination:!1,searchCol:{xs:1,sm:2,md:2,lg:3,xl:5},"select-id":"index","init-param":p.value,requestApi:h(B)},{operation:l(_=>[s(t,{type:"primary",link:"",onClick:v=>m(_.row)},{default:l(()=>[g(A("\u53D6\u6D88\u6291\u5236"))]),_:2},1032,["onClick"])]),_:1},8,["init-param","requestApi"])])}}});export{L as _};
