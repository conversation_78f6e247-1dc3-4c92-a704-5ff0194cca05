import t from"./LoginForm-d3421baa.js";import{d as s,o as _,c as a,a as e,b as c,p as i,e as n,t as d}from"./main-dd669ad8.js";import{_ as r}from"./_plugin-vue_export-helper-361d09b5.js";import"./keepAlive-0c595e2b.js";import"./crypto-5816c22c.js";const p=o=>(i("data-v-e43453e9"),o=o(),n(),o),l={class:"login-container flx-center",id:"login_bg"},m={class:"login-box"},g={class:"login-form"},u=p(()=>e("div",{class:"login-logo"},[e("h2",{class:"logo-text isDark"},d("\u76D1\u63A7\u573A\u666F"))],-1)),f=s({name:"login"}),x=s({...f,setup(o){return(h,v)=>(_(),a("div",l,[e("div",m,[e("div",g,[u,c(t)])])]))}});const B=r(x,[["__scopeId","data-v-e43453e9"]]);export{B as default};
