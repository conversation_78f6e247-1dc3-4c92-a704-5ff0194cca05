import{d as Se,aJ as Ue,r as h,x as ze,am as Te,f as Ee,h as p,o as u,c as D,b as n,w as s,a as i,t as c,j as d,aA as qe,F as $,P as U,y as S,i as g,ao as O,B as y,aK as Fe,ak as Re,p as Be,e as Le}from"./main-dd669ad8.js";import{u as P,_ as J}from"./useSelection-ccce70fc.js";import{_ as x}from"./index.vue_vue_type_script_setup_true_name_SvgIcon_lang-c82a9164.js";import{f as je}from"./serviceDict-f46c4ded.js";import{g as G}from"./product-327919f5.js";import{a as He}from"./control-95804351.js";import{_ as Me}from"./RelationDialog.vue_vue_type_style_index_0_lang-965e03ce.js";import Ke from"./EditActionDialog-55b8c6cf.js";import{_ as Ne}from"./_plugin-vue_export-helper-361d09b5.js";import"./device-b598f94e.js";import"./index.vue_vue_type_script_setup_true_name_ProTable_lang-ce2538eb.js";const Q=V=>(Be("data-v-f663658f"),V=V(),Le(),V),Oe={class:"header-title"},Pe={key:0,class:"config-box"},xe={class:"strategy-box"},Ge={class:"header"},Je={class:"title"},Qe={style:{color:"var(--el-text-color-regular)"}},We={style:{color:"var(--el-text-color-regular)"}},Xe={style:{color:"var(--el-text-color-regular)"}},Ye={class:"device-box"},Ze={class:"header"},eo={class:"title"},oo={class:"defineBtn"},to={class:"table-empty"},lo=Q(()=>i("img",{src:J,alt:"notData"},null,-1)),ao={key:1,class:"actiontable-box"},no={class:"defineBtn"},so={class:"table-empty"},io=Q(()=>i("img",{src:J,alt:"notData"},null,-1)),ro={class:"footer-btns"},co=Se({__name:"AddActionDrawer",props:{deviceClass:null,pointFilter:{type:Function},valueFilter:{type:Function}},emits:["editHandle"],setup(V,{expose:W,emit:X}){const m=V,{t:f}=Ue(),z=h(!1),Y=ze(()=>f("views.control.group.components.AddActionDrawer.tianjiadongzuo")),L=Te("setDeviceClassMap"),A=h("config"),j=h(),H=h(),M=h(),_=h([{point:"",pointValues:[],value:"",classID:"",attributes:[],timeout:5,deviceID:[],fail_proc:0,pointType:"",retry_count:0,finished_wait:5}]),T=h(""),I=h([]),E=h([]);let q=Ee([]);const Z=async(e,o)=>{m.deviceClass[e]||await L([e],[]),o.attributes=m.deviceClass[e].attribute.filter(t=>["control","stateControl","calculation"].includes(t.pointType))},ee=(e,o)=>{o.value="";let t=o.attributes.find(r=>r.code==e);if(t&&(o.pointType=t.type,t.type.includes("D")||t.type.includes("E"))){o.pointValues=[];for(let r in t.value)o.pointValues.push({label:t.value[r],value:r+""})}},oe=(e,o,t)=>{/^-?\d+(\.\d+)?$/.test(o)?t():t(new Error(f("views.control.group.components.AddActionDrawer.shuzhifeifa")))},F=e=>{let o=e.attributes.find(t=>t.code==e.point);return o?o.type==="AO"||o.type==="AI"||o.type==="SO"||o.type==="SI"||o.type==="AIO"||o.type==="SIO":!0},te=(e,o)=>{o.value=e.replace(/[^\-^\.^\d]/g,"").replace(/^\..*/g,"").replace(new RegExp("(?<=^\\-)\\D.*","g"),"").replace(new RegExp("(?<=\\..*)[\\.].*","g"),"").replace(new RegExp("(?<=(^0|^\\-0))[^\\.].*","g"),"").replace(new RegExp("(?<=(^0\\.\\d*|^\\-0\\.\\d*))[^\\d].*","g"),"").replace(new RegExp("(?<=(\\d|\\.))[\\-].*","g"),"")},le=e=>{_.value.splice(e,1)},ae=()=>{_.value.push({point:"",pointValues:[],value:"",classID:"",attributes:[],timeout:5,deviceID:[],fail_proc:0,pointType:"",retry_count:0,finished_wait:5})},{selectionChange:ne,getRowKeys:se,selectedListIds:ie,isSelected:re}=P("deviceID"),{selectionChange:ce,getRowKeys:ue,selectedListIds:de,isSelected:pe}=P(),fe=(e,o)=>{var t;if(!o.classID){Re.warning(f("views.control.group.components.AddActionDrawer.shebeimoxingweixuanze"));return}(t=j.value)==null||t.openDialog(e,o.classID)},ve=async e=>{let o=[];e.data.forEach(r=>{m.deviceClass[r]||o.push(r)}),await L([],o);let t=[];e.data.forEach(r=>{_.value[e.index].deviceID.some(w=>w.deviceID===r)||m.deviceClass[r]&&t.push({deviceID:m.deviceClass[r].ID,name:m.deviceClass[r].name,classID:m.deviceClass[r].classID})}),_.value[e.index].deviceID=_.value[e.index].deviceID.concat(t)},ge=e=>{let o=new Set(ie.value);e.deviceID=e.deviceID.filter(t=>!o.has(t.deviceID))},me=()=>{M.value.validate(async e=>{if(e){let o=[];if(_.value.forEach(t=>{t.deviceID.forEach(r=>{r.classID===t.classID&&o.push({id:"",point:t.point,value:t.value,classID:t.classID,timeout:t.timeout,deviceID:r.deviceID,fail_proc:t.fail_proc,pointType:t.pointType,retry_count:t.retry_count,fatherGroupId:T.value,finished_wait:t.finished_wait})})}),o.length)try{let t=await He(o.length);o.forEach((r,k)=>{r.id=t.uuid[k]})}catch{}I.value=[...o],A.value="action"}})},_e=()=>{A.value="config"},K=e=>{var o,t;return(t=(o=m.deviceClass[e])==null?void 0:o.name)!=null?t:""},we=()=>{let e=new Set(de.value);I.value=I.value.filter(o=>!e.has(o.id))},be=(e,o)=>{E.value=m.deviceClass[o.classID].attribute.filter(t=>["control","stateControl","calculation"].includes(t.pointType)),H.value.openDialog(e,o)},he=e=>{I.value.splice(e.index,1,e.data)},De=e=>{z.value=!0,Ae(),Fe(()=>{T.value=e.groupID})},R=()=>{_.value=[{point:"",pointValues:[],value:"",classID:"",attributes:[],timeout:5,deviceID:[],fail_proc:0,pointType:"",retry_count:0,finished_wait:5}],A.value="config",q=[],I.value=[],T.value="",E.value=[],z.value=!1},ye=()=>{X("editHandle",I.value),R()},Ae=async()=>{let e=[];try{let o=await G({classType:"product"});o&&(e=e.concat(o.data));let t=await G({classType:"point"});t&&(e=e.concat(t.data)),q=[...e]}catch{}};return W({openDrawer:De}),(e,o)=>{const t=p("el-icon"),r=p("el-option"),k=p("el-select"),w=p("el-form-item"),Ie=p("el-row"),Ce=p("el-input"),B=p("el-input-number"),C=p("el-button"),b=p("el-table-column"),N=p("el-table"),Ve=p("el-form"),ke=p("el-drawer");return u(),D($,null,[n(ke,{modelValue:z.value,"onUpdate:modelValue":o[1]||(o[1]=l=>z.value=l),onClose:R,size:"900","destroy-on-close":"","close-on-click-modal":!1},{header:s(()=>[i("span",Oe,c(d(Y)),1)]),default:s(()=>[A.value==="config"?(u(),D("div",Pe,[n(Ve,{inline:!0,ref_key:"formRef",ref:M,onSubmit:o[0]||(o[0]=qe(()=>{},["prevent"])),model:{configData:_.value},"label-width":"100px",class:"form-inline"},{default:s(()=>[(u(!0),D($,null,U(_.value,(l,v)=>(u(),D($,null,[i("div",xe,[i("div",Ge,[i("div",Je,c(e.$t("views.control.group.components.AddActionDrawer.kongzhicel\xFCe")),1),n(t,{style:{padding:"0 0 0 20px",cursor:"pointer"},onClick:a=>le(v)},{default:s(()=>[n(x,{name:"delete"})]),_:2},1032,["onClick"])]),n(Ie,null,{default:s(()=>[n(w,{label:e.$t("views.control.group.components.AddActionDrawer.shebeimoxing"),prop:["configData",v,"classID"],rules:{required:!0,message:e.$t("views.control.group.components.AddActionDrawer.qingxuanzemoxing"),trigger:"blur"}},{default:s(()=>[n(k,{modelValue:l.classID,"onUpdate:modelValue":a=>l.classID=a,class:"input",filterable:"",onChange:a=>Z(a,l)},{default:s(()=>[(u(!0),D($,null,U(d(q),a=>(u(),y(r,{label:a.name,value:a.classID},null,8,["label","value"]))),256))]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])]),_:2},1032,["label","prop","rules"])]),_:2},1024),n(w,{label:e.$t("views.control.group.components.AddActionDrawer.shuxing"),prop:["configData",v,"point"],rules:{required:!0,message:e.$t("views.control.group.components.AddActionDrawer.qingshurushuxing"),trigger:"blur"}},{default:s(()=>[n(k,{modelValue:l.point,"onUpdate:modelValue":a=>l.point=a,class:"input",filterable:"",onChange:a=>ee(a,l)},{default:s(()=>[(u(!0),D($,null,U(l.attributes,a=>(u(),y(r,{label:a.name,value:a.code},null,8,["label","value"]))),256))]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])]),_:2},1032,["label","prop","rules"]),n(w,{label:d(f)("views.control.group.components.AddActionDrawer.kongzhizhi"),prop:["configData",v,"value"],rules:[{required:!0,message:e.$t("views.control.group.components.AddActionDrawer.qingshurukongzhizhi"),trigger:"blur"},{validator:F(l)?oe:(a,uo,$e)=>$e(),trigger:"blur"}]},{default:s(()=>[F(l)?(u(),y(Ce,{key:0,modelValue:l.value,"onUpdate:modelValue":a=>l.value=a,modelModifiers:{trim:!0},disabled:!l.point,class:"input",onInput:a=>te(a,l)},null,8,["modelValue","onUpdate:modelValue","disabled","onInput"])):F(l)?S("",!0):(u(),y(k,{key:1,disabled:!l.point,class:"input",filterable:"",modelValue:l.value,"onUpdate:modelValue":a=>l.value=a},{default:s(()=>[(u(!0),D($,null,U(l.pointValues,a=>(u(),y(r,{key:a.value,value:a.value+"",label:a.label},null,8,["value","label"]))),128))]),_:2},1032,["disabled","modelValue","onUpdate:modelValue"]))]),_:2},1032,["label","prop","rules"]),n(w,{label:e.$t("views.control.group.components.AddActionDrawer.shibaicel\xFCe"),prop:["configData",v,"fail_proc"],rules:{required:!0,message:e.$t("views.control.group.components.AddActionDrawer.bunengweikong"),trigger:"blur"}},{default:s(()=>[n(k,{modelValue:l.fail_proc,"onUpdate:modelValue":a=>l.fail_proc=a,class:"input",filterable:""},{default:s(()=>[(u(!0),D($,null,U(d(je),a=>(u(),y(r,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:2},1032,["label","prop","rules"]),n(w,{label:e.$t("views.control.group.components.AddActionDrawer.shibaizhongshi"),prop:["configData",v,"retry_count"],rules:{required:!0,message:e.$t("views.control.group.components.AddActionDrawer.bunengweikong"),trigger:"blur"}},{default:s(()=>[n(B,{modelValue:l.retry_count,"onUpdate:modelValue":a=>l.retry_count=a,"controls-position":"right",min:0,precision:0,class:"input-num",max:600},null,8,["modelValue","onUpdate:modelValue"]),i("span",Qe,c(e.$t("views.control.group.components.AddActionDrawer.ci")),1)]),_:2},1032,["label","prop","rules"]),n(w,{label:e.$t("views.control.group.components.AddActionDrawer.chaoshishijian"),prop:["configData",v,"timeout"],rules:{required:!0,message:e.$t("views.control.group.components.AddActionDrawer.bunengweikong"),trigger:"blur"}},{default:s(()=>[n(B,{modelValue:l.timeout,"onUpdate:modelValue":a=>l.timeout=a,"controls-position":"right",min:1,precision:0,class:"input-num",max:600},null,8,["modelValue","onUpdate:modelValue"]),i("span",We,c(e.$t("views.control.group.components.AddActionDrawer.miao")),1)]),_:2},1032,["label","prop","rules"]),n(w,{label:e.$t("views.control.group.components.AddActionDrawer.zhixinghoudengdai"),prop:["configData",v,"finished_wait"],rules:{required:!0,message:e.$t("views.control.group.components.AddActionDrawer.bunengweikong"),trigger:"blur"}},{default:s(()=>[n(B,{modelValue:l.finished_wait,"onUpdate:modelValue":a=>l.finished_wait=a,"controls-position":"right",min:1,precision:0,class:"input-num",max:600},null,8,["modelValue","onUpdate:modelValue"]),i("span",Xe,c(e.$t("views.control.group.components.AddActionDrawer.miao")),1)]),_:2},1032,["label","prop","rules"])]),i("div",Ye,[i("div",Ze,[i("div",eo,c(e.$t("views.control.group.components.AddActionDrawer.guanlianshebei")),1)]),i("div",oo,[n(C,{type:"primary",onClick:a=>fe(v,l)},{default:s(()=>[g(c(e.$t("common.button.tianjia")),1)]),_:2},1032,["onClick"]),n(C,{type:"default",onClick:a=>ge(l),disabled:!d(re)},{default:s(()=>[g(c(e.$t("common.button.shanchu")),1)]),_:2},1032,["onClick","disabled"])]),n(N,{data:l.deviceID,fit:"",border:"","max-height":"250","row-key":d(se),onSelectionChange:d(ne)},{empty:s(()=>[i("div",to,[O(e.$slots,"empty",{},()=>[lo,i("div",null,c(e.$t("common.message.nodata")),1)],!0)])]),default:s(()=>[n(b,{type:"selection",width:"40",align:"center"}),n(b,{label:e.$t("views.control.group.components.AddActionDrawer.shebeibianma")},{default:s(a=>[g(c(a.row.deviceID),1)]),_:1},8,["label"]),n(b,{label:e.$t("views.control.group.components.AddActionDrawer.shebeimingcheng")},{default:s(a=>[g(c(a.row.name),1)]),_:1},8,["label"])]),_:2},1032,["data","row-key","onSelectionChange"])])],64))),256))]),_:3},8,["model"]),i("div",{class:"add-btn",onClick:ae},[n(t,null,{default:s(()=>[n(x,{name:"add"})]),_:1}),i("span",null,c(e.$t("common.button.tianjia")),1)])])):S("",!0),A.value==="action"?(u(),D("div",ao,[i("div",no,[n(C,{type:"default",onClick:we,disabled:!d(pe)},{default:s(()=>[g(c(e.$t("common.button.shanchu")),1)]),_:1},8,["disabled"])]),n(N,{data:I.value,fit:"",border:"","row-key":d(ue),onSelectionChange:d(ce)},{empty:s(()=>[i("div",so,[O(e.$slots,"empty",{},()=>[io,i("div",null,c(e.$t("common.message.nodata")),1)],!0)])]),default:s(()=>[n(b,{type:"selection",width:"40",align:"center"}),n(b,{label:d(f)("views.control.group.components.AddActionDrawer.shebeishili"),"show-overflow-tooltip":""},{default:s(l=>[i("span",null,c(K(l.row.deviceID)),1)]),_:1},8,["label"]),n(b,{label:d(f)("views.control.group.components.AddActionDrawer.shebeimoxing"),"show-overflow-tooltip":""},{default:s(l=>[i("span",null,c(K(l.row.classID)),1)]),_:1},8,["label"]),n(b,{label:d(f)("views.control.group.components.AddActionDrawer.kongzhidian"),"show-overflow-tooltip":""},{default:s(l=>[i("span",null,c(V.pointFilter(l.row.point,l.row.classID)),1)]),_:1},8,["label"]),n(b,{label:d(f)("views.control.group.components.AddActionDrawer.kongzhizhi"),"show-overflow-tooltip":""},{default:s(l=>[i("span",null,c(V.valueFilter(l.row.value,l.row.point,l.row.classID)),1)]),_:1},8,["label"]),n(b,{label:e.$t("common.table.caozuo"),align:"center",width:"80"},{default:s(l=>[n(C,{type:"primary",link:"",onClick:v=>be(l.$index,l.row)},{default:s(()=>[g(c(e.$t("common.button.bianji")),1)]),_:2},1032,["onClick"])]),_:1},8,["label"])]),_:3},8,["data","row-key","onSelectionChange"])])):S("",!0)]),footer:s(()=>[i("div",ro,[A.value==="config"?(u(),y(C,{key:0,type:"primary",onClick:me},{default:s(()=>[g(c(d(f)("views.control.group.components.AddActionDrawer.xiayibu")),1)]),_:1})):S("",!0),A.value==="action"?(u(),y(C,{key:1,type:"primary",onClick:_e},{default:s(()=>[g(c(d(f)("views.control.group.components.AddActionDrawer.shangyibu")),1)]),_:1})):S("",!0),A.value==="action"?(u(),y(C,{key:2,type:"primary",disabled:I.value.length===0,onClick:ye},{default:s(()=>[g(c(e.$t("common.button.baocun")),1)]),_:1},8,["disabled"])):S("",!0),n(C,{onClick:R},{default:s(()=>[g(c(e.$t("common.button.quxiao")),1)]),_:1})])]),_:3},8,["modelValue"]),n(Me,{ref_key:"relationRef",ref:j,onEditHandle:ve},null,512),n(Ke,{ref_key:"editActionRef",ref:H,attributes:E.value,onEditHandle:he},null,8,["attributes"])],64)}}});const Ao=Ne(co,[["__scopeId","data-v-f663658f"]]);export{Ao as default};
