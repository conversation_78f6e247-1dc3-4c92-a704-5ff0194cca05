import{d as H,aJ as J,A as P,r as m,x as T,f as Q,h as p,o as W,c as X,b as o,w as s,a as n,t as u,j as i,aA as Y,i as f,ao as Z,F as ee,aK as te,p as ae,e as oe}from"./main-dd669ad8.js";import{u as le,_ as se}from"./useSelection-ccce70fc.js";import{s as ne,t as re,v as ie,f as de}from"./control-95804351.js";import{g as ue}from"./common-9b09fb7e.js";import ce from"./EditScheduleDialog-c23e15ff.js";import{_ as me}from"./_plugin-vue_export-helper-361d09b5.js";const pe=h=>(ae("data-v-af3a4633"),h=h(),oe(),h),fe={class:"header-title"},_e={class:"config-box"},ve={class:"strategy-box"},he={class:"header"},be={class:"title"},ge={class:"device-box"},we={class:"header"},ye={class:"title"},De={class:"defineBtn"},Ce={class:"table-empty"},Se=pe(()=>n("img",{src:se,alt:"notData"},null,-1)),Te={class:"footer-btns"},ke=H({__name:"EditTimetableDrawer",setup(h,{expose:k}){const{t:d}=J();P();const b=m(!1),g=m(0),x=T(()=>(g.value?d("common.button.bianji"):d("common.button.tianjia"))+d("views.control.timetable.components.EditTimetableDrawer.shijianbiao")),w=m(),E=Q({programName:[{required:!0,message:d("views.control.timetable.components.EditTimetableDrawer.qingshurumingcheng"),trigger:"blur"}]}),l=m({version:0,schedule:[],programID:"",description:"",programName:"",scheduleConfig:{startDate:"",endDate:"",repeatType:"",lastDayOfMonth:!1,days:null,specificDays:null},enable:!1}),I=T(()=>l.value.schedule.sort((e,t)=>t.runTime>e.runTime?-1:1)),_=m(),C=m([]),{selectionChange:V,getRowKeys:N,selectedListIds:j,isSelected:$}=le("uuid"),z=e=>{let t="";return e.forEach(a=>{let v=C.value.find(c=>c.controlSetID===a.controlSetID);v&&(t+=v.controlSetName+" ")}),t},S=m(),R=()=>{S.value.openDialog(-1)},M=()=>{let e=new Set(j.value);l.value.schedule=l.value.schedule.filter(t=>!e.has(t.uuid))},B=(e,t)=>{S.value.openDialog(e,t)},G=e=>{e.index>-1?l.value.schedule.splice(e.index,1,e.data):l.value.schedule.push(e.data)},K=(e,t)=>{b.value=!0,te(async()=>{if(w.value&&w.value.resetFields(),_.value=e,A(),t){g.value=1;try{let a=await ie({programID:t.programID});a.result&&a.result.resultCode==="0"&&Object.assign(l.value,a.data)}catch{}}else g.value=0,q()})},q=async()=>{try{let e=await ue();e.result&&e.result.resultCode==="0"&&(l.value.programID=e.data[0])}catch{}},A=async()=>{try{let t=await de({pageNum:1,pageSize:9999});t.result&&t.result.resultCode==="0"&&(C.value=t.data.data)}catch{}},y=()=>{l.value={version:0,schedule:[],programID:"",description:"",programName:"",scheduleConfig:{startDate:"",endDate:"",repeatType:"",lastDayOfMonth:!1,days:null,specificDays:null},enable:!1},b.value=!1},F=()=>{w.value.validate(async(e,t)=>{if(e)if(g.value===0)try{let a=await ne(l.value);a.result&&a.result.resultCode==="0"&&(y(),_.value&&_.value())}catch{}else try{let a=await re(l.value);a.result&&a.result.resultCode==="0"&&(y(),_.value&&_.value())}catch{}})};return k({openDrawer:K}),(e,t)=>{const a=p("el-input"),v=p("el-form-item"),c=p("el-button"),D=p("el-table-column"),L=p("el-table"),O=p("el-form"),U=p("el-drawer");return W(),X(ee,null,[o(U,{modelValue:b.value,"onUpdate:modelValue":t[3]||(t[3]=r=>b.value=r),onClose:y,size:"900","destroy-on-close":"","close-on-click-modal":!1},{header:s(()=>[n("span",fe,u(i(x)),1)]),default:s(()=>[n("div",_e,[o(O,{ref_key:"formRef",ref:w,onSubmit:t[2]||(t[2]=Y(()=>{},["prevent"])),rules:E,model:l.value,"label-width":"100px",class:"form-inline"},{default:s(()=>[n("div",ve,[n("div",he,[n("div",be,u(i(d)("views.control.timetable.components.EditTimetableDrawer.jibenxinxi")),1)]),o(v,{label:i(d)("views.control.timetable.components.EditTimetableDrawer.fanganmingcheng"),prop:"programName"},{default:s(()=>[o(a,{maxlength:"128",modelValue:l.value.programName,"onUpdate:modelValue":t[0]||(t[0]=r=>l.value.programName=r),modelModifiers:{trim:!0}},null,8,["modelValue"])]),_:1},8,["label"]),o(v,{label:i(d)("views.control.timetable.components.EditTimetableDrawer.fanganmiaoshu"),prop:"description"},{default:s(()=>[o(a,{modelValue:l.value.description,"onUpdate:modelValue":t[1]||(t[1]=r=>l.value.description=r),modelModifiers:{trim:!0},type:"textarea",maxlength:"128","show-word-limit":""},null,8,["modelValue"])]),_:1},8,["label"])]),n("div",ge,[n("div",we,[n("div",ye,u(i(d)("views.control.timetable.components.EditTimetableDrawer.chufapeizhi")),1)]),n("div",De,[o(c,{type:"primary",onClick:R},{default:s(()=>[f(u(e.$t("common.button.tianjia")),1)]),_:1}),o(c,{type:"default",onClick:M,disabled:!i($)},{default:s(()=>[f(u(e.$t("common.button.shanchu")),1)]),_:1},8,["disabled"])]),o(L,{data:i(I),fit:"",border:"","row-key":i(N),onSelectionChange:i(V)},{empty:s(()=>[n("div",Ce,[Z(e.$slots,"empty",{},()=>[Se,n("div",null,u(e.$t("common.message.nodata")),1)],!0)])]),default:s(()=>[o(D,{type:"selection",width:"40",align:"center"}),o(D,{label:i(d)("views.control.timetable.components.EditTimetableDrawer.zhixingshijian"),width:"100",align:"center"},{default:s(r=>[f(u(r.row.runTime),1)]),_:1},8,["label"]),o(D,{label:i(d)("views.control.timetable.components.EditTimetableDrawer.zukongmingcheng")},{default:s(r=>[f(u(z(r.row.controlSet)),1)]),_:1},8,["label"]),o(D,{label:i(d)("common.table.caozuo"),width:"80"},{default:s(r=>[o(c,{type:"primary",link:"",onClick:xe=>B(r.$index,r.row)},{default:s(()=>[f(u(e.$t("common.button.bianji")),1)]),_:2},1032,["onClick"])]),_:1},8,["label"])]),_:3},8,["data","row-key","onSelectionChange"])])]),_:3},8,["rules","model"])])]),footer:s(()=>[n("div",Te,[o(c,{type:"primary",onClick:F},{default:s(()=>[f(u(e.$t("common.button.baocun")),1)]),_:1}),o(c,{onClick:y},{default:s(()=>[f(u(e.$t("common.button.quxiao")),1)]),_:1})])]),_:3},8,["modelValue"]),o(ce,{ref_key:"scheduleRef",ref:S,schedules:l.value.schedule,groups:C.value,onEditHandle:G},null,8,["schedules","groups"])],64)}}});const ze=me(ke,[["__scopeId","data-v-af3a4633"]]);export{ze as default};
