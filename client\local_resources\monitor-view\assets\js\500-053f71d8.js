import{_ as n}from"./500-5329ddf0.js";import{d as u,u as d,h as r,o as p,c as i,a as e,b as l,w as m,j as _,p as h,e as f,i as F,H as v}from"./main-dd669ad8.js";import{_ as E}from"./_plugin-vue_export-helper-361d09b5.js";const o=t=>(h("data-v-ccc0043d"),t=t(),f(),t),C={class:"not-container"},x=o(()=>e("img",{src:n,class:"not-img",alt:"500"},null,-1)),D={class:"not-detail"},B=o(()=>e("h2",null,"500",-1)),y=o(()=>e("h4",null,"\u62B1\u6B49\uFF0C\u60A8\u7684\u7F51\u7EDC\u4E0D\u89C1\u4E86~\u{1F926}\u200D\u2642\uFE0F\u{1F926}\u200D\u2640\uFE0F",-1)),I=u({name:"500"}),b=u({...I,setup(t){const c=d();return(g,s)=>{const a=r("el-button");return p(),i("div",C,[x,e("div",D,[B,y,l(a,{type:"primary",onClick:s[0]||(s[0]=k=>_(c).push(_(v)))},{default:m(()=>[F("\u8FD4\u56DE\u9996\u9875")]),_:1})])])}}});const V=E(b,[["__scopeId","data-v-ccc0043d"]]);export{V as default};
