<!DOCTYPE html>
<html style="height:100%;">
<head>
    <meta charset="utf-8">
    <title>设备预览</title>
    <link rel="icon" href="/supaiot/static/favicon.ico">
    <link rel="stylesheet" href="/supaiot/static/css/symbolanimation.css">
    <link href="/supaiot/static/css/element.css" rel="stylesheet">
    <script src="/supaiot/static/js/jquery.min.js"></script>
    <script src="/supaiot/static/js/react.min.js"></script>
    <script src="/supaiot/static/js/react-dom.min.js"></script>
    <script src="/supaiot/static/js/JSXTransformer.js"></script>
    <script src="/supaiot/static/js/postdata.js"></script>
    <script src="/supaiot/static/js/deviceevent.js"></script>
    <script src="/supaiot/static/js/devicemanage.js"></script>
    <script src="/supaiot/static/js/devicedata.js"></script>
    <script src="/supaiot/static/js/controller.js"></script>
</head>
<body style="height:100%;margin:0px;overflow:hidden;user-select:none;">
<div id="svg_div" style="visibility:hidden;width:0px;height:0px;display:flex;"><svg id="20405785c0346478bbe26712d22f5c2dsymbolshow_root" xmlns="http://www.w3.org/2000/svg" xmlns:svg="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" style="overflow:visible;visibility:visible;" width="111.21151733398438" height="112.71150207519531" ecityosbackgroundcolor="#FFF" ecityosname="图形1" viewBox="0 0 111.21151733398438 112.71150207519531"><defs></defs><g id="20405785c0346478bbe26712d22f5c2dsymbol_sup" ecityosname="图形1"><title>图形1</title><g id="20405785c0346478bbe26712d22f5c2d__symbol_sup__graph4" ecityosgroup="yes" customid="20405785c0346478bbe26712d22f5c2d__symbol_sup__graph4" transform="translate(-276.3943 -105.8942)" xmlns="http://www.w3.org/2000/svg"><desc>状态graph4</desc><circle fill="#FF0000" stroke="#000000" stroke-width="5" cx="332" cy="163" r="55.60575485229492" id="20405785c0346478bbe26712d22f5c2d__symbol_sup__graph1" customid="20405785c0346478bbe26712d22f5c2d__symbol_sup__graph1" fill-opacity="1" stroke-opacity="1"></circle></g><g id="20405785c0346478bbe26712d22f5c2d__symbol_sup__graph5" ecityosgroup="yes" customid="20405785c0346478bbe26712d22f5c2d__symbol_sup__graph5" fill-opacity="1" transform="translate(-276.3943 -105.8942)" xmlns="http://www.w3.org/2000/svg"><desc>状态graph5</desc><circle fill="#00ff7f" stroke="#000000" stroke-width="5" cx="332" cy="161.50000381469727" r="55.60575485229492" id="20405785c0346478bbe26712d22f5c2d__symbol_sup__graph3" customid="20405785c0346478bbe26712d22f5c2d__symbol_sup__graph3" fill-opacity="1" stroke-opacity="1"></circle></g></g></svg></div>
<svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg" id="symbolshow_real">
    <g class="svg-pan-zoom_viewport">
        <rect id="pan-zoom-rect" fill="#ff0000" fill-opacity="0" stroke="#000000" stroke-width="0" x="0" y="0" />
        <g class="layer">
            <g id="TempDevice"></g>
        </g>
    </g>
</svg>
<script>
    let svgroot = $("#svg_div svg")
    let widthattr = svgroot.attr('width');
    let heightattr = svgroot.attr('height');
    svgroot.attr('style', 'overflow:visible;visibility: hidden;');
    $("#pan-zoom-rect").attr('width',widthattr)
    $("#pan-zoom-rect").attr('height',heightattr)
</script>
<script src="/supaiot/static/js/svg-pan-zoom.js"></script>
<script src="/supaiot/static/js/pagezoom.js"></script>
<script type="text/jsx" src="/supaiot/static/js/message-box.jsx"></script>
<script type="text/jsx">
		var _VIEW_DEVICE_ID = ""
		if(_VIEW_DEVICE_ID == "") {
			_VIEW_DEVICE_ID = getQueryVariable("id")
		}
        var ReactObjectManager = [];
        var ReactDeviceConfig = '';
        function getConfig() {
			let pointlist = [_VIEW_DEVICE_ID];
			let React_device_config_url = getUrlByTag("deviceConfig");
            $.ajax({
                type: "POST",
                url: React_device_config_url,
                contentType: 'application/json',
                data: JSON.stringify(pointlist),
                success: function (data, status, request) {
                    ReactDeviceConfig = data;
                },
                error: function (data) {
                    console.log(data)
                }
            })
        }
        getConfig()

        function setConfig(resultdata) {
            let tempdata = {}
            if (Object.prototype.toString.call(resultdata) == '[object Object]') {
                tempdata = resultdata
            } else {
                tempdata = JSON.parse(resultdata);
            }
            let data = tempdata['data'];
            for (let i = 0; i < ReactObjectManager.length; i++) {
                let device = ReactObjectManager[i];
                let state = {}
                for (let j = 0; j < data.length; j++) {
                    if (data[j]['ID'] != device.props.deviceid) {
                        continue
                    }
                    state['config'] = data[j];
                    device.setState(state);
                }
            }
        }

        function parsedata(resultdata) {
            let tempdata = {}
            if (Object.prototype.toString.call(resultdata) == '[object Object]') {
                tempdata = resultdata
            } else {
                tempdata = JSON.parse(resultdata);
            }
            let data = tempdata['data'];
            if (ReactDeviceConfig != "") {
                setConfig(ReactDeviceConfig);
            }
            for (let i = 0; i < ReactObjectManager.length; i++) {
                let device = ReactObjectManager[i];
                let state = {};
                for (let j = 0; j < data.length; j++) {
                    if (data[j]['id'] != device.props.deviceid) {
                        continue
                    }
                    let tagdata = data[j]['state'];
                    state["state"] = tagdata;
                    let updatetime = data[j]["updatetime"];
                    state["updatetime"] = updatetime;
                    let quality = data[j]["quality"];
                    state["quality"] = quality;
                    device.setState(state);
                }
            }
        }

        function attchReactObject(device) {
            ReactObjectManager.push(device);
        }

        function dispatchReactObject(device) {
            ReactObjectManager.pop();
        }

        function startInteval() {
            var _device_subcribe_url = getUrlByTag("realData");
            let pointlist = [_VIEW_DEVICE_ID];
            let jsondata = {
                id: pointlist
            };
            $.ajax({
                type: "POST",
                url: _device_subcribe_url,
                data: JSON.stringify(jsondata),
                success: function (data, status, request) {
                    parsedata(data)
                },
                error: function (data) {
                    console.log(data)
                }
            })
        }
    </script>
<script type="text/jsx">
class _visualization_2d_symbol_sAbqduscW5zrfanf_图形1 extends React.Component{
        constructor(props){
            super(props);
            this.state = {
                state:{},
                quality:{},
                updatetime:{},
                config:{
                    information:{}
                },
                userChanged:false,
                
            };
        }
        componentDidMount(){
            attchReactObject(this);
            this.loadStyles();
        }
        componentWillUnmount(){
            dispatchReactObject(this);
        }
        componentDidUpdate(prevProps, prevState) {
            if(!this.state.userChanged){
                let newState = {};
                if(Object.keys(this.state.state).length>0){
                    newState.userChanged = true;
                }
                
                Object.keys(newState).length && this.setState(newState)
            }
        }
        loadStyles() {
            const styleString = ``;

            if(styleString == ""){ return; }
            const styleElement = document.createElement('style');
            if (styleElement.styleSheet) {
               styleElement.styleSheet.cssText = styleString;
            } else {
               styleElement.appendChild(document.createTextNode(styleString));
            }
            document.head.appendChild(styleElement);
        }
        condition( state,  updatetime, quality, config, flaglist ) {
            try{if(state["DI1"] == false){flaglist[0] = true;}
else if(state["DI1"] == true){flaglist[1] = true;}
else{}} catch(e){ console.error(e); }

        }
        handleChange(stateKey,event) {
            this.setState({
                [stateKey]: event.target.value,
            })
        }
        buttonFunc(stateKey,control,input_data,timeOut) {
            let id = this.props.deviceid;
            if(!id){return}
            let json = {};
            json.id = id;
            json.control = control;
            json.value = input_data;

            console.log("json  ",json);
            this.setState({
                [stateKey + "Disabled"]:true
            });
            if(timeOut){
                setTimeout(()=>{
                   this.setState({
                       [stateKey + "Disabled"]:false
                   });
                },timeOut)
            }
            subfetch(json,"",()=> {
                this.setState({
                    [stateKey + "Disabled"]: false, // 解除 select 禁用
                });
            });
        }
        buttonClick(stateKey,control,input_data,config,event){
            if(control == ""){
                return;
            }
            let confirm = config.confirm,
                msg = config.confirmMsg,
                timeout = config.timeout;
            if(confirm){
                Elconfirm(msg,"提示").then(()=>{
                    this.buttonFunc(stateKey,control,input_data,timeout)
                }).catch(()=>{console.log("canel")})
            }else{
                this.buttonFunc(stateKey,control,input_data,timeout)
            }
        }
        processData(value,mapping,flag){
            if (value === undefined || value === null) {
                return "--";
            }
            if(typeof value === 'object'){
                try{
                    return JSON.stringify(value)
                } catch(e){
                    return value + ''
                }
            }else{
                if(flag && typeof value === 'boolean'){
                    return mapping[value ? "ON" : "OFF"]
                }else{
                    return mapping[value] || (value + '')
                }
            }
        }
        render(){
            let flaglist = [];
            flaglist[0] = false;
flaglist[1] = false;

            this.condition(this.state.state, this.state.updatetime, this.state.quality, this.state.config, flaglist);
            let temp = [];
            temp.push(React.createElement("use",{style:{visibility:flaglist[0]?"visible":"hidden"},xlinkHref:"#20405785c0346478bbe26712d22f5c2d__symbol_sup__graph4",key:"状态graph4"}));
temp.push(React.createElement("use",{style:{visibility:flaglist[1]?"visible":"hidden"},xlinkHref:"#20405785c0346478bbe26712d22f5c2d__symbol_sup__graph5",key:"状态graph5"}));

            return React.createElement("g", null, temp);
        }
    }

	ReactDOM.render(<_visualization_2d_symbol_sAbqduscW5zrfanf_图形1 deviceid={_VIEW_DEVICE_ID} />,document.getElementById('TempDevice'));
	startInteval();
	setInterval(startInteval, 1000);
    </script>
</body>

</html>