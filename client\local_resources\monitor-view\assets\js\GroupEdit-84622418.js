import{d as B,aJ as N,f as E,r as c,h as n,o as k,B as q,w as t,a as U,b as o,i as C,aA as A,aK as R}from"./main-dd669ad8.js";import{g as j}from"./common-9b09fb7e.js";import{_ as K}from"./_plugin-vue_export-helper-361d09b5.js";const M={class:"dialog-footer"},O=B({__name:"GroupEdit",emits:["addGroup","editGroup"],setup(T,{expose:b,emit:f}){const{t:d}=N(),u=E({trendGroupID:"",trendGroupName:"",max:1e3,min:0,point:[],version:1}),F=(r,e,a)=>{if(e<=u.min)return a(new Error(d("views.monitor.trend.Edit.zuidazhiyaodayuzuixiaozhi")));a()},G=(r,e,a)=>{if(e>=u.max)return a(new Error(d("views.monitor.trend.Edit.zuixiaozhiyaoxiaoyuzuidazhi")));a()},V=E({trendGroupID:[{required:!0,message:"\u8BF7\u8F93\u5165\u6570\u636E\u7EC4ID",trigger:"blur"}],trendGroupName:[{required:!0,message:"\u8BF7\u8F93\u5165\u6570\u636E\u7EC4\u540D\u79F0",trigger:"blur"}],max:[{required:!0,message:d("views.monitor.trend.Edit.qingshuruzuidazhi"),trigger:"change"},{type:"number",validator:F,trigger:"change"}],min:[{required:!0,message:d("views.monitor.trend.Edit.qingshuruzuixiaozhi"),trigger:"change"},{type:"number",validator:G,trigger:"change"}]}),i=c(),s=c(!1),m=c("add"),h=(r,e)=>{s.value=!0,m.value=r,R(()=>{i.value&&i.value.resetFields(),m.value==="add"&&y(),e&&Object.assign(u,e)})},y=async()=>{try{let r=await j();r.result&&r.result.resultCode==="0"&&(u.trendGroupID=r.data[0])}catch{}},_=()=>{Object.assign(u,{trendGroupID:"",trendGroupName:"",max:1e3,min:0,point:[],version:1}),i.value&&i.value.clearValidate(),s.value=!1},D=()=>{i.value.validate(async(r,e)=>{r&&(m.value==="add"?f("addGroup",u):f("editGroup",u))})};return b({openDialog:h,handleCancel:_}),(r,e)=>{const a=n("el-input"),p=n("el-form-item"),g=n("el-input-number"),v=n("el-col"),w=n("el-row"),z=n("el-form"),x=n("el-button"),I=n("el-dialog");return k(),q(I,{modelValue:s.value,"onUpdate:modelValue":e[5]||(e[5]=l=>s.value=l),title:`${m.value==="add"?"\u65B0\u5EFA":"\u7F16\u8F91"}\u6570\u636E\u7EC4`,onClose:_,"close-on-click-modal":!1,width:"600px",draggable:""},{footer:t(()=>[U("span",M,[o(x,{type:"info",onClick:_},{default:t(()=>[C("\u53D6\u6D88")]),_:1}),o(x,{type:"primary",onClick:D},{default:t(()=>[C("\u786E\u5B9A")]),_:1})])]),default:t(()=>[o(z,{model:u,onSubmit:e[4]||(e[4]=A(()=>{},["prevent"])),rules:V,ref_key:"formRef",ref:i,"label-width":"95px"},{default:t(()=>[o(p,{label:"\u6570\u636E\u7EC4ID",prop:"trendGroupID"},{default:t(()=>[o(a,{modelValue:u.trendGroupID,"onUpdate:modelValue":e[0]||(e[0]=l=>u.trendGroupID=l),disabled:""},null,8,["modelValue"])]),_:1}),o(p,{label:"\u6570\u636E\u7EC4\u540D\u79F0",prop:"trendGroupName"},{default:t(()=>[o(a,{modelValue:u.trendGroupName,"onUpdate:modelValue":e[1]||(e[1]=l=>u.trendGroupName=l),modelModifiers:{trim:!0},maxlength:64,placeholder:"\u8BF7\u8F93\u5165\u6570\u636E\u7EC4\u540D\u79F0"},null,8,["modelValue"])]),_:1}),o(w,{gutter:24},{default:t(()=>[o(v,{span:12},{default:t(()=>[o(p,{label:"\u5750\u6807\u6700\u5927\u503C",prop:"max"},{default:t(()=>[o(g,{modelValue:u.max,"onUpdate:modelValue":e[2]||(e[2]=l=>u.max=l),placeholder:"\u8BF7\u8F93\u5165\u5750\u6807\u6700\u5927\u503C","controls-position":"right"},null,8,["modelValue"])]),_:1})]),_:1}),o(v,{span:12},{default:t(()=>[o(p,{label:"\u5750\u6807\u6700\u5C0F\u503C",prop:"min"},{default:t(()=>[o(g,{modelValue:u.min,"onUpdate:modelValue":e[3]||(e[3]=l=>u.min=l),placeholder:"\u8BF7\u8F93\u5165\u5750\u6807\u6700\u5C0F\u503C","controls-position":"right"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue","title"])}}});const H=K(O,[["__scopeId","data-v-326e0951"]]);export{H as default};
