import{d as w,aJ as I,r as g,A as S,f as v,h as r,o as q,B,w as l,a as j,b as a,i as k,t as b,j as p,aA as F,aK as K}from"./main-dd669ad8.js";import{k as L}from"./control-95804351.js";import{g as R}from"./common-9b09fb7e.js";import{_ as U}from"./_plugin-vue_export-helper-361d09b5.js";const M={class:"dialog-footer"},T=w({name:"LinkageDialog"}),$=w({...T,setup(J,{expose:y}){const{t:d}=I(),u=g(!1);S();const m=g(),t=v({trigger:{script:"",taglist:[],reference:[]},version:0,linkageID:"",controlSet:[],description:"",linkageName:""}),V=v({linkageName:[{required:!0,message:d("views.control.linkage.components.AddDialog.qingshuruliandongmingcheng"),trigger:"blur"}]}),n=g(),h=e=>{u.value=!0,K(()=>{n.value&&n.value.resetFields(),x(),m.value=e})},x=async()=>{try{let e=await R();e.result&&e.result.resultCode==="0"&&(t.linkageID=e.data[0])}catch{}},D=e=>{c(),e()},c=()=>{Object.assign(t,{trigger:{script:"",taglist:[],reference:[]},version:0,linkageID:"",controlSet:[],description:"",linkageName:""}),n.value&&n.value.resetFields(),u.value=!1},C=()=>{n.value.validate(async(e,o)=>{if(e)try{let s=await L(t);s.result&&s.result.resultCode==="0"&&(c(),m.value&&m.value())}catch{}})};return y({openDialog:h}),(e,o)=>{const s=r("el-input"),f=r("el-form-item"),A=r("el-form"),_=r("el-button"),N=r("el-dialog");return q(),B(N,{modelValue:u.value,"onUpdate:modelValue":o[3]||(o[3]=i=>u.value=i),title:p(d)("views.control.linkage.components.AddDialog.xinjianliandong"),"before-close":D,"close-on-click-modal":!1,width:"800px",draggable:""},{footer:l(()=>[j("span",M,[a(_,{onClick:c},{default:l(()=>[k(b(e.$t("common.button.quxiao")),1)]),_:1}),a(_,{type:"primary",onClick:C},{default:l(()=>[k(b(e.$t("common.button.queren")),1)]),_:1})])]),default:l(()=>[a(A,{model:t,onSubmit:o[2]||(o[2]=F(()=>{},["prevent"])),rules:V,ref_key:"formRef",ref:n,"label-width":"150px"},{default:l(()=>[a(f,{label:p(d)("views.control.linkage.components.AddDialog.liandongmingcheng"),prop:"linkageName"},{default:l(()=>[a(s,{class:"input",modelValue:t.linkageName,"onUpdate:modelValue":o[0]||(o[0]=i=>t.linkageName=i),modelModifiers:{trim:!0},maxlength:64},null,8,["modelValue"])]),_:1},8,["label"]),a(f,{label:p(d)("views.control.linkage.components.AddDialog.liandongmiaoshu")},{default:l(()=>[a(s,{type:"textarea",maxlength:128,"show-word-limit":"",rows:2,class:"input",modelValue:t.description,"onUpdate:modelValue":o[1]||(o[1]=i=>t.description=i)},null,8,["modelValue"])]),_:1},8,["label"])]),_:1},8,["model","rules"])]),_:1},8,["modelValue","title"])}}});const H=U($,[["__scopeId","data-v-9618b878"]]);export{H as default};
