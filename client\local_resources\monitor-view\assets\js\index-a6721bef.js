import{_ as c}from"./History.vue_vue_type_script_setup_true_name_HistoryAlarm_lang-959a4f7d.js";import f from"./Real-6d18abb0.js";import{_ as d}from"./Suppress.vue_vue_type_script_setup_true_name_HistoryAlarm_lang-80355062.js";import{d as b,r as l,h as _,o as v,B as x,w as t,b as e}from"./main-dd669ad8.js";import{_ as y}from"./_plugin-vue_export-helper-361d09b5.js";import"./index.vue_vue_type_script_setup_true_name_ProTable_lang-ce2538eb.js";import"./useSelection-ccce70fc.js";import"./product-327919f5.js";import"./monitor-1739c0cb.js";import"./SceneDialog.vue_vue_type_style_index_0_lang-e0ce4561.js";import"./mittBus-a5a7f363.js";const B=b({__name:"index",setup(k){const n=l("real"),a=l(),s=l(),u=o=>{o.props.name=="suppress"?a.value&&a.value.getList():o.props.name=="history"&&s.value&&s.value.getList()};return(o,p)=>{const r=_("el-tab-pane"),m=_("el-tabs");return v(),x(m,{class:"tab-style",modelValue:n.value,"onUpdate:modelValue":p[0]||(p[0]=i=>n.value=i),onTabClick:u,type:"border-card"},{default:t(()=>[e(r,{name:"real",label:"\u5B9E\u65F6\u62A5\u8B66"},{default:t(()=>[e(f)]),_:1}),e(r,{name:"suppress",label:"\u6291\u5236\u62A5\u8B66"},{default:t(()=>[e(d,{ref_key:"suppressRef",ref:a},null,512)]),_:1}),e(r,{name:"history",label:"\u5386\u53F2\u62A5\u8B66"},{default:t(()=>[e(c,{ref_key:"hisRef",ref:s},null,512)]),_:1})]),_:1},8,["modelValue"])}}});const I=y(B,[["__scopeId","data-v-5a4438e7"]]);export{I as default};
