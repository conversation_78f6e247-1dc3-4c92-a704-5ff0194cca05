import{c as _}from"./cloneDeep-174273b5.js";import{i as S,t as C}from"./index-2ff848b4.js";import{d as L,am as F,r as k,g as E,a7 as z,a3 as N,o as T,c as B,a as M,R as $}from"./main-dd669ad8.js";import{_ as R}from"./_plugin-vue_export-helper-361d09b5.js";const W=L({__name:"LineChart",props:{id:{default:"lineChart"},width:{default:"100%"},height:{default:"400px"},data:{default:[]}},setup(c){const d=c,a=F("currentGroup"),b="#10182E",l="#FFFFFF99",n="#2E4076",u=k();let o=null;const w=()=>{var m,x;d.data;let t=_(d.data),r=0,i=0,s={},g=t.map(e=>{typeof e.max=="number"&&r<e.max&&(r=e.max),typeof e.min=="number"&&i>e.min&&(i=e.min);let p=e.deviceName+":"+(e!=null&&e.aliasCodeName?e.aliasCodeName:e.codeName),v=e.data.map(y=>({name:p,value:[y.time,y.value]}));return s[p]=e.isShow,{name:p,type:"line",symbol:"none",itemStyle:{color:e.color,borderColor:"#fff",borderWidth:1},areaStyle:{color:{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:0,color:`${f(e.color)}80`},{offset:1,color:`${f(e.color)}00`}],global:!1}},data:v}});!u.value||(o=S(u.value),o.setOption({tooltip:{trigger:"axis"},legend:{show:!0,selected:s,selectedMode:!1,bottom:"bottom",itemGap:16,itemWidth:8,itemHeight:8,icon:"circle",itemStyle:{borderWidth:0},textStyle:{fontSize:14,color:l},formatter:e=>C(e,90,"","\u2026",{}),tooltip:{show:!0,extraCssText:"max-width:300px;white-space: pre-wrap;word-break:break-all;"}},grid:{top:"30",left:"0",right:"0",bottom:"30",containLabel:!0,show:!0,backgroundColor:b,borderColor:"transparent"},xAxis:{type:"time",axisTick:{show:!1},axisLabel:{color:l,rotate:45},splitLine:{show:!0,lineStyle:{type:"dashed",color:n}},axisLine:{show:!0,lineStyle:{color:n}}},yAxis:[{type:"value",min:(m=a.value.min)!=null?m:i,max:(x=a.value.max)!=null?x:r,position:"left",nameTextStyle:{color:l},axisLabel:{color:l},splitLine:{show:!0,lineStyle:{type:"dashed",color:n}},axisLine:{show:!0,lineStyle:{color:n}}}],series:g}))};function f(t){const[r,i,s]=t.match(/\d+/g).map(Number);return`#${((1<<24)+(r<<16)+(i<<8)+s).toString(16).slice(1)}`}E(()=>window.addEventListener("resize",h)),z(()=>{window.removeEventListener("resize",h),o==null||o.dispose(),o=null});const h=()=>o&&o.resize();return N([()=>d.data,()=>{var t;return(t=a.value)==null?void 0:t.min},()=>{var t;return(t=a.value)==null?void 0:t.max}],()=>setTimeout(()=>w(),0),{deep:!0,immediate:!0}),(t,r)=>(T(),B("div",{style:$({width:c.width,height:c.height})},[M("div",{ref_key:"chartRef",ref:u,class:"chart"},null,512)],4))}});const j=R(W,[["__scopeId","data-v-ebb02d2c"]]);export{j as default};
