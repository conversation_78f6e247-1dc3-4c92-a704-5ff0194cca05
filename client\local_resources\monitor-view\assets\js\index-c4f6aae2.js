import y from"./LoginForm-b0f4c46f.js";import{d,G as h,x as c,g as v,o as l,c as g,a as n,j as i,y as b,t as x,b as k,z as B}from"./main-dd669ad8.js";import{_ as C}from"./_plugin-vue_export-helper-361d09b5.js";import"./keepAlive-0c595e2b.js";import"./crypto-5816c22c.js";const w={class:"login-container flx-center",id:"login_bg"},I={class:"login-box"},L={class:"login-form"},N={class:"login-logo"},S=["src"],D={class:"logo-text"},P=d({name:"login"}),V=d({...P,setup(j){const s=h(),t=c(()=>s.loginConfig),_=c(()=>s.viewName),r=c(()=>s.themeConfig),m=(o,e)=>{o.style.setProperty("--bg-image",`url(${e})`)},u=(o,e)=>{o.style.setProperty("--opacity",e)},p=o=>{let{id:e}=B();return o+=`&projectId=${e}`,o=o.replace("/resource-manager/image/preview","/resource-manager/noauth/image/preview"),o};return v(()=>{const o=document.getElementById("login_bg"),e=t.value.background,f=t.value.backgroundOpacity;if(t.value.customBg&&(m(o,e),u(o,f)),t.value.isDark){const a=document.querySelector("#login_bg .logo-text");a==null||a.classList.add("isDark")}}),(o,e)=>(l(),g("div",w,[n("div",I,[n("div",L,[n("div",N,[i(r).showLoginLogo?(l(),g("img",{key:0,class:"login-icon",src:p(i(r).logo),alt:"logo"},null,8,S)):b("",!0),n("h2",D,x(i(_)),1)]),k(y)])])]))}});const z=C(V,[["__scopeId","data-v-04e9303d"]]);export{z as default};
