import{d as B,aJ as H,r as p,x as R,f as K,h as r,o as _,B as w,w as u,b as s,i as T,t as y,j as S,c as $,P as A,F as G,aA as J,aK as L}from"./main-dd669ad8.js";import{g as M}from"./control-95804351.js";import{_ as O}from"./_plugin-vue_export-helper-361d09b5.js";const P=B({__name:"EditScheduleDialog",props:{schedules:null,groups:null},emits:["editHandle"],setup(b,{expose:k,emit:E}){const h=b,{t:a}=H(),d=p(!1),c=p(-1),V=R(()=>(c.value>-1?a("common.button.bianji"):a("common.button.tianjia"))+a("views.control.timetable.components.EditScheduleDialog.shijianbiao")),t=p({uuid:"",runTime:"",group:[],controlSet:[]}),m=p(),z=(o,e,n)=>{const i=h.schedules;(i==null?void 0:i.some((v,f)=>v.runTime==t.value.runTime?c.value>-1?f!=c.value:!0:!1))?n(new Error(a("views.control.timetable.components.EditScheduleDialog.gaishijianpeizhiyicunzai"))):n()},I=K({runTime:[{required:!0,message:a("views.control.timetable.components.EditScheduleDialog.qingxuanzezhixingshijian"),trigger:"change"},{validator:z,trigger:"change"}],group:[{required:!0,message:a("views.control.timetable.components.EditScheduleDialog.qingxuanzekongzhizu"),trigger:"change"}]}),C=(o,e)=>{d.value=!0,L(()=>{m.value&&m.value.resetFields(),c.value=o,o>-1?(Object.assign(t.value,e),t.value.group=e==null?void 0:e.controlSet.map(n=>n.controlSetID)):j()})},j=async()=>{try{let o=await M();t.value.uuid=o.uuid}catch{}},q=o=>{g(),o()},g=o=>{t.value={uuid:"",runTime:"",group:[],controlSet:[]},m.value&&m.value.resetFields(),o&&(d.value=!1)},F=()=>{t.value.controlSet=[],t.value.group.forEach(o=>{let e=h.groups.find(n=>n.controlSetID===o);e&&t.value.controlSet.push({controlSetID:e.controlSetID,controlSetName:e.controlSetName})})},N=o=>{m.value.validate(async(e,n)=>{if(e){let i={uuid:t.value.uuid,runTime:t.value.runTime,controlSet:t.value.controlSet};E("editHandle",{index:c.value,data:i}),g(o)}})};return k({openDialog:C}),(o,e)=>{const n=r("el-time-picker"),i=r("el-form-item"),D=r("el-option"),v=r("el-select"),f=r("el-form"),x=r("el-button"),U=r("el-dialog");return _(),w(U,{modelValue:d.value,"onUpdate:modelValue":e[4]||(e[4]=l=>d.value=l),title:S(V),"before-close":q,"close-on-click-modal":!1,width:"480px",draggable:""},{footer:u(()=>[s(x,{onClick:g},{default:u(()=>[T(y(o.$t("common.button.quxiao")),1)]),_:1}),s(x,{type:"primary",onClick:e[3]||(e[3]=l=>N(!0))},{default:u(()=>[T(y(o.$t("common.button.queren")),1)]),_:1})]),default:u(()=>[s(f,{model:t.value,onSubmit:e[2]||(e[2]=J(()=>{},["prevent"])),ref_key:"formRef",ref:m,rules:I,"label-width":"120px"},{default:u(()=>[s(i,{label:S(a)("views.control.timetable.components.EditScheduleDialog.zhixingshijian"),prop:"runTime"},{default:u(()=>[s(n,{style:{width:"calc(100% - 40px)"},modelValue:t.value.runTime,"onUpdate:modelValue":e[0]||(e[0]=l=>t.value.runTime=l),"value-format":"HH:mm:ss"},null,8,["modelValue"])]),_:1},8,["label"]),s(i,{label:S(a)("views.control.timetable.components.EditScheduleDialog.kongzhizu"),prop:"group"},{default:u(()=>[s(v,{modelValue:t.value.group,"onUpdate:modelValue":e[1]||(e[1]=l=>t.value.group=l),filterable:"",clearable:"",class:"input",onChange:F,multiple:""},{default:u(()=>[(_(!0),$(G,null,A(b.groups,l=>(_(),w(D,{key:l.controlSetID,label:l.controlSetName,value:l.controlSetID},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1},8,["label"])]),_:1},8,["model","rules"])]),_:1},8,["modelValue","title"])}}});const Y=O(P,[["__scopeId","data-v-0d0de91f"]]);export{Y as default};
