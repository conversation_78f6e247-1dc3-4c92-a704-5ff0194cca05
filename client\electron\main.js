const { app, BrowserWindow, Menu } = require('electron');
const path = require('path');
const fs = require('fs');
const { createApp, PORT } = require('../src/http-server');
const os = require('os');
const axios = require('axios');
const { WebSocket } = require('ws');
const AdmZip = require('adm-zip');

function loadConfig() {
    const configPath = path.join(__dirname, '..', 'config.json');
    try {
        return JSON.parse(fs.readFileSync(configPath, 'utf8'));
    } catch {
        return { publishRoot: '', serverUrl: 'http://localhost:8080' };
    }
}

let server;
let globalConfig;
let ws;
let currentVersion = '';
let updatePromptActive = false;
const declinedUpdateByProject = {}; // project -> version declined
const latestRequestedVersionByProject = {}; // project -> latest server version
const pendingUpdateByProject = {}; // project -> version pending publish
const promptingVersionByProject = {}; // project -> version currently prompting
const promptDebounceTimerByProject = {}; // project -> timeout id

async function maybePromptUpdate(projectId) {
    try {
        if (!projectId) return;
        if (updatePromptActive) return;
        const serverVersionNow = String(latestRequestedVersionByProject[projectId] || '');
        if (!serverVersionNow) return;
        if (pendingUpdateByProject[projectId] === serverVersionNow) return; // already accepted and pending
        if (declinedUpdateByProject[projectId] === serverVersionNow) return;
        if (promptingVersionByProject[projectId] === serverVersionNow) return; // already showing this version
        updatePromptActive = true;
        promptingVersionByProject[projectId] = serverVersionNow;
        const { dialog } = require('electron');
        const result = await dialog.showMessageBox({
            type: 'info',
            buttons: ['立即更新', '稍后'],
            defaultId: 0,
            cancelId: 1,
            title: '发现新版本',
            message: `项目 ${projectId} 有新版本 ${serverVersionNow}，是否立即更新？`
        });
        if (result.response === 0) {
            // 重新获取最新的服务端版本（弹窗期间可能已更新）
            const latestServerVersion = String(latestRequestedVersionByProject[projectId] || '');

            // 检查当前版本是否已经是最新版本
            if (String(currentVersion) === String(latestServerVersion)) {
                // 当前已经是最新版本，无需更新
                console.log(`项目 ${projectId} 当前版本 ${currentVersion} 已经是最新版本 ${latestServerVersion}，无需更新`);
                return;
            }

            // 检查是否有更新的版本需要处理
            if (latestServerVersion && String(latestServerVersion) !== String(serverVersionNow)) {
                // 弹窗期间有更新的版本，使用最新版本
                console.log(`项目 ${projectId} 弹窗期间版本已更新：${serverVersionNow} -> ${latestServerVersion}，将更新到最新版本`);
            }

            // 发送更新请求到最新版本
            const targetVersion = latestServerVersion || serverVersionNow;
            try { ws && ws.send(JSON.stringify({ type: 'request_publish', project: projectId })); } catch { }
            pendingUpdateByProject[projectId] = targetVersion;
            // 清除已拒绝记录，避免后续阻塞
            delete declinedUpdateByProject[projectId];
        } else {
            // 用户拒绝更新，记录当前弹窗显示的版本
            declinedUpdateByProject[projectId] = serverVersionNow;
        }
    } catch { } finally {
        updatePromptActive = false;
        const lastPrompting = promptingVersionByProject[projectId];
        if (lastPrompting) delete promptingVersionByProject[projectId];
        // 如果在弹窗期间收到了更新的更高版本，则覆盖再弹一次
        const latest = String(latestRequestedVersionByProject[projectId] || '');
        if (latest && declinedUpdateByProject[projectId] !== latest) {
            // 重新检查并弹出最新版本
            setTimeout(() => { maybePromptUpdate(projectId); }, 0);
        }
    }
}

function ensureDir(dir) {
    if (!fs.existsSync(dir)) fs.mkdirSync(dir, { recursive: true });
}

function log(...args) { console.log('[electron-client]', ...args); }

function removeDirRecursive(dir) {
    try { fs.rmSync(dir, { recursive: true, force: true }); } catch { }
}

function connectToServer() {
    const SERVER_URL = globalConfig.serverUrl;
    const WS_URL = SERVER_URL.replace('http', 'ws') + '/ws';
    const CLIENT_ID = globalConfig.clientId || undefined;
    const CLIENT_NAME = globalConfig.clientName || os.hostname();
    const CLIENT_PLATFORM = `${os.platform()} ${os.release()}`;
    const CLIENT_PROJECT = globalConfig.clientProject;
    const HEARTBEAT_INTERVAL_MS = globalConfig.heartbeatIntervalMs || 10000;

    const url = CLIENT_ID ? `${WS_URL}?clientId=${encodeURIComponent(CLIENT_ID)}` : WS_URL;
    ws = new WebSocket(url);

    ws.on('open', () => {
        log('connected to server');
        // read local version file for this project
        let localVersion = '';
        try {
            const publishRoot = globalConfig.publishRoot || path.join(process.cwd(), 'local_resources');
            const verDir = path.join(publishRoot, '.versions');
            const verFile = path.join(verDir, `${CLIENT_PROJECT}.json`);
            if (fs.existsSync(verFile)) {
                const v = JSON.parse(fs.readFileSync(verFile, 'utf8'));
                localVersion = String(v && v.version ? v.version : '');
            }
        } catch { }
        currentVersion = localVersion;
        ws.send(JSON.stringify({ type: 'hello', name: CLIENT_NAME, platform: CLIENT_PLATFORM, project: CLIENT_PROJECT, version: localVersion }));
    });

    ws.on('message', async (data) => {
        try {
            const msg = JSON.parse(data.toString());
            if (msg.type === 'heartbeat') {
                ws.send(JSON.stringify({ type: 'heartbeat', version: currentVersion }));
                return;
            }
            if (msg.type === 'publish') {
                const downloadUrl = `${SERVER_URL}${msg.downloadUrl}`;
                const publishRoot = globalConfig.publishRoot || path.join(process.cwd(), 'local_resources');
                const publishPath = path.resolve(publishRoot, msg.publishPath.replace(/^\//, ''));
                const parentDir = path.dirname(publishPath);
                ensureDir(parentDir);
                const stagingDir = publishPath + '.tmp';
                const backupDir = publishPath + '.bak';
                const tmpZip = path.join(os.tmpdir(), `pkg_${msg.pkgId}.zip`);
                let swapped = false;
                let backupMade = false;
                try {
                    log('downloading package', downloadUrl);
                    const response = await axios.get(downloadUrl, { responseType: 'arraybuffer' });
                    fs.writeFileSync(tmpZip, response.data);
                    // prepare staging
                    removeDirRecursive(stagingDir);
                    ensureDir(stagingDir);
                    // extract to staging
                    const zip = new AdmZip(tmpZip);
                    zip.extractAllTo(stagingDir, true);
                    // atomic replace: live -> .bak, staging -> live
                    removeDirRecursive(backupDir);
                    if (fs.existsSync(publishPath)) {
                        fs.renameSync(publishPath, backupDir);
                        backupMade = true;
                    }
                    fs.renameSync(stagingDir, publishPath);
                    swapped = true;
                    removeDirRecursive(backupDir);
                    log('published to', publishPath);
                    // persist version if provided
                    try {
                        if (msg.version) {
                            const verDir = path.join(publishRoot, '.versions');
                            ensureDir(verDir);
                            const verFile = path.join(verDir, `${CLIENT_PROJECT}.json`);
                            fs.writeFileSync(verFile, JSON.stringify({ project: CLIENT_PROJECT, version: String(msg.version) }, null, 2));
                            currentVersion = String(msg.version);
                        }
                    } catch { }
                    ws.send(JSON.stringify({ type: 'publish_result', ok: true, pkgId: msg.pkgId }));
                } catch (err) {
                    log('publish failed', err.message);
                    try {
                        if (!swapped && backupMade && !fs.existsSync(publishPath) && fs.existsSync(backupDir)) {
                            fs.renameSync(backupDir, publishPath);
                        }
                    } catch { }
                    ws.send(JSON.stringify({ type: 'publish_result', ok: false, pkgId: msg.pkgId, reason: err.message }));
                } finally {
                    try { fs.unlinkSync(tmpZip); } catch { }
                    removeDirRecursive(stagingDir);
                }
                return;
            }
            if (msg.type === 'update_available') {
                // Prompt user to update now or later
                try {
                    const projectId = String(msg.project || '');
                    const serverVersion = String(msg.version || '');
                    if (!projectId || !serverVersion) return;

                    // 检查当前版本是否已经是最新版本
                    if (String(currentVersion) === String(serverVersion)) {
                        console.log(`项目 ${projectId} 当前版本 ${currentVersion} 已经是最新版本 ${serverVersion}，无需弹窗提示`);
                        return;
                    }

                    // 记录最新版本请求（覆盖旧的）
                    latestRequestedVersionByProject[projectId] = serverVersion;
                    // 若当前有弹窗，忽略新通知（弹窗无法强制关闭）
                    if (updatePromptActive) return;
                    if (pendingUpdateByProject[projectId] === serverVersion) return; // already pending
                    // 立即弹出
                    maybePromptUpdate(projectId);
                } catch { }
                return;
            }
            if (msg.type === 'publish_result') {
                // Clear pending for this project so future prompts can appear if needed
                try {
                    const pid = String(msg.pkgId || globalConfig.clientProject || '');
                    if (pid) {
                        delete pendingUpdateByProject[pid];
                        // 如果更新成功，清除拒绝记录，允许后续版本提示
                        if (msg.ok) {
                            delete declinedUpdateByProject[pid];
                            console.log(`项目 ${pid} 更新成功，当前版本: ${currentVersion}`);
                        } else {
                            console.log(`项目 ${pid} 更新失败: ${msg.reason || '未知错误'}`);
                        }
                    }
                } catch { }
            }
        } catch (e) {
            // ignore
        }
    });

    ws.on('close', () => {
        log('connection closed, retrying in 3s');
        setTimeout(connectToServer, 3000);
    });

    ws.on('error', (err) => {
        log('ws error', err.message);
    });
}

function startHeartbeat() {
    const HEARTBEAT_INTERVAL_MS = globalConfig.heartbeatIntervalMs || 10000;
    setInterval(() => {
        try { ws && ws.readyState === 1 && ws.send(JSON.stringify({ type: 'heartbeat' })); } catch { }
    }, HEARTBEAT_INTERVAL_MS);
}

function startHttpServer(config) {
    const appHttp = createApp(config);
    server = appHttp.listen(3000, () => {
        console.log('Client HTTP server listening on http://127.0.0.1:3000');
    });
}

function createWindow() {
    console.log('createWindow');
    const win = new BrowserWindow({
        width: 1200,
        height: 800,
        webPreferences: { nodeIntegration: false, contextIsolation: true }
    });
    const projectId = encodeURIComponent(globalConfig.clientProject || '');
    const baseUrl = `http://127.0.0.1:3000/index.html?id=${projectId}#/login`;
    win.loadURL(baseUrl);

    //这块是为了刷新后访问初始页面，而不是no-permisson，但是在任意页面刷新都会回到初始页面，所以先注释
    // Application menu with standard sections; override Reload to go to baseUrl
    // const template = [
    //     {
    //         label: 'File',
    //         submenu: [
    //             { role: 'close' },
    //             { role: 'quit' }
    //         ]
    //     },
    //     {
    //         label: 'Edit',
    //         submenu: [
    //             { role: 'undo' },
    //             { role: 'redo' },
    //             { type: 'separator' },
    //             { role: 'cut' },
    //             { role: 'copy' },
    //             { role: 'paste' },
    //             { role: 'selectAll' }
    //         ]
    //     },
    //     {
    //         label: 'View',
    //         submenu: [
    //             {
    //                 label: 'Reload',
    //                 accelerator: 'CmdOrCtrl+R',
    //                 click: () => { try { win.loadURL(baseUrl); } catch {} }
    //             },
    //             {
    //                 label: 'Force Reload',
    //                 accelerator: 'Shift+CmdOrCtrl+R',
    //                 click: () => { try { win.loadURL(baseUrl); } catch {} }
    //             },
    //             { type: 'separator' },
    //             { role: 'toggleDevTools' },
    //             { type: 'separator' },
    //             { role: 'resetZoom' },
    //             { role: 'zoomIn' },
    //             { role: 'zoomOut' },
    //             { type: 'separator' },
    //             { role: 'togglefullscreen' }
    //         ]
    //     },
    //     {
    //         label: 'Window',
    //         submenu: [
    //             { role: 'minimize' },
    //             { role: 'close' }
    //         ]
    //     },
    //     {
    //         label: 'Help',
    //         submenu: []
    //     }
    // ];
    // try { Menu.setApplicationMenu(Menu.buildFromTemplate(template)); } catch {}

    // // Intercept user-triggered refresh to always reload the base URL
    // win.webContents.on('before-input-event', (event, input) => {
    //     const isCmdOrCtrl = input.control || input.meta;
    //     const isReloadKey = (input.key === 'r' && isCmdOrCtrl) || input.key === 'F5';
    //     if (isReloadKey) {
    //         event.preventDefault();
    //         try { win.loadURL(baseUrl); } catch {}
    //     }
    // });

    // win.webContents.on('did-fail-load', () => {
    //     try { win.loadURL(baseUrl); } catch {}
    // });
}

app.whenReady().then(() => {
    globalConfig = loadConfig();
    startHttpServer(globalConfig);
    connectToServer();
    startHeartbeat();
    createWindow();
    app.on('activate', () => {
        if (BrowserWindow.getAllWindows().length === 0) createWindow();
    });
});

app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
        app.quit();
    }
});

app.on('before-quit', () => {
    if (server) try { server.close(); } catch { }
    if (ws) try { ws.close(); } catch { }
});


