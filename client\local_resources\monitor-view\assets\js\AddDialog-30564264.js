import{d as w,aJ as N,r as p,A as z,f as v,h as i,o as I,B as q,w as l,a as B,b as a,i as b,t as h,j as f,aA as G,aK as j}from"./main-dd669ad8.js";import{c as F}from"./control-95804351.js";import{g as K}from"./common-9b09fb7e.js";import{_ as R}from"./_plugin-vue_export-helper-361d09b5.js";const U={class:"dialog-footer"},M=w({name:"ControlGroupDialog"}),T=w({...M,setup($,{expose:y}){const{t:u}=N(),d=p(!1);z();const c=p(),t=v({version:0,actionGroup:[],description:"",controlSetID:"",controlSetName:""}),x=v({controlSetName:[{required:!0,message:u("views.control.group.components.AddDialog.qingshurukongzhizumingcheng"),trigger:"blur"}]}),n=p(),C=e=>{d.value=!0,j(()=>{n.value&&n.value.resetFields(),V(),c.value=e})},V=async()=>{try{let e=await K();e.result&&e.result.resultCode==="0"&&(t.controlSetID=e.data[0])}catch{}},k=e=>{m(),e()},m=()=>{Object.assign(t,{version:0,actionGroup:[],description:"",controlSetID:"",controlSetName:""}),n.value&&n.value.resetFields(),d.value=!1},D=()=>{n.value.validate(async(e,o)=>{if(e)try{let r=await F(t);r.result&&r.result.resultCode==="0"&&(m(),c.value&&c.value())}catch{}})};return y({openDialog:C}),(e,o)=>{const r=i("el-input"),g=i("el-form-item"),S=i("el-form"),_=i("el-button"),A=i("el-dialog");return I(),q(A,{modelValue:d.value,"onUpdate:modelValue":o[3]||(o[3]=s=>d.value=s),title:f(u)("views.control.group.components.AddDialog.xinjiankongzhizu"),"before-close":k,"close-on-click-modal":!1,width:"800px",draggable:""},{footer:l(()=>[B("span",U,[a(_,{onClick:m},{default:l(()=>[b(h(e.$t("common.button.quxiao")),1)]),_:1}),a(_,{type:"primary",onClick:D},{default:l(()=>[b(h(e.$t("common.button.queren")),1)]),_:1})])]),default:l(()=>[a(S,{model:t,style:{"margin-top":"16px"},rules:x,ref_key:"formRef",ref:n,onSubmit:o[2]||(o[2]=G(()=>{},["prevent"])),"label-width":"150px"},{default:l(()=>[a(g,{label:f(u)("views.control.group.components.AddDialog.kongzhizumingcheng"),prop:"controlSetName"},{default:l(()=>[a(r,{class:"input",modelValue:t.controlSetName,"onUpdate:modelValue":o[0]||(o[0]=s=>t.controlSetName=s),modelModifiers:{trim:!0},maxlength:64},null,8,["modelValue"])]),_:1},8,["label"]),a(g,{label:f(u)("views.control.group.components.AddDialog.kongzhizumiaoshu")},{default:l(()=>[a(r,{type:"textarea",maxlength:128,"show-word-limit":"",rows:2,class:"input",modelValue:t.description,"onUpdate:modelValue":o[1]||(o[1]=s=>t.description=s)},null,8,["modelValue"])]),_:1},8,["label"])]),_:1},8,["model","rules"])]),_:1},8,["modelValue","title"])}}});const H=R(T,[["__scopeId","data-v-c97c31a2"]]);export{H as default};
