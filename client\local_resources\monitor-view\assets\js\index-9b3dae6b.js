import{d as ie,aJ as de,r as E,g as re,a3 as Q,h as _,o as i,c as d,b as n,w as s,i as g,F as x,a as t,j as m,aw as ce,aS as _e,aT as P,P as M,y as W,aA as p,ak as B,p as pe,e as me,B as ve,t as y,ay as fe,aU as Ce,aN as he,aV as X,R as Y}from"./main-dd669ad8.js";import ge from"./GroupEdit-84622418.js";import ke from"./PointEdit-ec03476c.js";import{p as Ee,q as ye,r as Z,t as we}from"./monitor-1739c0cb.js";import{u as S}from"./useHandleData-6b868160.js";import{c as T}from"./cloneDeep-174273b5.js";import{_ as be}from"./_plugin-vue_export-helper-361d09b5.js";import"./common-9b09fb7e.js";import"./product-327919f5.js";import"./device-b598f94e.js";import"./project-54c43e68.js";const w=F=>(pe("data-v-93893655"),F=F(),me(),F),De={class:"group-search"},Ge={key:0,class:"group-list"},Ie={class:"group-item__header"},Be={class:"name sle"},Fe={class:"btn-box"},Ne={key:0,class:"points"},Ve={class:"points-item__header"},$e={class:"name"},xe={class:"points-item__content"},Ae=w(()=>t("i",{class:"label"},"\u6240\u5C5E\u8BBE\u5907",-1)),Pe={class:"value"},Me=w(()=>t("i",{class:"label"},"\u5355\u4F4D",-1)),Se={class:"value"},Te=w(()=>t("i",{class:"label"},"\u66F2\u7EBF\u989C\u8272",-1)),Re={key:1,class:"no-data"},Le={key:1,class:"no-data"},Ue={key:1,class:"group-list"},ze={class:"group-item mt24"},je={class:"group-item__header flex-jcsb"},qe=w(()=>t("div",{class:"name"},"\u81EA\u5B9A\u4E49\u6570\u636E\u7EC4",-1)),Ke={key:0,class:"points"},He={class:"points-item__header"},Je={class:"name"},Oe={class:"points-item__content"},Qe=w(()=>t("i",{class:"label"},"\u6240\u5C5E\u8BBE\u5907",-1)),We={class:"value"},Xe=w(()=>t("i",{class:"label"},"\u5355\u4F4D",-1)),Ye={class:"value"},Ze=w(()=>t("i",{class:"label"},"\u66F2\u7EBF\u989C\u8272",-1)),eo={key:1,class:"no-data"},oo=ie({__name:"index",emits:["pointChange"],setup(F,{emit:R}){const{t:b}=de(),v=E("1"),A=E(""),k=E("null"),r=E({point:[],max:1e3,min:0,trendGroupID:"",trendGroupName:"\u81EA\u5B9A\u4E49\u6570\u636E\u7EC4",version:1}),f=E([]),C=async()=>{var o,e;try{let u={pageNum:1,pageSize:999999,trendGroupName:A.value},a=await Ee(u);a.result&&a.result.resultCode=="0"&&(f.value=((o=a.data)==null?void 0:o.data)||[],f.value,(!k.value||k.value==="null")&&(k.value=(e=f.value[0])==null?void 0:e.trendGroupID))}catch(u){console.warn("get error =>",u)}};re(()=>{C()}),Q([()=>k.value,()=>f.value,()=>v.value],()=>{if(v.value==="1"){let o=f.value.findIndex(e=>e.trendGroupID===k.value);R("pointChange",o>-1?f.value[o]:{})}});const N=E(),L=(o,e)=>{N.value.openDialog(o,e)},V=E(),D=(o,e,u)=>{if(o==="add"&&e.point.length>=10){B.warning(b("views.monitor.trend.Edit.tongyiqushizudianweishuliangbukechaoguo10ge"));return}V.value.openDialog(o,e,u)},ee=async o=>{try{await S(we,{trendGroupID:o},b("common.message.beforeDelete"))}catch{return}await C()},oe=async(o,e)=>{let u=T(o);u.point=u.point.filter(a=>!(a.deviceId===e.deviceId&&a.code===e.code));try{await S(Z,u,b("common.message.beforeDelete"))}catch{return}await C()},te=async o=>{let e=await ye(o);e.result&&e.result.resultCode==="0"?(N.value.handleCancel(),B.success(b("common.message.addMsg"))):B.error(e.result.resultError),await C()},U=async(o,e="group")=>{let u=T(o),a=await Z(u);a.result&&a.result.resultCode==="0"?(B.success(b("common.message.saveMsg")),e==="group"&&N.value.handleCancel(),e==="point"&&V.value.handleCancel()):B.error(a.result.resultError),await C()},le=async(o,e)=>{const u=async()=>(r.value.point=r.value.point.filter(a=>!(a.deviceId===e.deviceId&&a.code===e.code)),Promise.resolve(!0));try{await S(u,null,b("common.message.beforeDelete"),"warning")}catch{return}},ne=async o=>{r.value=T(o),V.value.handleCancel()};Q([()=>{var o;return(o=r.value)==null?void 0:o.point},()=>v.value],([o,e])=>{e==="0"&&R("pointChange",r.value)});const z=o=>o!=null&&o.aliasCodeName?o.aliasCodeName:o.codeName;return(o,e)=>{var K,H;const u=_("el-radio-button"),a=_("el-radio-group"),$=_("el-icon"),se=_("el-input"),I=_("el-button"),G=_("el-dropdown-item"),j=_("el-dropdown-menu"),q=_("el-dropdown"),ae=_("el-collapse-item"),ue=_("el-collapse");return i(),d("div",null,[n(a,{modelValue:v.value,"onUpdate:modelValue":e[0]||(e[0]=l=>v.value=l),class:"group-mode"},{default:s(()=>[n(u,{label:"1"},{default:s(()=>[g("\u6570\u636E\u7EC4")]),_:1}),n(u,{label:"0"},{default:s(()=>[g("\u81EA\u5B9A\u4E49")]),_:1})]),_:1},8,["modelValue"]),v.value==="1"?(i(),d(x,{key:0},[t("div",De,[n(se,{modelValue:A.value,"onUpdate:modelValue":e[2]||(e[2]=l=>A.value=l),placeholder:"\u8BF7\u8F93\u5165\u6570\u636E\u7EC4\u540D\u79F0\u641C\u7D22",clearable:"",onClear:e[3]||(e[3]=l=>C()),onKeyup:e[4]||(e[4]=_e(l=>C(),["enter"]))},{append:s(()=>[n($,{class:"icon",onClick:e[1]||(e[1]=l=>C())},{default:s(()=>[n(m(ce))]),_:1})]),_:1},8,["modelValue"]),n(I,{icon:m(P),plain:"",onClick:e[5]||(e[5]=l=>L("add"))},null,8,["icon"])]),(K=f.value)!=null&&K.length?(i(),d("div",Ge,[n(ue,{modelValue:k.value,"onUpdate:modelValue":e[6]||(e[6]=l=>k.value=l),accordion:""},{default:s(()=>[(i(!0),d(x,null,M(f.value,l=>(i(),ve(ae,{title:"Consistency",name:l.trendGroupID,key:l.trendGroupID,class:"group-item"},{title:s(()=>[t("div",Ie,[t("div",Be,y(l.trendGroupName),1),n($,null,{default:s(()=>[n(m(fe))]),_:1}),t("div",Fe,[n(I,{link:"",icon:m(P),onClick:p(h=>D("add",l),["stop"])},null,8,["icon","onClick"]),n(I,{link:"",icon:m(Ce),onClick:p(h=>L("edit",l),["stop"])},null,8,["icon","onClick"]),n(I,{link:"",icon:m(he),onClick:p(h=>ee([l.trendGroupID]),["stop"])},null,8,["icon","onClick"])])])]),default:s(()=>{var h;return[(h=l.point)!=null&&h.length?(i(),d("ul",Ne,[(i(!0),d(x,null,M(l.point,(c,J)=>(i(),d("li",{class:"points-item",key:c.deviceId+c.code},[t("div",Ve,[t("span",$e,y(z(c)),1),n(q,null,{dropdown:s(()=>[n(j,null,{default:s(()=>[n(G,{onClick:p(O=>D("detail",l,J),["stop"])},{default:s(()=>[g("\u8BE6\u60C5")]),_:2},1032,["onClick"]),n(G,{onClick:p(O=>D("edit",l,J),["stop"])},{default:s(()=>[g("\u7F16\u8F91")]),_:2},1032,["onClick"]),n(G,{onClick:p(O=>oe(l,c),["stop"])},{default:s(()=>[g("\u5220\u9664")]),_:2},1032,["onClick"])]),_:2},1024)]),default:s(()=>[n($,null,{default:s(()=>[n(m(X))]),_:1})]),_:2},1024)]),t("div",xe,[t("span",null,[Ae,t("i",Pe,y(c.deviceName),1)]),t("span",null,[Me,t("i",Se,y(c.unit),1)]),t("span",null,[Te,t("i",{class:"value color",style:Y({background:c.color})},null,4)])])]))),128))])):(i(),d("div",Re,"\u6682\u65E0\u6570\u636E"))]}),_:2},1032,["name"]))),128))]),_:1},8,["modelValue"])])):(i(),d("div",Le,"\u6682\u65E0\u6570\u636E"))],64)):W("",!0),v.value==="0"?(i(),d("div",Ue,[t("div",ze,[t("div",je,[qe,t("div",null,[n(I,{link:"",icon:m(P),onClick:e[7]||(e[7]=p(l=>D("add",r.value),["stop"]))},null,8,["icon"])])]),(H=r.value.point)!=null&&H.length?(i(),d("ul",Ke,[(i(!0),d(x,null,M(r.value.point,(l,h)=>(i(),d("li",{class:"points-item",key:l.deviceId+l.code},[t("div",He,[t("span",Je,y(z(l)),1),n(q,null,{dropdown:s(()=>[n(j,null,{default:s(()=>[n(G,{onClick:p(c=>D("detail",r.value,h),["stop"])},{default:s(()=>[g("\u8BE6\u60C5")]),_:2},1032,["onClick"]),n(G,{onClick:p(c=>D("edit",r.value,h),["stop"])},{default:s(()=>[g("\u7F16\u8F91")]),_:2},1032,["onClick"]),n(G,{onClick:p(c=>le(r.value,l),["stop"])},{default:s(()=>[g("\u5220\u9664")]),_:2},1032,["onClick"])]),_:2},1024)]),default:s(()=>[n($,null,{default:s(()=>[n(m(X))]),_:1})]),_:2},1024)]),t("div",Oe,[t("span",null,[Qe,t("i",We,y(l.deviceName),1)]),t("span",null,[Xe,t("i",Ye,y(l.unit),1)]),t("span",null,[Ze,t("i",{class:"value color",style:Y({background:l.color})},null,4)])])]))),128))])):(i(),d("div",eo,"\u6682\u65E0\u6570\u636E"))])])):W("",!0),n(ge,{ref_key:"groupEditRef",ref:N,onAddGroup:te,onEditGroup:U},null,512),n(ke,{ref_key:"pointEditRef",ref:V,groupMode:v.value,onEditPoint:U,onCustomEditPoint:ne},null,8,["groupMode"])])}}});const mo=be(oo,[["__scopeId","data-v-93893655"]]);export{mo as default};
