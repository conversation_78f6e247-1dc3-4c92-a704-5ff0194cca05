<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <title>参数测试</title>
  <style>
    body { font-family: Arial, sans-serif; margin: 40px; }
    #result { font-size: 18px; color: green; }
  </style>
</head>
<body>
  <h1>URL 参数测试</h1>
  <p>当前地址：<span id="url"></span></p>
  <p>传递的 id 参数：<span id="result"></span></p>

  <script>
    // 显示当前完整 URL
    document.getElementById("url").textContent = window.location.href;

    // 解析参数
    const params = new URLSearchParams(window.location.search);
    const id = params.get("id");

    // 显示 id
    document.getElementById("result").textContent = id ? id : "未传递";
  </script>
</body>
</html>
