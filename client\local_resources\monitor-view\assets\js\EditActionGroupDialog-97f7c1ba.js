import{d as j,aJ as q,r as d,x as z,f as S,h as u,o as U,B,w as l,b as s,i as b,t as D,j as x,aA as F,aK as K}from"./main-dd669ad8.js";import{g as R}from"./control-95804351.js";import{_ as T}from"./_plugin-vue_export-helper-361d09b5.js";const $=j({__name:"EditActionGroupDialog",props:{actionGroups:null},emits:["editHandle"],setup(G,{expose:w,emit:y}){const N=G,{t:a}=q(),m=d(!1),r=d(-1),h=z(()=>(r.value<0?a("common.button.tianjia"):a("common.button.bianji"))+a("views.control.group.components.EditActionGroupDialog.zu")),t=d({groupName:"",groupID:"",actions:[]}),A=async()=>{try{let e=await R();t.value.groupID=e.uuid}catch{}},V=(e,o,i)=>{N.actionGroups.some((p,f)=>p.groupName==t.value.groupName?r.value>-1?f!=r.value:!0:!1)?i(new Error(a("views.control.group.components.EditActionGroupDialog.gaimingchengyicunzai"))):i()},C=S({groupName:[{required:!0,message:a("views.control.group.components.EditActionGroupDialog.qingshuruzumingcheng"),trigger:"change"},{validator:V,trigger:"change"}]}),n=d(),E=(e,o)=>{m.value=!0,K(()=>{n.value&&n.value.resetFields(),r.value=e,e>-1?Object.assign(t.value,o):A()})},I=e=>{g(),e()},g=e=>{t.value={groupName:"",groupID:"",actions:[]},n.value&&n.value.resetFields(),e&&(m.value=!1)},k=e=>{n.value.validate(async(o,i)=>{o&&(y("editHandle",{index:r.value,data:{...t.value}}),g(e))})};return w({openDialog:E}),(e,o)=>{const i=u("el-input"),v=u("el-form-item"),_=u("el-form"),p=u("el-button"),f=u("el-dialog");return U(),B(f,{modelValue:m.value,"onUpdate:modelValue":o[3]||(o[3]=c=>m.value=c),title:x(h),"before-close":I,"close-on-click-modal":!1,width:"480px",draggable:""},{footer:l(()=>[s(p,{onClick:g},{default:l(()=>[b(D(e.$t("common.button.quxiao")),1)]),_:1}),s(p,{type:"primary",onClick:o[2]||(o[2]=c=>k(!0))},{default:l(()=>[b(D(e.$t("common.button.queren")),1)]),_:1})]),default:l(()=>[s(_,{model:t.value,onSubmit:o[1]||(o[1]=F(()=>{},["prevent"])),rules:C,ref_key:"formRef",ref:n,"label-width":"90px"},{default:l(()=>[s(v,{label:x(a)("views.control.group.components.EditActionGroupDialog.zumingcheng"),prop:"groupName"},{default:l(()=>[s(i,{modelValue:t.value.groupName,"onUpdate:modelValue":o[0]||(o[0]=c=>t.value.groupName=c),modelModifiers:{trim:!0},class:"input",maxlength:64},null,8,["modelValue"])]),_:1},8,["label"])]),_:1},8,["model","rules"])]),_:1},8,["modelValue","title"])}}});const O=T($,[["__scopeId","data-v-14fafe93"]]);export{O as default};
