import{d as E,aJ as $,r as c,A as H,x as m,a3 as j,h as f,o as z,B as J,w as p,b as r,i as _,t as h,a as n,j as u,ac as K,aQ as M,aK as O,ak as Q}from"./main-dd669ad8.js";import{g as b}from"./product-327919f5.js";import{a as U}from"./device-b598f94e.js";import{_ as D}from"./index.vue_vue_type_script_setup_true_name_ProTable_lang-ce2538eb.js";const F={class:"device-content"},G={class:"left-part"},W={class:"no-card table-box"},X={class:"opreation"},Y={class:"add"},Z={class:"remove"},ee={class:"right-part"},ae={class:"no-card table-box"},ie=E({__name:"DeviceDialog",emits:["editHandle"],setup(le,{expose:w,emit:y}){const{t:i}=$(),d=c(!1),T=c(i("views.edge.case.components.RelationDialog.guanlianshebei")),o=c(),s=c();H();const k=c({}),t=c([]),B=m(()=>{var e,a;return(a=(e=o.value)==null?void 0:e.isSelected)!=null?a:!1}),C=m(()=>{var e,a;return(a=(e=s.value)==null?void 0:e.isSelected)!=null?a:!1}),x=e=>{d.value=!0,O(()=>{t.value=[...e],s.value.getTableList()})};j(()=>{var e;return(e=o.value)==null?void 0:e.searchParam},e=>{e&&!e.classType&&(o.value.searchParam.classType="product",o.value.search())},{deep:!0});const g=async()=>{let e=[];try{let a=await b({classType:"product"});a&&(e=e.concat(a.data));let l=await b({classType:"point"});return l&&(e=e.concat(l.data)),{data:e}}catch{return{data:e}}},S=()=>Promise.resolve({data:t.value}),I=[{type:"selection",fixed:"left",width:50,selectable(e){return t.value.length===0?!0:!t.value.find(l=>l.ID===e.id)}},{prop:"name",label:i("views.control.linkage.components.DeviceDialog.mingcheng"),align:"left",search:{el:"input",span:1,key:"deviceName"}},{prop:"classType",isOnlySearch:!0,isShow:!1,label:"\u7C7B\u578B",align:"left",enum:[{value:"product",label:"\u666E\u901A\u8BBE\u5907"},{value:"point",label:"\u5355\u70B9\u8BBE\u5907"}],search:{el:"select",span:1,props:{clearable:!1}}},{prop:"classID",label:i("views.control.linkage.components.DeviceDialog.chanpin"),align:"left",enum:g,fieldNames:{label:"name",value:"classID"}}],L=[{type:"selection",fixed:"left",width:50},{prop:"name",label:i("views.control.linkage.components.DeviceDialog.mingcheng"),align:"left"},{prop:"classID",label:i("views.control.linkage.components.DeviceDialog.chanpin"),align:"left",enum:g,fieldNames:{label:"name",value:"classID"}}],A=()=>{let e=o.value.selectedList.map(a=>{let l={...a,ID:a.id};return delete l.id,l});t.value=t.value.concat(e),o.value.clearSelection(),s.value.getTableList()},V=()=>{let e=new Set([...s.value.selectedListIds]);t.value=t.value.filter(a=>!e.has(a.ID)),s.value.clearSelection(),s.value.getTableList()},q=e=>{v(),e()},v=()=>{o.value.clearSelection(),s.value.clearSelection(),d.value=!1},N=async()=>{if(t.value.length===0)return Q.warning(i("views.edge.case.components.RelationDialog.qingxuanzeguanlianshebei"));y("editHandle",t.value),v()};return w({openDialog:x}),(e,a)=>{const l=f("el-button"),P=f("el-dialog");return z(),J(P,{modelValue:d.value,"onUpdate:modelValue":a[0]||(a[0]=R=>d.value=R),title:T.value,"before-close":q,"destroy-on-close":"",class:"relation-dialog","close-on-click-modal":!1,width:"1190px",draggable:""},{footer:p(()=>[r(l,{onClick:v},{default:p(()=>[_(h(e.$t("common.button.quxiao")),1)]),_:1}),r(l,{type:"primary",onClick:N},{default:p(()=>[_(h(e.$t("common.button.queren")),1)]),_:1})]),default:p(()=>[n("div",F,[n("div",G,[n("div",W,[r(D,{ref_key:"proTable",ref:o,"highlight-current-row":"",columns:I,pagerCount:2,border:!0,"select-id":"id",toolButton:!1,"init-param":k.value,requestApi:u(U)},null,8,["init-param","requestApi"])])]),n("div",X,[n("div",Y,[r(l,{disabled:!u(B),onClick:A,icon:u(K)},null,8,["disabled","icon"])]),n("div",Z,[r(l,{disabled:!u(C),onClick:V,icon:u(M)},null,8,["disabled","icon"])])]),n("div",ee,[n("div",ae,[r(D,{ref_key:"proTableRight",ref:s,"highlight-current-row":"",columns:L,pagination:!1,border:!0,"select-id":"ID",toolButton:!1,requestApi:S},null,512)])])])]),_:1},8,["modelValue","title"])}}});export{ie as _};
