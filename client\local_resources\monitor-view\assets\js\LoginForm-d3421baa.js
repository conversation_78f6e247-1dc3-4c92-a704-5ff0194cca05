import{d as K,u as N,A as R,G as D,r as _,f as v,g as M,h as a,o as T,c as L,b as t,w as s,a as I,i as y,j as b,k as U,l as h,F as z,m as O,H as P,n as q}from"./main-dd669ad8.js";import{T as G,K as H}from"./keepAlive-0c595e2b.js";import{C as $}from"./crypto-5816c22c.js";import{_ as j}from"./_plugin-vue_export-helper-361d09b5.js";const J={class:"login-btn"},Q=K({__name:"LoginForm",setup(W){const F=N(),k=G(),C=H(),S=R(),V=D(),i=_(),x=v({username:[{required:!0,message:"\u8BF7\u8F93\u5165\u7528\u6237\u540D",trigger:"blur"}],password:[{required:!0,message:"\u8BF7\u8F93\u5165\u5BC6\u7801",trigger:"blur"}]}),d=_(!1),n=v({username:"",password:""}),m=e=>{!e||e.validate(async o=>{if(!!o){d.value=!0;try{let r={...n},l=r.password;c.value||await f(),l=new $("rsa").Encrypt(r.password,c.value),await O({...r,password:l}),window.__IS_DEMO_ADMINISTRATOR_&&S.setUserName("super-admin"),k.closeMultipleTab(),C.setKeepAliveName(),V.setLoginEnableVoice(),F.push(P)}finally{d.value=!1}}})},A=e=>{!e||e.resetFields()},c=_(""),f=async()=>{try{let e=await q();if(e){const o=e.data;c.value=o.publicKey}}catch{}};return M(async()=>{f(),document.onkeydown=e=>{if(e=window.event||e,e.code==="Enter"||e.code==="enter"||e.code==="NumpadEnter"){if(d.value)return;m(i.value)}}}),(e,o)=>{const r=a("user"),l=a("el-icon"),p=a("el-input"),g=a("el-form-item"),B=a("lock"),E=a("el-form"),w=a("el-button");return T(),L(z,null,[t(E,{ref_key:"loginFormRef",ref:i,model:n,rules:x,size:"large"},{default:s(()=>[t(g,{prop:"username"},{default:s(()=>[t(p,{modelValue:n.username,"onUpdate:modelValue":o[0]||(o[0]=u=>n.username=u),placeholder:"\u7528\u6237\u540D"},{prefix:s(()=>[t(l,{class:"el-input__icon"},{default:s(()=>[t(r)]),_:1})]),_:1},8,["modelValue"])]),_:1}),t(g,{prop:"password"},{default:s(()=>[t(p,{type:"password",modelValue:n.password,"onUpdate:modelValue":o[1]||(o[1]=u=>n.password=u),placeholder:"\u5BC6\u7801","show-password":""},{prefix:s(()=>[t(l,{class:"el-input__icon"},{default:s(()=>[t(B)]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model","rules"]),I("div",J,[t(w,{icon:b(U),round:"",onClick:o[2]||(o[2]=u=>A(i.value)),size:"large"},{default:s(()=>[y("\u91CD\u7F6E")]),_:1},8,["icon"]),t(w,{icon:b(h),round:"",onClick:o[3]||(o[3]=u=>m(i.value)),size:"large",type:"primary",loading:d.value},{default:s(()=>[y(" \u767B\u5F55 ")]),_:1},8,["icon","loading"])])],64)}}});const oe=j(Q,[["__scopeId","data-v-42b047db"]]);export{oe as default};
