import{d as N,aJ as z,f as P,r as I,a3 as j,h as p,o as _,c as b,a as r,b as c,w as i,F as q,P as A,i as m,t as d,j as u,aD as F,aA as H,B as U,y as J}from"./main-dd669ad8.js";import{c as G}from"./serviceDict-f46c4ded.js";import{g as K}from"./device-b598f94e.js";import{a as O}from"./product-327919f5.js";import{u as Q}from"./useHandleData-6b868160.js";import{_ as W}from"./DeviceDialog.vue_vue_type_style_index_0_lang-0335bbef.js";import{_ as X}from"./_plugin-vue_export-helper-361d09b5.js";import"./index.vue_vue_type_script_setup_true_name_ProTable_lang-ce2538eb.js";import"./useSelection-ccce70fc.js";const Y={class:"trigger-box"},Z={class:"option-box"},ee={class:"left"},te={class:"switch"},ae={key:0},ne={class:"right"},oe={class:"switch",style:{margin:"8px 0 0"}},se=N({__name:"Trigger",props:{trigger:{default:()=>({script:"",taglist:[],reference:[]})}},emits:["update:trigger","save"],setup(k,{expose:x,emit:D}){const v=k,{t:f}=z(),e=P({chooseDeviceId:"",reference:[],deviceList:[],tableData:[],deviceClassMap:{},input_content:""}),y=I(),g=I(!1);j(()=>v.trigger,async t=>{e.input_content=t.script,e.reference=[].concat(t.reference),await w()},{deep:!0});const w=async()=>{if(e.reference.length>0){let t=[],n=new Set,l=new Set;if(e.reference.forEach(a=>{e.deviceClassMap[a]?t.push(e.deviceClassMap[a]):n.add(a)}),n.size>0){let a=await K([...n],!0);a.result&&a.result.resultCode==="0"&&(t=t.concat(a.data),a.data.forEach(s=>{e.deviceClassMap[s.ID]=s}))}if(t.forEach(a=>{e.deviceClassMap[a.classID]||l.add(a.classID)}),l.size>0){let a=await O([...l],!0);a.result&&a.result.resultCode==="0"&&a.data.forEach(s=>{e.deviceClassMap[s.classID]=s})}e.deviceList=t}else e.deviceList=[];e.tableData=[],e.chooseDeviceId=e.reference[0]||"",C(e.chooseDeviceId)},C=t=>{if(t){e.tableData=[];const n=e.deviceClassMap[t];n&&(e.tableData=e.deviceClassMap[n.classID].attribute.filter(l=>l.pointType!="control"&&l.pointType!="internalControl"))}},T=()=>{y.value.openDialog(e.deviceList)},V=async t=>{e.reference=[...t.map(n=>n.ID)],w()},E=t=>{e.input_content=e.input_content+`reference["${e.chooseDeviceId}"].state["${t.code}"]`,g.value=!0},M=t=>{let n=t&&t.match(/reference\[[\'\"](.+?)[\'\"]].state\[[\'\"](.+?)[\'\"]/g)||[],l=[];return n.forEach(a=>{let s=a.split("].state");s.length==2&&l.push({deviceID:s[0].replace(/reference\[/g,"").replace(/[\'\"]/g,""),point:s[1].replace(/\[/g,"").replace(/[\'\"]/g,"")})}),l},L=()=>{let t={reference:e.reference,taglist:M(e.input_content),script:e.input_content};D("update:trigger",t),g.value=!1},S=()=>{D("save")},$=async()=>{const t=async()=>(e.input_content=v.trigger.script,e.reference=[].concat(v.trigger.reference),g.value=!1,Promise.resolve(!0));try{await Q(t,null,f("views.control.linkage.components.Trigger.shifouquxiao"),"warning",!0)}catch{return}};return x({triggerEdit:g,submmitData:L}),(t,n)=>{const l=p("el-option"),a=p("el-select"),s=p("el-button"),h=p("el-table-column"),B=p("el-table"),R=p("el-input");return _(),b("div",Y,[r("div",Z,[r("div",ee,[r("div",te,[c(a,{modelValue:e.chooseDeviceId,"onUpdate:modelValue":n[0]||(n[0]=o=>e.chooseDeviceId=o),style:{width:"160px","margin-left":"10px"},onChange:C},{default:i(()=>[(_(!0),b(q,null,A(e.deviceList,o=>(_(),U(l,{key:o.ID,label:o.name,value:o.ID},{default:i(()=>[o.name?(_(),b("span",ae,d(o.name),1)):J("",!0)]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue"]),c(s,{style:{"margin-left":"10px"},type:"primary",onClick:T},{default:i(()=>[m(d(u(f)("views.control.linkage.components.Trigger.tianjialiandongshebei")),1)]),_:1})]),c(B,{data:e.tableData,fit:"","highlight-current-row":""},{default:i(()=>[c(h,{label:u(f)("views.control.linkage.components.Trigger.dianbianma"),align:"center"},{default:i(o=>[r("span",null,d(o.row.code),1)]),_:1},8,["label"]),c(h,{label:u(f)("views.control.linkage.components.Trigger.leixing"),align:"center"},{default:i(o=>[r("span",null,d(u(F)(o.row.type,u(G))),1)]),_:1},8,["label"]),c(h,{label:t.$t("common.table.caozuo"),align:"center"},{default:i(o=>[c(s,{link:"",type:"primary",onClick:H(le=>E(o.row),["stop"])},{default:i(()=>[m(d(u(f)("views.control.linkage.components.Trigger.charu")),1)]),_:2},1032,["onClick"])]),_:1},8,["label"])]),_:1},8,["data"])]),r("div",ne,[r("div",null,[c(R,{type:"textarea",modelValue:e.input_content,"onUpdate:modelValue":n[1]||(n[1]=o=>e.input_content=o),rows:20},null,8,["modelValue"])])])]),r("div",oe,[c(s,{type:"primary",onClick:S},{default:i(()=>[m(d(t.$t("common.button.baocun")),1)]),_:1}),c(s,{disabled:!g.value,onClick:$},{default:i(()=>[m(d(t.$t("common.button.quxiao")),1)]),_:1},8,["disabled"])]),c(W,{ref_key:"deviceRef",ref:y,onEditHandle:V},null,512)])}}});const me=X(se,[["__scopeId","data-v-6b97ac78"]]);export{me as default};
