import{d as D,aJ as G,r as u,aj as b,u as S,a0 as j,h as L,o as z,c as T,a as q,b as t,w as i,i as s,t as c,j as N,aP as d}from"./main-dd669ad8.js";import{f as P,q as A,h as B,r as H}from"./control-95804351.js";import{_ as V}from"./index.vue_vue_type_script_setup_true_name_ProTable_lang-ce2538eb.js";import F from"./AddDialog-30564264.js";import E from"./RunDialog-ebf29f0d.js";import J from"./HistoryDrawer-a37f7eac.js";import{u as O}from"./useHandleData-6b868160.js";import{_ as K}from"./_plugin-vue_export-helper-361d09b5.js";import"./useSelection-ccce70fc.js";import"./common-9b09fb7e.js";import"./serviceDict-f46c4ded.js";const M={class:"main-box"},Q={class:"group-control table-box"},U=D({name:"GroupControlList"}),W=D({...U,setup(X){const{t:n}=G(),p=u(),m=u(null),f=u(),g=u(),w=u({}),k=[{type:"selection",fixed:"left",width:50},{type:"index",label:n("common.table.xuhao"),width:60},{prop:"controlSetName",label:n("views.control.group.Index.kongzhizumingcheng"),align:"left",width:260,search:{el:"input",span:1}},{prop:"description",label:n("views.control.group.Index.kongzhizumiaoshu"),align:"left"},{prop:"creationtime",label:n("views.control.group.Index.chuangjianshijian"),align:"left",customFormat(e){return b(e)},width:180},{prop:"modificationtime",label:n("views.control.group.Index.xiugaishijian"),align:"left",customFormat(e){return b(e)},width:180},{prop:"operation",label:n("common.table.caozuo"),fixed:"right",width:200}],y=S(),v=j(),h=async e=>{let l={controlSetID:e};try{let o=await A(l);if(o.result&&o.result.resultCode==="0"){let r=o.data.some(a=>a.Linkage&&a.Linkage.length>0||a.Program&&a.Program.length>0);await O(B,l,d("p",null,[d("span",null,n("common.message.beforeDelete")),d("br",null),r?d("span",{style:"color: red"},n("views.control.group.Index.message")):null]))}}catch{return}p.value.clearSelection(),p.value.getTableList()},C=()=>{var e;(e=m.value)==null||e.openDialog(p.value.getTableList)},I=e=>{y.push(`${v.path}_edit/${e.controlSetID}`)},_=e=>{var l;(l=f.value)==null||l.openDialog({recordID:e.recordID})},x=async e=>{let l={controlSetID:e.controlSetID};try{let o=await H(l);if(o.result&&o.result.resultCode==="0"){if(!o.data.recordID)return;_({recordID:o.data.recordID})}}catch{}},$=e=>{g.value.openDrawer(e)},R=e=>{h([e.controlSetID])};return(e,l)=>{const o=L("el-button");return z(),T("div",M,[q("div",Q,[t(V,{ref_key:"proTable",ref:p,"highlight-current-row":"",columns:k,border:!1,selectId:"controlSetID","init-param":w.value,searchCol:{xs:1,sm:2,md:2,lg:3,xl:5},requestApi:N(P)},{tableHeader:i(r=>[t(o,{type:"primary",onClick:C},{default:i(()=>[s(c(e.$t("common.button.xinjian")),1)]),_:1}),t(o,{plain:"",onClick:a=>h(r.selectedListIds),disabled:!r.isSelected},{default:i(()=>[s(c(e.$t("common.button.shanchu")),1)]),_:2},1032,["onClick","disabled"])]),operation:i(r=>[t(o,{type:"primary",link:"",onClick:a=>I(r.row)},{default:i(()=>[s(c(e.$t("common.button.bianji")),1)]),_:2},1032,["onClick"]),t(o,{type:"primary",link:"",onClick:a=>x(r.row)},{default:i(()=>[s(c(e.$t("views.control.group.Index.zhixing")),1)]),_:2},1032,["onClick"]),t(o,{type:"primary",link:"",onClick:a=>$(r.row)},{default:i(()=>[s(c(e.$t("common.button.chakan")),1)]),_:2},1032,["onClick"]),t(o,{type:"primary",link:"",onClick:a=>R(r.row)},{default:i(()=>[s(c(e.$t("common.button.shanchu")),1)]),_:2},1032,["onClick"])]),_:1},8,["init-param","requestApi"])]),t(F,{ref_key:"addDialogRef",ref:m},null,512),t(E,{ref_key:"runDialogRef",ref:f},null,512),t(J,{onOpenRun:_,ref_key:"hisDrawerRef",ref:g},null,512)])}}});const ce=K(W,[["__scopeId","data-v-a5a02262"]]);export{ce as default};
