const path = require('path');
const fs = require('fs');
const os = require('os');
const axios = require('axios');
const { WebSocket } = require('ws');
const AdmZip = require('adm-zip');

// Load configuration from config.json
const configPath = path.join(__dirname, '..', 'config.json');
let config;
try {
    config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
} catch (e) {
    console.error('Failed to load config.json, using defaults');
    config = {
        serverUrl: 'http://localhost:8080',
        clientId: '',
        clientName: '',
        clientProject: 'default',
        publishRoot: '',
        heartbeatIntervalMs: 10000
    };
}

const SERVER_URL = config.serverUrl;
const WS_URL = SERVER_URL.replace('http', 'ws') + '/ws';
const CLIENT_ID = config.clientId || undefined; // let server assign if not provided
const CLIENT_NAME = config.clientName || os.hostname();
const CLIENT_PLATFORM = `${os.platform()} ${os.release()}`;
const CLIENT_PROJECT = config.clientProject;
const HEARTBEAT_INTERVAL_MS = config.heartbeatIntervalMs;

function ensureDir(dir) {
    if (!fs.existsSync(dir)) fs.mkdirSync(dir, { recursive: true });
}

function log(...args) { console.log('[client]', ...args); }

function removeDirRecursive(dir) {
    try { fs.rmSync(dir, { recursive: true, force: true }); } catch {}
}

let ws;
let currentVersion = '';

function connect() {
    const url = CLIENT_ID ? `${WS_URL}?clientId=${encodeURIComponent(CLIENT_ID)}` : WS_URL;
    ws = new WebSocket(url);

    ws.on('open', () => {
        log('connected to server');
        // read local version file for this project
        let localVersion = '';
        try {
            const publishRoot = config.publishRoot || path.join(process.cwd(), 'local_resources');
            const verDir = path.join(publishRoot, '.versions');
            const verFile = path.join(verDir, `${CLIENT_PROJECT}.json`);
            if (fs.existsSync(verFile)) {
                const v = JSON.parse(fs.readFileSync(verFile, 'utf8'));
                localVersion = String(v && v.version ? v.version : '');
            }
        } catch { }
        currentVersion = localVersion;
        ws.send(JSON.stringify({ type: 'hello', name: CLIENT_NAME, platform: CLIENT_PLATFORM, project: CLIENT_PROJECT, version: localVersion }));
    });

    ws.on('message', async (data) => {
        try {
            const msg = JSON.parse(data.toString());
            if (msg.type === 'heartbeat') {
                ws.send(JSON.stringify({ type: 'heartbeat', version: currentVersion }));
                return;
            }
            if (msg.type === 'publish') {
                const downloadUrl = `${SERVER_URL}${msg.downloadUrl}`;
                const publishRoot = config.publishRoot || path.join(process.cwd(), 'local_resources');
                const publishPath = path.resolve(publishRoot, msg.publishPath.replace(/^\//, ''));
                const parentDir = path.dirname(publishPath);
                ensureDir(parentDir);
                const stagingDir = publishPath + '.tmp';
                const backupDir = publishPath + '.bak';
                const tmpZip = path.join(os.tmpdir(), `pkg_${msg.pkgId}.zip`);
                let swapped = false;
                let backupMade = false;
                try {
                    log('downloading package', downloadUrl);
                    const response = await axios.get(downloadUrl, { responseType: 'arraybuffer' });
                    fs.writeFileSync(tmpZip, response.data);
                    // prepare staging
                    removeDirRecursive(stagingDir);
                    ensureDir(stagingDir);
                    // extract to staging
                    const zip = new AdmZip(tmpZip);
                    zip.extractAllTo(stagingDir, true);
                    // atomic replace: live -> .bak, staging -> live
                    removeDirRecursive(backupDir);
                    if (fs.existsSync(publishPath)) {
                        fs.renameSync(publishPath, backupDir);
                        backupMade = true;
                    }
                    fs.renameSync(stagingDir, publishPath);
                    swapped = true;
                    removeDirRecursive(backupDir);
                    log('published to', publishPath);
                    // persist version if provided
                    try {
                        if (msg.version) {
                            const verDir = path.join(publishRoot, '.versions');
                            ensureDir(verDir);
                            const verFile = path.join(verDir, `${CLIENT_PROJECT}.json`);
                            fs.writeFileSync(verFile, JSON.stringify({ project: CLIENT_PROJECT, version: String(msg.version) }, null, 2));
                            currentVersion = String(msg.version);
                            log('version updated to', msg.version);
                        }
                    } catch { }
                    ws.send(JSON.stringify({ type: 'publish_result', ok: true, pkgId: msg.pkgId }));
                } catch (err) {
                    log('publish failed', err.message);
                    try {
                        if (!swapped && backupMade && !fs.existsSync(publishPath) && fs.existsSync(backupDir)) {
                            fs.renameSync(backupDir, publishPath);
                        }
                    } catch {}
                    ws.send(JSON.stringify({ type: 'publish_result', ok: false, pkgId: msg.pkgId, reason: err.message }));
                } finally {
                    try { fs.unlinkSync(tmpZip); } catch {}
                    removeDirRecursive(stagingDir);
                }
                return;
            }
        } catch (e) {
            // ignore
        }
    });

    ws.on('close', () => {
        log('connection closed, retrying in 3s');
        setTimeout(connect, 3000);
    });

    ws.on('error', (err) => {
        log('ws error', err.message);
    });
}

setInterval(() => {
    try { ws && ws.readyState === 1 && ws.send(JSON.stringify({ type: 'heartbeat' })); } catch {}
}, HEARTBEAT_INTERVAL_MS);

connect();


