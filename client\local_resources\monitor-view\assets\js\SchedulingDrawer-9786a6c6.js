import{d as se,aJ as ne,A as ue,r as k,x as I,f as oe,a3 as re,h as D,o as h,B as ce,w as r,a as o,t as b,j as $,b as c,i as v,c as _,P as C,F as M,aA as ie,aK as de,Q as fe,p as pe,e as ye}from"./main-dd669ad8.js";import{t as De,v as ve}from"./control-95804351.js";import{_ as he}from"./_plugin-vue_export-helper-361d09b5.js";const me=T=>(pe("data-v-5fbbcb4c"),T=T(),ye(),T),ge={class:"header-title"},_e={class:"schedule-box"},we={class:"form-box"},be={class:"date"},ke={class:"repeat"},Fe={style:{display:"flex","justify-content":"space-between"}},Ce={class:"calendar-section"},Me={class:"year-nav"},Te={class:"calendar-grid"},Re=me(()=>o("thead",null,[o("tr",null,[o("th",null,"\u65E5"),o("th",null,"\u4E00"),o("th",null,"\u4E8C"),o("th",null,"\u4E09"),o("th",null,"\u56DB"),o("th",null,"\u4E94"),o("th",null,"\u516D")])],-1)),Ee=["onClick"],Se={class:"footer-btns"},Oe=se({__name:"SchedulingDrawer",setup(T,{expose:U}){ne(),ue();const R=k(!1),q=I(()=>"\u6392\u7A0B"),j=a=>a.getTime()<Date.now()-24*60*60*1e3,E=k(),N=oe({dateRange:[{required:!0,message:"\u65E5\u671F\u533A\u95F4\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),S=k({version:0,schedule:[],programID:"",description:"",programName:"",scheduleConfig:{startDate:"",endDate:"",repeatType:"",lastDayOfMonth:!1,days:null,specificDays:null},enable:!1}),e=k({dateRange:[],weekdays:[],monthDays:[],startDate:"",endDate:"",repeatType:"daily",lastDayOfMonth:!1,days:[],specificDays:[]}),F=k(new Date().getFullYear()),z=[{value:1,label:"\u661F\u671F\u4E00"},{value:2,label:"\u661F\u671F\u4E8C"},{value:3,label:"\u661F\u671F\u4E09"},{value:4,label:"\u661F\u671F\u56DB"},{value:5,label:"\u661F\u671F\u4E94"},{value:6,label:"\u661F\u671F\u516D"},{value:0,label:"\u661F\u671F\u65E5"}],L=a=>{var t;e.value.specificDays=(t=e.value.specificDays)==null?void 0:t.filter(s=>{if(a&&a.length===2&&a[0]&&a[1]){const u=new Date(a[0]),i=new Date(a[1]),p=new Date(s);if(p>=u&&p<=i)return!0}})};re(()=>e.value.repeatType,(a,t)=>{a!==t&&(a==="weekly"?(e.value.monthDays=[],e.value.lastDayOfMonth=!1,e.value.specificDays=[]):a==="monthly"?(e.value.weekdays=[],e.value.specificDays=[]):a==="specific"?(e.value.weekdays=[],e.value.monthDays=[],e.value.lastDayOfMonth=!1):a==="daily"&&(e.value.weekdays=[],e.value.monthDays=[],e.value.lastDayOfMonth=!1,e.value.specificDays=[]))});const H=a=>{a==="monthly"&&(e.value.lastDayOfMonth=!1)},J=()=>{e.value.lastDayOfMonth=!0,e.value.monthDays=[];for(let a=1;a<=31;a++)e.value.monthDays.push(a)},K=()=>{F.value--},P=()=>{F.value++},Q=a=>{var u;let t=a.fullDate;if(B(t)||!t)return;if(e.value.repeatType!=="specific"){const i=W(),p=F.value,l=new Set((u=e.value.specificDays)==null?void 0:u.concat(i));for(let f=1;f<=12;f++){const d=O(p,f);for(let m=1;m<=d;m++){const g=Y(p,f,m);A(g)&&l.add(g)}}e.value.specificDays=Array.from(l),e.value.repeatType="specific"}const s=e.value.specificDays.indexOf(t);s>-1?e.value.specificDays.splice(s,1):e.value.specificDays.push(t)},W=()=>{const a=e.value;if(a.dateRange&&a.dateRange.length===2&&a.dateRange[0]&&a.dateRange[1]){const t=new Date(a.dateRange[0]),s=new Date(a.dateRange[1]),u=[],i=l=>new Date(Date.UTC(l.getFullYear(),l.getMonth(),l.getDate()));if(t.setHours(0,0,0,0),s.setHours(0,0,0,0),a.repeatType==="daily"){let l=new Date(t);for(;l<=s;)u.push(i(l)),l=new Date(l.setDate(l.getDate()+1))}if(a.repeatType==="weekly"){let l=new Date(t);for(;l<=s;)a.weekdays.includes(l.getDay())&&u.push(i(l)),l=new Date(l.setDate(l.getDate()+1))}if(a.repeatType==="monthly"){let l=new Date(t.getFullYear(),t.getMonth(),1);for(;l<=s;){const f=l.getFullYear(),d=l.getMonth(),m=new Date(f,d+1,0).getDate(),g=new Set;for(const w of a.monthDays)w>=1&&w<=m&&g.add(w);a.lastDayOfMonth&&g.add(m);for(const w of g){const n=new Date(f,d,w);n>=t&&n<=s&&u.push(i(n))}l=new Date(f,d+1,1)}}return Array.from(new Set(u.map(l=>l.getTime()))).sort().map(l=>new Date(l)).map(l=>`${l.getFullYear()}-${(l.getMonth()+1).toString().padStart(2,"0")}-${l.getDate().toString().padStart(2,"0")}`)}else return[]},O=(a,t)=>new Date(a,t,0).getDate(),G=(a,t)=>new Date(a,t-1,1).getDay(),Y=(a,t,s)=>{const u=String(t).padStart(2,"0"),i=String(s).padStart(2,"0");return`${a}-${u}-${i}`},X=I(()=>{const a=F.value,t=[];for(let s=1;s<=12;s++){const u=O(a,s),i=G(a,s),p=[];let l=[],f=1;for(let d=0;d<i;d++)l.push(null);for(;f<=u;){l.length===7&&(p.push(l),l=[]);const d=Y(a,s,f);l.push({dayOfMonth:f,fullDate:d}),f++}for(;l.length<7;)l.push(null);p.push(l),t.push({month:s,weeks:p})}return t}),B=a=>{if(!a)return!0;const t=new Date(a);if(t.getTime()<Date.now()-24*60*60*1e3)return!0;if(e.value.dateRange&&e.value.dateRange.length===2&&e.value.dateRange[0]&&e.value.dateRange[1]){const s=new Date(e.value.dateRange[0]),u=new Date(e.value.dateRange[1]);if(t<s||t>u)return!0}return!1},A=a=>{if(!a)return!1;const t=new Date(a);if(e.value.dateRange&&e.value.dateRange.length===2&&e.value.dateRange[0]&&e.value.dateRange[1]){const s=new Date(e.value.dateRange[0]),u=new Date(e.value.dateRange[1]);if(t<s||t>u)return!1}switch(e.value.repeatType){case"daily":return!0;case"weekly":return e.value.weekdays.includes(t.getDay());case"monthly":const s=t.getDate(),u=e.value.lastDayOfMonth&&s===O(t.getFullYear(),t.getMonth()+1);return e.value.monthDays.includes(s)||u;case"specific":return e.value.specificDays.includes(a);default:return!1}},x=k(),Z=(a,t)=>{R.value=!0,de(async()=>{if(E.value&&E.value.resetFields(),x.value=a,t)try{let s=await ve({programID:t.programID});s.result&&s.result.resultCode==="0"&&(Object.assign(S.value,s.data),Object.assign(e.value,s.data.scheduleConfig),e.value.startDate&&e.value.endDate&&(e.value.dateRange=[e.value.startDate,e.value.endDate]),e.value.repeatType==="weekly"&&(e.value.weekdays=e.value.days||[]),e.value.repeatType==="monthly"&&(e.value.monthDays=e.value.days||[]),e.value.specificDays||(e.value.specificDays=[]))}catch{}})},V=()=>{e.value={dateRange:[],weekdays:[],monthDays:[],startDate:"",endDate:"",repeatType:"daily",lastDayOfMonth:!1,days:[],specificDays:[]},R.value=!1},ee=()=>{E.value.validate(async(a,t)=>{if(a){let s={startDate:e.value.dateRange[0],endDate:e.value.dateRange[1],repeatType:e.value.repeatType,days:[],specificDays:[],lastDayOfMonth:e.value.lastDayOfMonth};s.repeatType==="monthly"&&(s.days=e.value.monthDays),s.repeatType==="weekly"&&(s.days=e.value.weekdays),s.repeatType==="specific"&&(s.specificDays=e.value.specificDays),S.value.scheduleConfig=s;try{let u=await De(S.value);u.result&&u.result.resultCode==="0"&&(V(),x.value&&x.value())}catch{}}})};return U({openDrawer:Z}),(a,t)=>{const s=D("el-date-picker"),u=D("el-form-item"),i=D("el-radio"),p=D("el-option"),l=D("el-select"),f=D("el-checkbox"),d=D("el-button"),m=D("el-radio-group"),g=D("el-form"),w=D("el-drawer");return h(),ce(w,{modelValue:R.value,"onUpdate:modelValue":t[6]||(t[6]=n=>R.value=n),onClose:V,class:"schedule-drawer",size:"1456","destroy-on-close":"","close-on-click-modal":!1},{header:r(()=>[o("span",ge,b($(q)),1)]),default:r(()=>[o("div",_e,[c(g,{ref_key:"formRef",ref:E,onSubmit:t[5]||(t[5]=ie(()=>{},["prevent"])),rules:N,model:e.value,class:"form-inline"},{default:r(()=>[o("div",we,[o("div",be,[c(u,{prop:"dateRange"},{default:r(()=>[c(s,{modelValue:e.value.dateRange,"onUpdate:modelValue":t[0]||(t[0]=n=>e.value.dateRange=n),onChange:L,style:{width:"240px"},"unlink-panels":"",type:"daterange","range-separator":"\u81F3","disabled-date":j,"start-placeholder":"\u5F00\u59CB\u65E5\u671F","end-placeholder":"\u7ED3\u675F\u65E5\u671F",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD"},null,8,["modelValue"])]),_:1})]),o("div",ke,[c(m,{modelValue:e.value.repeatType,"onUpdate:modelValue":t[4]||(t[4]=n=>e.value.repeatType=n),onChange:H},{default:r(()=>[c(i,{value:"daily"},{default:r(()=>[v("\u6BCF\u5929")]),_:1}),c(i,{value:"weekly"},{default:r(()=>[v("\u6BCF\u5468 "),c(l,{modelValue:e.value.weekdays,"onUpdate:modelValue":t[1]||(t[1]=n=>e.value.weekdays=n),disabled:e.value.repeatType!="weekly",placeholder:"\u8BF7\u9009\u62E9",multiple:"","collapse-tags":"",style:{width:"240px"}},{default:r(()=>[(h(),_(M,null,C(z,n=>c(p,{key:n.value,label:n.label,value:n.value},null,8,["label","value"])),64))]),_:1},8,["modelValue","disabled"])]),_:1}),c(i,{value:"monthly"},{default:r(()=>[v("\u6BCF\u6708 "),c(l,{modelValue:e.value.monthDays,"onUpdate:modelValue":t[3]||(t[3]=n=>e.value.monthDays=n),disabled:e.value.repeatType!="monthly",placeholder:"\u8BF7\u9009\u62E9",multiple:"","collapse-tags":"",style:{width:"240px"}},{header:r(()=>[o("div",Fe,[c(f,{modelValue:e.value.lastDayOfMonth,"onUpdate:modelValue":t[2]||(t[2]=n=>e.value.lastDayOfMonth=n)},{default:r(()=>[v(" \u6700\u540E\u4E00\u5929 ")]),_:1},8,["modelValue"]),c(d,{type:"primary",link:"",onClick:J},{default:r(()=>[v(b("\u5168\u9009"))]),_:1})])]),default:r(()=>[(h(),_(M,null,C(31,n=>c(p,{key:n,label:n+"\u53F7",value:n},null,8,["label","value"])),64))]),_:1},8,["modelValue","disabled"])]),_:1}),c(i,{value:"specific"},{default:r(()=>[v("\u6307\u5B9A\u65E5\u671F")]),_:1})]),_:1},8,["modelValue"])])])]),_:1},8,["rules","model"]),o("div",Ce,[o("div",Me,[c(d,{onClick:K},{default:r(()=>[v("<")]),_:1}),o("h3",null,b(F.value)+"\u5E74",1),c(d,{onClick:P},{default:r(()=>[v(">")]),_:1})]),o("div",Te,[(h(!0),_(M,null,C($(X),n=>(h(),_("div",{class:"calendar-month",key:n.month},[o("table",null,[o("caption",null,b(n.month)+"\u6708",1),Re,o("tbody",null,[(h(!0),_(M,null,C(n.weeks,(ae,te)=>(h(),_("tr",{key:te},[(h(!0),_(M,null,C(ae,(y,le)=>(h(),_("td",{key:le,class:fe({selected:y&&A(y.fullDate),"disabled-day":y&&B(y.fullDate),"calendar-day":y,"clickable-cell":y}),onClick:xe=>y&&Q(y)},b(y?y.dayOfMonth:""),11,Ee))),128))]))),128))])])]))),128))])])])]),footer:r(()=>[o("div",Se,[c(d,{type:"primary",onClick:ee},{default:r(()=>[v(b(a.$t("common.button.queding")),1)]),_:1}),c(d,{onClick:V},{default:r(()=>[v(b(a.$t("common.button.quxiao")),1)]),_:1})])]),_:1},8,["modelValue"])}}});const Ae=he(Oe,[["__scopeId","data-v-5fbbcb4c"]]);export{Ae as default};
