var qr=Object.defineProperty;var Pr=(r,t,e)=>t in r?qr(r,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):r[t]=e;var we=(r,t,e)=>(Pr(r,typeof t!="symbol"?t+"":t,e),e);import{v as Nr}from"./main-dd669ad8.js";var Ct={exports:{}};(function(r,t){(function(){var e,i=0xdeadbeefcafe,n=(i&16777215)==15715070;function s(o,h,f){o!=null&&(typeof o=="number"?this.fromNumber(o,h,f):h==null&&typeof o!="string"?this.fromString(o,256):this.fromString(o,h))}function u(){return new s(null)}function a(o,h,f,c,d,y){for(;--y>=0;){var b=h*this[o++]+f[c]+d;d=Math.floor(b/67108864),f[c++]=b&67108863}return d}function l(o,h,f,c,d,y){for(var b=h&32767,T=h>>15;--y>=0;){var V=this[o]&32767,H=this[o++]>>15,$=T*V+H*b;V=b*V+(($&32767)<<15)+f[c]+(d&1073741823),d=(V>>>30)+($>>>15)+T*H+(d>>>30),f[c++]=V&1073741823}return d}function v(o,h,f,c,d,y){for(var b=h&16383,T=h>>14;--y>=0;){var V=this[o]&16383,H=this[o++]>>14,$=T*V+H*b;V=b*V+(($&16383)<<14)+f[c]+d,d=(V>>28)+($>>14)+T*H,f[c++]=V&268435455}return d}var g=typeof navigator<"u";g&&n&&navigator.appName=="Microsoft Internet Explorer"?(s.prototype.am=l,e=30):g&&n&&navigator.appName!="Netscape"?(s.prototype.am=a,e=26):(s.prototype.am=v,e=28),s.prototype.DB=e,s.prototype.DM=(1<<e)-1,s.prototype.DV=1<<e;var x=52;s.prototype.FV=Math.pow(2,x),s.prototype.F1=x-e,s.prototype.F2=2*e-x;var m="0123456789abcdefghijklmnopqrstuvwxyz",w=new Array,S,D;for(S="0".charCodeAt(0),D=0;D<=9;++D)w[S++]=D;for(S="a".charCodeAt(0),D=10;D<36;++D)w[S++]=D;for(S="A".charCodeAt(0),D=10;D<36;++D)w[S++]=D;function I(o){return m.charAt(o)}function q(o,h){var f=w[o.charCodeAt(h)];return f==null?-1:f}function O(o){for(var h=this.t-1;h>=0;--h)o[h]=this[h];o.t=this.t,o.s=this.s}function L(o){this.t=1,this.s=o<0?-1:0,o>0?this[0]=o:o<-1?this[0]=o+this.DV:this.t=0}function N(o){var h=u();return h.fromInt(o),h}function it(o,h){var f;if(h==16)f=4;else if(h==8)f=3;else if(h==256)f=8;else if(h==2)f=1;else if(h==32)f=5;else if(h==4)f=2;else{this.fromRadix(o,h);return}this.t=0,this.s=0;for(var c=o.length,d=!1,y=0;--c>=0;){var b=f==8?o[c]&255:q(o,c);if(b<0){o.charAt(c)=="-"&&(d=!0);continue}d=!1,y==0?this[this.t++]=b:y+f>this.DB?(this[this.t-1]|=(b&(1<<this.DB-y)-1)<<y,this[this.t++]=b>>this.DB-y):this[this.t-1]|=b<<y,y+=f,y>=this.DB&&(y-=this.DB)}f==8&&(o[0]&128)!=0&&(this.s=-1,y>0&&(this[this.t-1]|=(1<<this.DB-y)-1<<y)),this.clamp(),d&&s.ZERO.subTo(this,this)}function B(){for(var o=this.s&this.DM;this.t>0&&this[this.t-1]==o;)--this.t}function rt(o){if(this.s<0)return"-"+this.negate().toString(o);var h;if(o==16)h=4;else if(o==8)h=3;else if(o==2)h=1;else if(o==32)h=5;else if(o==4)h=2;else return this.toRadix(o);var f=(1<<h)-1,c,d=!1,y="",b=this.t,T=this.DB-b*this.DB%h;if(b-- >0)for(T<this.DB&&(c=this[b]>>T)>0&&(d=!0,y=I(c));b>=0;)T<h?(c=(this[b]&(1<<T)-1)<<h-T,c|=this[--b]>>(T+=this.DB-h)):(c=this[b]>>(T-=h)&f,T<=0&&(T+=this.DB,--b)),c>0&&(d=!0),d&&(y+=I(c));return d?y:"0"}function nt(){var o=u();return s.ZERO.subTo(this,o),o}function Z(){return this.s<0?this.negate():this}function ht(o){var h=this.s-o.s;if(h!=0)return h;var f=this.t;if(h=f-o.t,h!=0)return this.s<0?-h:h;for(;--f>=0;)if((h=this[f]-o[f])!=0)return h;return 0}function E(o){var h=1,f;return(f=o>>>16)!=0&&(o=f,h+=16),(f=o>>8)!=0&&(o=f,h+=8),(f=o>>4)!=0&&(o=f,h+=4),(f=o>>2)!=0&&(o=f,h+=2),(f=o>>1)!=0&&(o=f,h+=1),h}function ee(){return this.t<=0?0:this.DB*(this.t-1)+E(this[this.t-1]^this.s&this.DM)}function wt(o,h){var f;for(f=this.t-1;f>=0;--f)h[f+o]=this[f];for(f=o-1;f>=0;--f)h[f]=0;h.t=this.t+o,h.s=this.s}function ei(o,h){for(var f=o;f<this.t;++f)h[f-o]=this[f];h.t=Math.max(this.t-o,0),h.s=this.s}function ii(o,h){var f=o%this.DB,c=this.DB-f,d=(1<<c)-1,y=Math.floor(o/this.DB),b=this.s<<f&this.DM,T;for(T=this.t-1;T>=0;--T)h[T+y+1]=this[T]>>c|b,b=(this[T]&d)<<f;for(T=y-1;T>=0;--T)h[T]=0;h[y]=b,h.t=this.t+y+1,h.s=this.s,h.clamp()}function ri(o,h){h.s=this.s;var f=Math.floor(o/this.DB);if(f>=this.t){h.t=0;return}var c=o%this.DB,d=this.DB-c,y=(1<<c)-1;h[0]=this[f]>>c;for(var b=f+1;b<this.t;++b)h[b-f-1]|=(this[b]&y)<<d,h[b-f]=this[b]>>c;c>0&&(h[this.t-f-1]|=(this.s&y)<<d),h.t=this.t-f,h.clamp()}function ni(o,h){for(var f=0,c=0,d=Math.min(o.t,this.t);f<d;)c+=this[f]-o[f],h[f++]=c&this.DM,c>>=this.DB;if(o.t<this.t){for(c-=o.s;f<this.t;)c+=this[f],h[f++]=c&this.DM,c>>=this.DB;c+=this.s}else{for(c+=this.s;f<o.t;)c-=o[f],h[f++]=c&this.DM,c>>=this.DB;c-=o.s}h.s=c<0?-1:0,c<-1?h[f++]=this.DV+c:c>0&&(h[f++]=c),h.t=f,h.clamp()}function si(o,h){var f=this.abs(),c=o.abs(),d=f.t;for(h.t=d+c.t;--d>=0;)h[d]=0;for(d=0;d<c.t;++d)h[d+f.t]=f.am(0,c[d],h,d,0,f.t);h.s=0,h.clamp(),this.s!=o.s&&s.ZERO.subTo(h,h)}function oi(o){for(var h=this.abs(),f=o.t=2*h.t;--f>=0;)o[f]=0;for(f=0;f<h.t-1;++f){var c=h.am(f,h[f],o,2*f,0,1);(o[f+h.t]+=h.am(f+1,2*h[f],o,2*f+1,c,h.t-f-1))>=h.DV&&(o[f+h.t]-=h.DV,o[f+h.t+1]=1)}o.t>0&&(o[o.t-1]+=h.am(f,h[f],o,2*f,0,1)),o.s=0,o.clamp()}function hi(o,h,f){var c=o.abs();if(!(c.t<=0)){var d=this.abs();if(d.t<c.t){h!=null&&h.fromInt(0),f!=null&&this.copyTo(f);return}f==null&&(f=u());var y=u(),b=this.s,T=o.s,V=this.DB-E(c[c.t-1]);V>0?(c.lShiftTo(V,y),d.lShiftTo(V,f)):(c.copyTo(y),d.copyTo(f));var H=y.t,$=y[H-1];if($!=0){var K=$*(1<<this.F1)+(H>1?y[H-2]>>this.F2:0),st=this.FV/K,Lt=(1<<this.F1)/K,J=1<<this.F2,W=f.t,_t=W-H,ft=h==null?u():h;for(y.dlShiftTo(_t,ft),f.compareTo(ft)>=0&&(f[f.t++]=1,f.subTo(ft,f)),s.ONE.dlShiftTo(H,ft),ft.subTo(y,y);y.t<H;)y[y.t++]=0;for(;--_t>=0;){var se=f[--W]==$?this.DM:Math.floor(f[W]*st+(f[W-1]+J)*Lt);if((f[W]+=y.am(0,se,f,_t,0,H))<se)for(y.dlShiftTo(_t,ft),f.subTo(ft,f);f[W]<--se;)f.subTo(ft,f)}h!=null&&(f.drShiftTo(H,h),b!=T&&s.ZERO.subTo(h,h)),f.t=H,f.clamp(),V>0&&f.rShiftTo(V,f),b<0&&s.ZERO.subTo(f,f)}}}function fi(o){var h=u();return this.abs().divRemTo(o,null,h),this.s<0&&h.compareTo(s.ZERO)>0&&o.subTo(h,h),h}function vt(o){this.m=o}function ui(o){return o.s<0||o.compareTo(this.m)>=0?o.mod(this.m):o}function ai(o){return o}function li(o){o.divRemTo(this.m,null,o)}function ci(o,h,f){o.multiplyTo(h,f),this.reduce(f)}function pi(o,h){o.squareTo(h),this.reduce(h)}vt.prototype.convert=ui,vt.prototype.revert=ai,vt.prototype.reduce=li,vt.prototype.mulTo=ci,vt.prototype.sqrTo=pi;function di(){if(this.t<1)return 0;var o=this[0];if((o&1)==0)return 0;var h=o&3;return h=h*(2-(o&15)*h)&15,h=h*(2-(o&255)*h)&255,h=h*(2-((o&65535)*h&65535))&65535,h=h*(2-o*h%this.DV)%this.DV,h>0?this.DV-h:-h}function yt(o){this.m=o,this.mp=o.invDigit(),this.mpl=this.mp&32767,this.mph=this.mp>>15,this.um=(1<<o.DB-15)-1,this.mt2=2*o.t}function gi(o){var h=u();return o.abs().dlShiftTo(this.m.t,h),h.divRemTo(this.m,null,h),o.s<0&&h.compareTo(s.ZERO)>0&&this.m.subTo(h,h),h}function vi(o){var h=u();return o.copyTo(h),this.reduce(h),h}function yi(o){for(;o.t<=this.mt2;)o[o.t++]=0;for(var h=0;h<this.m.t;++h){var f=o[h]&32767,c=f*this.mpl+((f*this.mph+(o[h]>>15)*this.mpl&this.um)<<15)&o.DM;for(f=h+this.m.t,o[f]+=this.m.am(0,c,o,h,0,this.m.t);o[f]>=o.DV;)o[f]-=o.DV,o[++f]++}o.clamp(),o.drShiftTo(this.m.t,o),o.compareTo(this.m)>=0&&o.subTo(this.m,o)}function xi(o,h){o.squareTo(h),this.reduce(h)}function mi(o,h,f){o.multiplyTo(h,f),this.reduce(f)}yt.prototype.convert=gi,yt.prototype.revert=vi,yt.prototype.reduce=yi,yt.prototype.mulTo=mi,yt.prototype.sqrTo=xi;function bi(){return(this.t>0?this[0]&1:this.s)==0}function Ti(o,h){if(o>4294967295||o<1)return s.ONE;var f=u(),c=u(),d=h.convert(this),y=E(o)-1;for(d.copyTo(f);--y>=0;)if(h.sqrTo(f,c),(o&1<<y)>0)h.mulTo(c,d,f);else{var b=f;f=c,c=b}return h.revert(f)}function wi(o,h){var f;return o<256||h.isEven()?f=new vt(h):f=new yt(h),this.exp(o,f)}s.prototype.copyTo=O,s.prototype.fromInt=L,s.prototype.fromString=it,s.prototype.clamp=B,s.prototype.dlShiftTo=wt,s.prototype.drShiftTo=ei,s.prototype.lShiftTo=ii,s.prototype.rShiftTo=ri,s.prototype.subTo=ni,s.prototype.multiplyTo=si,s.prototype.squareTo=oi,s.prototype.divRemTo=hi,s.prototype.invDigit=di,s.prototype.isEven=bi,s.prototype.exp=Ti,s.prototype.toString=rt,s.prototype.negate=nt,s.prototype.abs=Z,s.prototype.compareTo=ht,s.prototype.bitLength=ee,s.prototype.mod=fi,s.prototype.modPowInt=wi,s.ZERO=N(0),s.ONE=N(1);function Si(){var o=u();return this.copyTo(o),o}function Ei(){if(this.s<0){if(this.t==1)return this[0]-this.DV;if(this.t==0)return-1}else{if(this.t==1)return this[0];if(this.t==0)return 0}return(this[1]&(1<<32-this.DB)-1)<<this.DB|this[0]}function Di(){return this.t==0?this.s:this[0]<<24>>24}function Bi(){return this.t==0?this.s:this[0]<<16>>16}function Ri(o){return Math.floor(Math.LN2*this.DB/Math.log(o))}function Fi(){return this.s<0?-1:this.t<=0||this.t==1&&this[0]<=0?0:1}function Ai(o){if(o==null&&(o=10),this.signum()==0||o<2||o>36)return"0";var h=this.chunkSize(o),f=Math.pow(o,h),c=N(f),d=u(),y=u(),b="";for(this.divRemTo(c,d,y);d.signum()>0;)b=(f+y.intValue()).toString(o).substr(1)+b,d.divRemTo(c,d,y);return y.intValue().toString(o)+b}function Ii(o,h){this.fromInt(0),h==null&&(h=10);for(var f=this.chunkSize(h),c=Math.pow(h,f),d=!1,y=0,b=0,T=0;T<o.length;++T){var V=q(o,T);if(V<0){o.charAt(T)=="-"&&this.signum()==0&&(d=!0);continue}b=h*b+V,++y>=f&&(this.dMultiply(c),this.dAddOffset(b,0),y=0,b=0)}y>0&&(this.dMultiply(Math.pow(h,y)),this.dAddOffset(b,0)),d&&s.ZERO.subTo(this,this)}function Oi(o,h,f){if(typeof h=="number")if(o<2)this.fromInt(1);else for(this.fromNumber(o,f),this.testBit(o-1)||this.bitwiseTo(s.ONE.shiftLeft(o-1),ie,this),this.isEven()&&this.dAddOffset(1,0);!this.isProbablePrime(h);)this.dAddOffset(2,0),this.bitLength()>o&&this.subTo(s.ONE.shiftLeft(o-1),this);else{var c=new Array,d=o&7;c.length=(o>>3)+1,h.nextBytes(c),d>0?c[0]&=(1<<d)-1:c[0]=0,this.fromString(c,256)}}function Vi(){var o=this.t,h=new Array;h[0]=this.s;var f=this.DB-o*this.DB%8,c,d=0;if(o-- >0)for(f<this.DB&&(c=this[o]>>f)!=(this.s&this.DM)>>f&&(h[d++]=c|this.s<<this.DB-f);o>=0;)f<8?(c=(this[o]&(1<<f)-1)<<8-f,c|=this[--o]>>(f+=this.DB-8)):(c=this[o]>>(f-=8)&255,f<=0&&(f+=this.DB,--o)),(c&128)!=0&&(c|=-256),d==0&&(this.s&128)!=(c&128)&&++d,(d>0||c!=this.s)&&(h[d++]=c);return h}function qi(o){return this.compareTo(o)==0}function Pi(o){return this.compareTo(o)<0?this:o}function Ni(o){return this.compareTo(o)>0?this:o}function Mi(o,h,f){var c,d,y=Math.min(o.t,this.t);for(c=0;c<y;++c)f[c]=h(this[c],o[c]);if(o.t<this.t){for(d=o.s&this.DM,c=y;c<this.t;++c)f[c]=h(this[c],d);f.t=this.t}else{for(d=this.s&this.DM,c=y;c<o.t;++c)f[c]=h(d,o[c]);f.t=o.t}f.s=h(this.s,o.s),f.clamp()}function Ci(o,h){return o&h}function Hi(o){var h=u();return this.bitwiseTo(o,Ci,h),h}function ie(o,h){return o|h}function Li(o){var h=u();return this.bitwiseTo(o,ie,h),h}function ge(o,h){return o^h}function _i(o){var h=u();return this.bitwiseTo(o,ge,h),h}function ve(o,h){return o&~h}function ji(o){var h=u();return this.bitwiseTo(o,ve,h),h}function ki(){for(var o=u(),h=0;h<this.t;++h)o[h]=this.DM&~this[h];return o.t=this.t,o.s=~this.s,o}function Ui(o){var h=u();return o<0?this.rShiftTo(-o,h):this.lShiftTo(o,h),h}function Ki(o){var h=u();return o<0?this.lShiftTo(-o,h):this.rShiftTo(o,h),h}function zi(o){if(o==0)return-1;var h=0;return(o&65535)==0&&(o>>=16,h+=16),(o&255)==0&&(o>>=8,h+=8),(o&15)==0&&(o>>=4,h+=4),(o&3)==0&&(o>>=2,h+=2),(o&1)==0&&++h,h}function Zi(){for(var o=0;o<this.t;++o)if(this[o]!=0)return o*this.DB+zi(this[o]);return this.s<0?this.t*this.DB:-1}function $i(o){for(var h=0;o!=0;)o&=o-1,++h;return h}function Gi(){for(var o=0,h=this.s&this.DM,f=0;f<this.t;++f)o+=$i(this[f]^h);return o}function Yi(o){var h=Math.floor(o/this.DB);return h>=this.t?this.s!=0:(this[h]&1<<o%this.DB)!=0}function Xi(o,h){var f=s.ONE.shiftLeft(o);return this.bitwiseTo(f,h,f),f}function Ji(o){return this.changeBit(o,ie)}function Wi(o){return this.changeBit(o,ve)}function Qi(o){return this.changeBit(o,ge)}function tr(o,h){for(var f=0,c=0,d=Math.min(o.t,this.t);f<d;)c+=this[f]+o[f],h[f++]=c&this.DM,c>>=this.DB;if(o.t<this.t){for(c+=o.s;f<this.t;)c+=this[f],h[f++]=c&this.DM,c>>=this.DB;c+=this.s}else{for(c+=this.s;f<o.t;)c+=o[f],h[f++]=c&this.DM,c>>=this.DB;c+=o.s}h.s=c<0?-1:0,c>0?h[f++]=c:c<-1&&(h[f++]=this.DV+c),h.t=f,h.clamp()}function er(o){var h=u();return this.addTo(o,h),h}function ir(o){var h=u();return this.subTo(o,h),h}function rr(o){var h=u();return this.multiplyTo(o,h),h}function nr(){var o=u();return this.squareTo(o),o}function sr(o){var h=u();return this.divRemTo(o,h,null),h}function or(o){var h=u();return this.divRemTo(o,null,h),h}function hr(o){var h=u(),f=u();return this.divRemTo(o,h,f),new Array(h,f)}function fr(o){this[this.t]=this.am(0,o-1,this,0,0,this.t),++this.t,this.clamp()}function ur(o,h){if(o!=0){for(;this.t<=h;)this[this.t++]=0;for(this[h]+=o;this[h]>=this.DV;)this[h]-=this.DV,++h>=this.t&&(this[this.t++]=0),++this[h]}}function qt(){}function ye(o){return o}function ar(o,h,f){o.multiplyTo(h,f)}function lr(o,h){o.squareTo(h)}qt.prototype.convert=ye,qt.prototype.revert=ye,qt.prototype.mulTo=ar,qt.prototype.sqrTo=lr;function cr(o){return this.exp(o,new qt)}function pr(o,h,f){var c=Math.min(this.t+o.t,h);for(f.s=0,f.t=c;c>0;)f[--c]=0;var d;for(d=f.t-this.t;c<d;++c)f[c+this.t]=this.am(0,o[c],f,c,0,this.t);for(d=Math.min(o.t,h);c<d;++c)this.am(0,o[c],f,c,0,h-c);f.clamp()}function dr(o,h,f){--h;var c=f.t=this.t+o.t-h;for(f.s=0;--c>=0;)f[c]=0;for(c=Math.max(h-this.t,0);c<o.t;++c)f[this.t+c-h]=this.am(h-c,o[c],f,0,0,this.t+c-h);f.clamp(),f.drShiftTo(1,f)}function xt(o){this.r2=u(),this.q3=u(),s.ONE.dlShiftTo(2*o.t,this.r2),this.mu=this.r2.divide(o),this.m=o}function gr(o){if(o.s<0||o.t>2*this.m.t)return o.mod(this.m);if(o.compareTo(this.m)<0)return o;var h=u();return o.copyTo(h),this.reduce(h),h}function vr(o){return o}function yr(o){for(o.drShiftTo(this.m.t-1,this.r2),o.t>this.m.t+1&&(o.t=this.m.t+1,o.clamp()),this.mu.multiplyUpperTo(this.r2,this.m.t+1,this.q3),this.m.multiplyLowerTo(this.q3,this.m.t+1,this.r2);o.compareTo(this.r2)<0;)o.dAddOffset(1,this.m.t+1);for(o.subTo(this.r2,o);o.compareTo(this.m)>=0;)o.subTo(this.m,o)}function xr(o,h){o.squareTo(h),this.reduce(h)}function mr(o,h,f){o.multiplyTo(h,f),this.reduce(f)}xt.prototype.convert=gr,xt.prototype.revert=vr,xt.prototype.reduce=yr,xt.prototype.mulTo=mr,xt.prototype.sqrTo=xr;function br(o,h){var f=o.bitLength(),c,d=N(1),y;if(f<=0)return d;f<18?c=1:f<48?c=3:f<144?c=4:f<768?c=5:c=6,f<8?y=new vt(h):h.isEven()?y=new xt(h):y=new yt(h);var b=new Array,T=3,V=c-1,H=(1<<c)-1;if(b[1]=y.convert(this),c>1){var $=u();for(y.sqrTo(b[1],$);T<=H;)b[T]=u(),y.mulTo($,b[T-2],b[T]),T+=2}var K=o.t-1,st,Lt=!0,J=u(),W;for(f=E(o[K])-1;K>=0;){for(f>=V?st=o[K]>>f-V&H:(st=(o[K]&(1<<f+1)-1)<<V-f,K>0&&(st|=o[K-1]>>this.DB+f-V)),T=c;(st&1)==0;)st>>=1,--T;if((f-=T)<0&&(f+=this.DB,--K),Lt)b[st].copyTo(d),Lt=!1;else{for(;T>1;)y.sqrTo(d,J),y.sqrTo(J,d),T-=2;T>0?y.sqrTo(d,J):(W=d,d=J,J=W),y.mulTo(J,b[st],d)}for(;K>=0&&(o[K]&1<<f)==0;)y.sqrTo(d,J),W=d,d=J,J=W,--f<0&&(f=this.DB-1,--K)}return y.revert(d)}function Tr(o){var h=this.s<0?this.negate():this.clone(),f=o.s<0?o.negate():o.clone();if(h.compareTo(f)<0){var c=h;h=f,f=c}var d=h.getLowestSetBit(),y=f.getLowestSetBit();if(y<0)return h;for(d<y&&(y=d),y>0&&(h.rShiftTo(y,h),f.rShiftTo(y,f));h.signum()>0;)(d=h.getLowestSetBit())>0&&h.rShiftTo(d,h),(d=f.getLowestSetBit())>0&&f.rShiftTo(d,f),h.compareTo(f)>=0?(h.subTo(f,h),h.rShiftTo(1,h)):(f.subTo(h,f),f.rShiftTo(1,f));return y>0&&f.lShiftTo(y,f),f}function wr(o){if(o<=0)return 0;var h=this.DV%o,f=this.s<0?o-1:0;if(this.t>0)if(h==0)f=this[0]%o;else for(var c=this.t-1;c>=0;--c)f=(h*f+this[c])%o;return f}function Sr(o){var h=o.isEven();if(this.isEven()&&h||o.signum()==0)return s.ZERO;for(var f=o.clone(),c=this.clone(),d=N(1),y=N(0),b=N(0),T=N(1);f.signum()!=0;){for(;f.isEven();)f.rShiftTo(1,f),h?((!d.isEven()||!y.isEven())&&(d.addTo(this,d),y.subTo(o,y)),d.rShiftTo(1,d)):y.isEven()||y.subTo(o,y),y.rShiftTo(1,y);for(;c.isEven();)c.rShiftTo(1,c),h?((!b.isEven()||!T.isEven())&&(b.addTo(this,b),T.subTo(o,T)),b.rShiftTo(1,b)):T.isEven()||T.subTo(o,T),T.rShiftTo(1,T);f.compareTo(c)>=0?(f.subTo(c,f),h&&d.subTo(b,d),y.subTo(T,y)):(c.subTo(f,c),h&&b.subTo(d,b),T.subTo(y,T))}if(c.compareTo(s.ONE)!=0)return s.ZERO;if(T.compareTo(o)>=0)return T.subtract(o);if(T.signum()<0)T.addTo(o,T);else return T;return T.signum()<0?T.add(o):T}var _=[2,3,5,7,11,13,17,19,23,29,31,37,41,43,47,53,59,61,67,71,73,79,83,89,97,101,103,107,109,113,127,131,137,139,149,151,157,163,167,173,179,181,191,193,197,199,211,223,227,229,233,239,241,251,257,263,269,271,277,281,283,293,307,311,313,317,331,337,347,349,353,359,367,373,379,383,389,397,401,409,419,421,431,433,439,443,449,457,461,463,467,479,487,491,499,503,509,521,523,541,547,557,563,569,571,577,587,593,599,601,607,613,617,619,631,641,643,647,653,659,661,673,677,683,691,701,709,719,727,733,739,743,751,757,761,769,773,787,797,809,811,821,823,827,829,839,853,857,859,863,877,881,883,887,907,911,919,929,937,941,947,953,967,971,977,983,991,997],Er=(1<<26)/_[_.length-1];function Dr(o){var h,f=this.abs();if(f.t==1&&f[0]<=_[_.length-1]){for(h=0;h<_.length;++h)if(f[0]==_[h])return!0;return!1}if(f.isEven())return!1;for(h=1;h<_.length;){for(var c=_[h],d=h+1;d<_.length&&c<Er;)c*=_[d++];for(c=f.modInt(c);h<d;)if(c%_[h++]==0)return!1}return f.millerRabin(o)}function Br(o){var h=this.subtract(s.ONE),f=h.getLowestSetBit();if(f<=0)return!1;var c=h.shiftRight(f);o=o+1>>1,o>_.length&&(o=_.length);for(var d=u(),y=0;y<o;++y){d.fromInt(_[Math.floor(Math.random()*_.length)]);var b=d.modPow(c,this);if(b.compareTo(s.ONE)!=0&&b.compareTo(h)!=0){for(var T=1;T++<f&&b.compareTo(h)!=0;)if(b=b.modPowInt(2,this),b.compareTo(s.ONE)==0)return!1;if(b.compareTo(h)!=0)return!1}}return!0}s.prototype.chunkSize=Ri,s.prototype.toRadix=Ai,s.prototype.fromRadix=Ii,s.prototype.fromNumber=Oi,s.prototype.bitwiseTo=Mi,s.prototype.changeBit=Xi,s.prototype.addTo=tr,s.prototype.dMultiply=fr,s.prototype.dAddOffset=ur,s.prototype.multiplyLowerTo=pr,s.prototype.multiplyUpperTo=dr,s.prototype.modInt=wr,s.prototype.millerRabin=Br,s.prototype.clone=Si,s.prototype.intValue=Ei,s.prototype.byteValue=Di,s.prototype.shortValue=Bi,s.prototype.signum=Fi,s.prototype.toByteArray=Vi,s.prototype.equals=qi,s.prototype.min=Pi,s.prototype.max=Ni,s.prototype.and=Hi,s.prototype.or=Li,s.prototype.xor=_i,s.prototype.andNot=ji,s.prototype.not=ki,s.prototype.shiftLeft=Ui,s.prototype.shiftRight=Ki,s.prototype.getLowestSetBit=Zi,s.prototype.bitCount=Gi,s.prototype.testBit=Yi,s.prototype.setBit=Ji,s.prototype.clearBit=Wi,s.prototype.flipBit=Qi,s.prototype.add=er,s.prototype.subtract=ir,s.prototype.multiply=rr,s.prototype.divide=sr,s.prototype.remainder=or,s.prototype.divideAndRemainder=hr,s.prototype.modPow=br,s.prototype.modInverse=Sr,s.prototype.pow=cr,s.prototype.gcd=Tr,s.prototype.isProbablePrime=Dr,s.prototype.square=nr,s.prototype.Barrett=xt;var Ht,U,C;function Rr(o){U[C++]^=o&255,U[C++]^=o>>8&255,U[C++]^=o>>16&255,U[C++]^=o>>24&255,C>=ne&&(C-=ne)}function xe(){Rr(new Date().getTime())}if(U==null){U=new Array,C=0;var X;if(typeof window<"u"&&window.crypto){if(window.crypto.getRandomValues){var me=new Uint8Array(32);for(window.crypto.getRandomValues(me),X=0;X<32;++X)U[C++]=me[X]}else if(navigator.appName=="Netscape"&&navigator.appVersion<"5"){var be=window.crypto.random(32);for(X=0;X<be.length;++X)U[C++]=be.charCodeAt(X)&255}}for(;C<ne;)X=Math.floor(65536*Math.random()),U[C++]=X>>>8,U[C++]=X&255;C=0,xe()}function Fr(){if(Ht==null){for(xe(),Ht=Vr(),Ht.init(U),C=0;C<U.length;++C)U[C]=0;C=0}return Ht.next()}function Ar(o){var h;for(h=0;h<o.length;++h)o[h]=Fr()}function Te(){}Te.prototype.nextBytes=Ar;function re(){this.i=0,this.j=0,this.S=new Array}function Ir(o){var h,f,c;for(h=0;h<256;++h)this.S[h]=h;for(f=0,h=0;h<256;++h)f=f+this.S[h]+o[h%o.length]&255,c=this.S[h],this.S[h]=this.S[f],this.S[f]=c;this.i=0,this.j=0}function Or(){var o;return this.i=this.i+1&255,this.j=this.j+this.S[this.i]&255,o=this.S[this.i],this.S[this.i]=this.S[this.j],this.S[this.j]=o,this.S[o+this.S[this.i]&255]}re.prototype.init=Ir,re.prototype.next=Or;function Vr(){return new re}var ne=256;r.exports={default:s,BigInteger:s,SecureRandom:Te}}).call(Nr)})(Ct);const{BigInteger:It}=Ct.exports;function Mr(r){let t=r.toString(16);if(t[0]!=="-")t.length%2===1?t="0"+t:t.match(/^[0-7]/)||(t="00"+t);else{t=t.substr(1);let e=t.length;e%2===1?e+=1:t.match(/^[0-7]/)||(e+=2);let i="";for(let n=0;n<e;n++)i+="f";i=new It(i,16),t=i.xor(r).add(It.ONE),t=t.toString(16).replace(/^-/,"")}return t}class je{constructor(){this.tlv=null,this.t="00",this.l="00",this.v=""}getEncodedHex(){return this.tlv||(this.v=this.getValue(),this.l=this.getLength(),this.tlv=this.t+this.l+this.v),this.tlv}getLength(){const t=this.v.length/2;let e=t.toString(16);return e.length%2===1&&(e="0"+e),t<128?e:(128+e.length/2).toString(16)+e}getValue(){return""}}class Se extends je{constructor(t){super(),this.t="02",t&&(this.v=Mr(t))}getValue(){return this.v}}class Cr extends je{constructor(t){super(),this.t="30",this.asn1Array=t}getValue(){return this.v=this.asn1Array.map(t=>t.getEncodedHex()).join(""),this.v}}function ke(r,t){return+r[t+2]<8?1:+r.substr(t+2,2)&127+1}function Ee(r,t){const e=ke(r,t),i=r.substr(t+2,e*2);return i?(+i[0]<8?new It(i,16):new It(i.substr(2),16)).intValue():-1}function oe(r,t){const e=ke(r,t);return t+(e+1)*2}var Hr={encodeDer(r,t){const e=new Se(r),i=new Se(t);return new Cr([e,i]).getEncodedHex()},decodeDer(r){const t=oe(r,0),e=oe(r,t),i=Ee(r,t),n=r.substr(e,i*2),s=e+n.length,u=oe(r,s),a=Ee(r,s),l=r.substr(u,a*2),v=new It(n,16),g=new It(l,16);return{r:v,s:g}}};const{BigInteger:k}=Ct.exports,De=new k("2"),Be=new k("3");class at{constructor(t,e){this.x=e,this.q=t}equals(t){return t===this?!0:this.q.equals(t.q)&&this.x.equals(t.x)}toBigInteger(){return this.x}negate(){return new at(this.q,this.x.negate().mod(this.q))}add(t){return new at(this.q,this.x.add(t.toBigInteger()).mod(this.q))}subtract(t){return new at(this.q,this.x.subtract(t.toBigInteger()).mod(this.q))}multiply(t){return new at(this.q,this.x.multiply(t.toBigInteger()).mod(this.q))}divide(t){return new at(this.q,this.x.multiply(t.toBigInteger().modInverse(this.q)).mod(this.q))}square(){return new at(this.q,this.x.square().mod(this.q))}}class dt{constructor(t,e,i,n){this.curve=t,this.x=e,this.y=i,this.z=n==null?k.ONE:n,this.zinv=null}getX(){return this.zinv===null&&(this.zinv=this.z.modInverse(this.curve.q)),this.curve.fromBigInteger(this.x.toBigInteger().multiply(this.zinv).mod(this.curve.q))}getY(){return this.zinv===null&&(this.zinv=this.z.modInverse(this.curve.q)),this.curve.fromBigInteger(this.y.toBigInteger().multiply(this.zinv).mod(this.curve.q))}equals(t){return t===this?!0:this.isInfinity()?t.isInfinity():t.isInfinity()?this.isInfinity():t.y.toBigInteger().multiply(this.z).subtract(this.y.toBigInteger().multiply(t.z)).mod(this.curve.q).equals(k.ZERO)?t.x.toBigInteger().multiply(this.z).subtract(this.x.toBigInteger().multiply(t.z)).mod(this.curve.q).equals(k.ZERO):!1}isInfinity(){return this.x===null&&this.y===null?!0:this.z.equals(k.ZERO)&&!this.y.toBigInteger().equals(k.ZERO)}negate(){return new dt(this.curve,this.x,this.y.negate(),this.z)}add(t){if(this.isInfinity())return t;if(t.isInfinity())return this;const e=this.x.toBigInteger(),i=this.y.toBigInteger(),n=this.z,s=t.x.toBigInteger(),u=t.y.toBigInteger(),a=t.z,l=this.curve.q,v=e.multiply(a).mod(l),g=s.multiply(n).mod(l),x=v.subtract(g),m=i.multiply(a).mod(l),w=u.multiply(n).mod(l),S=m.subtract(w);if(k.ZERO.equals(x))return k.ZERO.equals(S)?this.twice():this.curve.infinity;const D=v.add(g),I=n.multiply(a).mod(l),q=x.square().mod(l),O=x.multiply(q).mod(l),L=I.multiply(S.square()).subtract(D.multiply(q)).mod(l),N=x.multiply(L).mod(l),it=S.multiply(q.multiply(v).subtract(L)).subtract(m.multiply(O)).mod(l),B=O.multiply(I).mod(l);return new dt(this.curve,this.curve.fromBigInteger(N),this.curve.fromBigInteger(it),B)}twice(){if(this.isInfinity())return this;if(!this.y.toBigInteger().signum())return this.curve.infinity;const t=this.x.toBigInteger(),e=this.y.toBigInteger(),i=this.z,n=this.curve.q,s=this.curve.a.toBigInteger(),u=t.square().multiply(Be).add(s.multiply(i.square())).mod(n),a=e.shiftLeft(1).multiply(i).mod(n),l=e.square().mod(n),v=l.multiply(t).multiply(i).mod(n),g=a.square().mod(n),x=u.square().subtract(v.shiftLeft(3)).mod(n),m=a.multiply(x).mod(n),w=u.multiply(v.shiftLeft(2).subtract(x)).subtract(g.shiftLeft(1).multiply(l)).mod(n),S=a.multiply(g).mod(n);return new dt(this.curve,this.curve.fromBigInteger(m),this.curve.fromBigInteger(w),S)}multiply(t){if(this.isInfinity())return this;if(!t.signum())return this.curve.infinity;const e=t.multiply(Be),i=this.negate();let n=this;for(let s=e.bitLength()-2;s>0;s--){n=n.twice();const u=e.testBit(s),a=t.testBit(s);u!==a&&(n=n.add(u?this:i))}return n}}class Lr{constructor(t,e,i){this.q=t,this.a=this.fromBigInteger(e),this.b=this.fromBigInteger(i),this.infinity=new dt(this,null,null)}equals(t){return t===this?!0:this.q.equals(t.q)&&this.a.equals(t.a)&&this.b.equals(t.b)}fromBigInteger(t){return new at(this.q,t)}decodePointHex(t){switch(parseInt(t.substr(0,2),16)){case 0:return this.infinity;case 2:case 3:const e=this.fromBigInteger(new k(t.substr(2),16));let i=this.fromBigInteger(e.multiply(e.square()).add(e.multiply(this.a)).add(this.b).toBigInteger().modPow(this.q.divide(new k("4")).add(k.ONE),this.q));return i.toBigInteger().mod(De).equals(new k(t.substr(0,2),16).subtract(De))||(i=i.negate()),new dt(this,e,i);case 4:case 6:case 7:const n=(t.length-2)/2,s=t.substr(2,n),u=t.substr(n+2,n);return new dt(this,this.fromBigInteger(new k(s,16)),this.fromBigInteger(new k(u,16)));default:return null}}}var _r={ECPointFp:dt,ECCurveFp:Lr};const{BigInteger:et,SecureRandom:jr}=Ct.exports,{ECCurveFp:kr}=_r,Ur=new jr,{curve:Ft,G:Kr,n:Re}=Ue();function zr(){return Ft}function Ue(){const r=new et("FFFFFFFEFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF00000000FFFFFFFFFFFFFFFF",16),t=new et("FFFFFFFEFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF00000000FFFFFFFFFFFFFFFC",16),e=new et("28E9FA9E9D9F5E344D5A9E4BCF6509A7F39789F515AB8F92DDBCBD414D940E93",16),i=new kr(r,t,e),n="32C4AE2C1F1981195F9904466A39C9948FE30BBFF2660BE1715A4589334C74C7",s="BC3736A2F4F6779C59BDCEE36B692153D0A9877CC62A474002DF32E52139F0A0",u=i.decodePointHex("04"+n+s),a=new et("FFFFFFFEFFFFFFFFFFFFFFFFFFFFFFFF7203DF6B21C6052B53BBF40939D54123",16);return{curve:i,G:u,n:a}}function Zr(r,t,e){const n=(r?new et(r,t,e):new et(Re.bitLength(),Ur)).mod(Re.subtract(et.ONE)).add(et.ONE),s=Nt(n.toString(16),64),u=Kr.multiply(n),a=Nt(u.getX().toBigInteger().toString(16),64),l=Nt(u.getY().toBigInteger().toString(16),64),v="04"+a+l;return{privateKey:s,publicKey:v}}function $r(r){if(r.length!==130)throw new Error("Invalid public key to compress");const t=(r.length-2)/2,e=r.substr(2,t),i=new et(r.substr(t+2,t),16);let n="03";return i.mod(new et("2")).equals(et.ZERO)&&(n="02"),n+e}function Gr(r){r=unescape(encodeURIComponent(r));const t=r.length,e=[];for(let n=0;n<t;n++)e[n>>>2]|=(r.charCodeAt(n)&255)<<24-n%4*8;const i=[];for(let n=0;n<t;n++){const s=e[n>>>2]>>>24-n%4*8&255;i.push((s>>>4).toString(16)),i.push((s&15).toString(16))}return i.join("")}function Nt(r,t){return r.length>=t?r:new Array(t-r.length+1).join("0")+r}function Yr(r){return r.map(t=>(t=t.toString(16),t.length===1?"0"+t:t)).join("")}function Xr(r){const t=[];let e=0;for(let i=0;i<r.length*2;i+=2)t[i>>>3]|=parseInt(r[e],10)<<24-i%8*4,e++;try{const i=[];for(let n=0;n<r.length;n++){const s=t[n>>>2]>>>24-n%4*8&255;i.push(String.fromCharCode(s))}return decodeURIComponent(escape(i.join("")))}catch{throw new Error("Malformed UTF-8 data")}}function Jr(r){const t=[];let e=r.length;e%2!==0&&(r=Nt(r,e+1)),e=r.length;for(let i=0;i<e;i+=2)t.push(parseInt(r.substr(i,2),16));return t}function Wr(r){const t=Ft.decodePointHex(r);if(!t)return!1;const e=t.getX();return t.getY().square().equals(e.multiply(e.square()).add(e.multiply(Ft.a)).add(Ft.b))}function Qr(r,t){const e=Ft.decodePointHex(r);if(!e)return!1;const i=Ft.decodePointHex(t);return i?e.equals(i):!1}var tn={getGlobalCurve:zr,generateEcparam:Ue,generateKeyPairHex:Zr,compressPublicKeyHex:$r,utf8ToHex:Gr,leftPad:Nt,arrayToHex:Yr,arrayToUtf8:Xr,hexToArray:Jr,verifyPublicKey:Wr,comparePublicKeyHex:Qr};const Q=new Uint32Array(68),he=new Uint32Array(64);function G(r,t){const e=t&31;return r<<e|r>>>32-e}function Fe(r,t){const e=[];for(let i=r.length-1;i>=0;i--)e[i]=(r[i]^t[i])&255;return e}function en(r){return r^G(r,9)^G(r,17)}function rn(r){return r^G(r,15)^G(r,23)}function Jt(r){let t=r.length*8,e=t%512;e=e>=448?512-e%448-1:448-e-1;const i=new Array((e-7)/8),n=new Array(8);for(let g=0,x=i.length;g<x;g++)i[g]=0;for(let g=0,x=n.length;g<x;g++)n[g]=0;t=t.toString(2);for(let g=7;g>=0;g--)if(t.length>8){const x=t.length-8;n[g]=parseInt(t.substr(x),2),t=t.substr(0,x)}else t.length>0&&(n[g]=parseInt(t,2),t="");const s=new Uint8Array([...r,128,...i,...n]),u=new DataView(s.buffer,0),a=s.length/64,l=new Uint32Array([1937774191,1226093241,388252375,3666478592,2842636476,372324522,3817729613,2969243214]);for(let g=0;g<a;g++){Q.fill(0),he.fill(0);const x=16*g;for(let E=0;E<16;E++)Q[E]=u.getUint32((x+E)*4,!1);for(let E=16;E<68;E++)Q[E]=rn(Q[E-16]^Q[E-9]^G(Q[E-3],15))^G(Q[E-13],7)^Q[E-6];for(let E=0;E<64;E++)he[E]=Q[E]^Q[E+4];const m=2043430169,w=2055708042;let S=l[0],D=l[1],I=l[2],q=l[3],O=l[4],L=l[5],N=l[6],it=l[7],B,rt,nt,Z,ht;for(let E=0;E<64;E++)ht=E>=0&&E<=15?m:w,B=G(G(S,12)+O+G(ht,E),7),rt=B^G(S,12),nt=(E>=0&&E<=15?S^D^I:S&D|S&I|D&I)+q+rt+he[E],Z=(E>=0&&E<=15?O^L^N:O&L|~O&N)+it+B+Q[E],q=I,I=G(D,9),D=S,S=nt,it=N,N=G(L,19),L=O,O=en(Z);l[0]^=S,l[1]^=D,l[2]^=I,l[3]^=q,l[4]^=O,l[5]^=L,l[6]^=N,l[7]^=it}const v=[];for(let g=0,x=l.length;g<x;g++){const m=l[g];v.push((m&4278190080)>>>24,(m&16711680)>>>16,(m&65280)>>>8,m&255)}return v}const Mt=64,Ke=new Uint8Array(Mt),ze=new Uint8Array(Mt);for(let r=0;r<Mt;r++)Ke[r]=54,ze[r]=92;function nn(r,t){for(t.length>Mt&&(t=Jt(t));t.length<Mt;)t.push(0);const e=Fe(t,Ke),i=Fe(t,ze),n=Jt([...e,...r]);return Jt([...i,...n])}var Ze={sm3:Jt,hmac:nn};const{BigInteger:z}=Ct.exports,{encodeDer:sn,decodeDer:on}=Hr,R=tn,Ot=Ze.sm3,{G:Tt,curve:$e,n:Bt}=R.generateEcparam(),Ge=0;function hn(r,t,e=1){r=typeof r=="string"?R.hexToArray(R.utf8ToHex(r)):Array.prototype.slice.call(r),t=R.getGlobalCurve().decodePointHex(t);const i=R.generateKeyPairHex(),n=new z(i.privateKey,16);let s=i.publicKey;s.length>128&&(s=s.substr(s.length-128));const u=t.multiply(n),a=R.hexToArray(R.leftPad(u.getX().toBigInteger().toRadix(16),64)),l=R.hexToArray(R.leftPad(u.getY().toBigInteger().toRadix(16),64)),v=R.arrayToHex(Ot([].concat(a,r,l)));let g=1,x=0,m=[];const w=[].concat(a,l),S=()=>{m=Ot([...w,g>>24&255,g>>16&255,g>>8&255,g&255]),g++,x=0};S();for(let I=0,q=r.length;I<q;I++)x===m.length&&S(),r[I]^=m[x++]&255;const D=R.arrayToHex(r);return e===Ge?s+D+v:s+v+D}function fn(r,t,e=1,{output:i="string"}={}){t=new z(t,16);let n=r.substr(128,64),s=r.substr(128+64);e===Ge&&(n=r.substr(r.length-64),s=r.substr(128,r.length-128-64));const u=R.hexToArray(s),l=R.getGlobalCurve().decodePointHex("04"+r.substr(0,128)).multiply(t),v=R.hexToArray(R.leftPad(l.getX().toBigInteger().toRadix(16),64)),g=R.hexToArray(R.leftPad(l.getY().toBigInteger().toRadix(16),64));let x=1,m=0,w=[];const S=[].concat(v,g),D=()=>{w=Ot([...S,x>>24&255,x>>16&255,x>>8&255,x&255]),x++,m=0};D();for(let q=0,O=u.length;q<O;q++)m===w.length&&D(),u[q]^=w[m++]&255;return R.arrayToHex(Ot([].concat(v,u,g)))===n.toLowerCase()?i==="array"?u:R.arrayToUtf8(u):i==="array"?[]:""}function un(r,t,{pointPool:e,der:i,hash:n,publicKey:s,userId:u}={}){let a=typeof r=="string"?R.utf8ToHex(r):R.arrayToHex(r);n&&(s=s||Xe(t),a=Ye(a,s,u));const l=new z(t,16),v=new z(a,16);let g=null,x=null,m=null;do{do{let w;e&&e.length?w=e.pop():w=Je(),g=w.k,x=v.add(w.x1).mod(Bt)}while(x.equals(z.ZERO)||x.add(g).equals(Bt));m=l.add(z.ONE).modInverse(Bt).multiply(g.subtract(x.multiply(l))).mod(Bt)}while(m.equals(z.ZERO));return i?sn(x,m):R.leftPad(x.toString(16),64)+R.leftPad(m.toString(16),64)}function an(r,t,e,{der:i,hash:n,userId:s}={}){let u=typeof r=="string"?R.utf8ToHex(r):R.arrayToHex(r);n&&(u=Ye(u,e,s));let a,l;if(i){const S=on(t);a=S.r,l=S.s}else a=new z(t.substring(0,64),16),l=new z(t.substring(64),16);const v=$e.decodePointHex(e),g=new z(u,16),x=a.add(l).mod(Bt);if(x.equals(z.ZERO))return!1;const m=Tt.multiply(l).add(v.multiply(x)),w=g.add(m.getX().toBigInteger()).mod(Bt);return a.equals(w)}function Ye(r,t,e="1234567812345678"){e=R.utf8ToHex(e);const i=R.leftPad(Tt.curve.a.toBigInteger().toRadix(16),64),n=R.leftPad(Tt.curve.b.toBigInteger().toRadix(16),64),s=R.leftPad(Tt.getX().toBigInteger().toRadix(16),64),u=R.leftPad(Tt.getY().toBigInteger().toRadix(16),64);let a,l;if(t.length===128)a=t.substr(0,64),l=t.substr(64,64);else{const m=Tt.curve.decodePointHex(t);a=R.leftPad(m.getX().toBigInteger().toRadix(16),64),l=R.leftPad(m.getY().toBigInteger().toRadix(16),64)}const v=R.hexToArray(e+i+n+s+u+a+l),g=e.length*4;v.unshift(g&255),v.unshift(g>>8&255);const x=Ot(v);return R.arrayToHex(Ot(x.concat(R.hexToArray(r))))}function Xe(r){const t=Tt.multiply(new z(r,16)),e=R.leftPad(t.getX().toBigInteger().toString(16),64),i=R.leftPad(t.getY().toBigInteger().toString(16),64);return"04"+e+i}function Je(){const r=R.generateKeyPairHex(),t=$e.decodePointHex(r.publicKey);return r.k=new z(r.privateKey,16),r.x1=t.getX().toBigInteger(),r}var ln={generateKeyPairHex:R.generateKeyPairHex,compressPublicKeyHex:R.compressPublicKeyHex,comparePublicKeyHex:R.comparePublicKeyHex,doEncrypt:hn,doDecrypt:fn,doSignature:un,doVerifySignature:an,getPublicKeyFromPrivateKey:Xe,getPoint:Je,verifyPublicKey:R.verifyPublicKey};const{sm3:cn,hmac:pn}=Ze;function dn(r,t){return r.length>=t?r:new Array(t-r.length+1).join("0")+r}function Ae(r){return r.map(t=>(t=t.toString(16),t.length===1?"0"+t:t)).join("")}function gn(r){const t=[];let e=r.length;e%2!==0&&(r=dn(r,e+1)),e=r.length;for(let i=0;i<e;i+=2)t.push(parseInt(r.substr(i,2),16));return t}function vn(r){const t=[];for(let e=0,i=r.length;e<i;e++){const n=r.codePointAt(e);if(n<=127)t.push(n);else if(n<=2047)t.push(192|n>>>6),t.push(128|n&63);else if(n<=55295||n>=57344&&n<=65535)t.push(224|n>>>12),t.push(128|n>>>6&63),t.push(128|n&63);else if(n>=65536&&n<=1114111)e++,t.push(240|n>>>18&28),t.push(128|n>>>12&63),t.push(128|n>>>6&63),t.push(128|n&63);else throw t.push(n),new Error("input is not supported")}return t}var yn=function(r,t){if(r=typeof r=="string"?vn(r):Array.prototype.slice.call(r),t){if((t.mode||"hmac")!=="hmac")throw new Error("invalid mode");let i=t.key;if(!i)throw new Error("invalid key");return i=typeof i=="string"?gn(i):Array.prototype.slice.call(i),Ae(pn(r,i))}return Ae(cn(r))};const ut=0,xn=32,mt=16,jt=[214,144,233,254,204,225,61,183,22,182,20,194,40,251,44,5,43,103,154,118,42,190,4,195,170,68,19,38,73,134,6,153,156,66,80,244,145,239,152,122,51,84,11,67,237,207,172,98,228,179,28,169,201,8,232,149,128,223,148,250,117,143,63,166,71,7,167,252,243,115,23,186,131,89,60,25,230,133,79,168,104,107,129,178,113,100,218,139,248,235,15,75,112,86,157,53,30,36,14,94,99,88,209,162,37,34,124,59,1,33,120,135,212,0,70,87,159,211,39,82,76,54,2,231,160,196,200,158,234,191,138,210,64,199,56,181,163,247,242,206,249,97,21,161,224,174,93,164,155,52,26,85,173,147,50,48,245,140,177,227,29,246,226,46,130,102,202,96,192,41,35,171,13,83,78,111,213,219,55,69,222,253,142,47,3,255,106,114,109,108,91,81,141,27,175,146,187,221,188,127,17,217,92,65,31,16,90,216,10,193,49,136,165,205,123,189,45,116,208,18,184,229,180,176,137,105,151,74,12,150,119,126,101,185,241,9,197,110,198,132,24,240,125,236,58,220,77,32,121,238,95,62,215,203,57,72],kt=[462357,472066609,943670861,1415275113,1886879365,2358483617,2830087869,3301692121,3773296373,4228057617,404694573,876298825,1347903077,1819507329,2291111581,2762715833,3234320085,3705924337,4177462797,337322537,808926789,1280531041,1752135293,2223739545,2695343797,3166948049,3638552301,4110090761,269950501,741554753,1213159005,1684763257];function fe(r){const t=[];for(let e=0,i=r.length;e<i;e+=2)t.push(parseInt(r.substr(e,2),16));return t}function mn(r){return r.map(t=>(t=t.toString(16),t.length===1?"0"+t:t)).join("")}function bn(r){const t=[];for(let e=0,i=r.length;e<i;e++){const n=r.codePointAt(e);if(n<=127)t.push(n);else if(n<=2047)t.push(192|n>>>6),t.push(128|n&63);else if(n<=55295||n>=57344&&n<=65535)t.push(224|n>>>12),t.push(128|n>>>6&63),t.push(128|n&63);else if(n>=65536&&n<=1114111)e++,t.push(240|n>>>18&28),t.push(128|n>>>12&63),t.push(128|n>>>6&63),t.push(128|n&63);else throw t.push(n),new Error("input is not supported")}return t}function Tn(r){const t=[];for(let e=0,i=r.length;e<i;e++)r[e]>=240&&r[e]<=247?(t.push(String.fromCodePoint(((r[e]&7)<<18)+((r[e+1]&63)<<12)+((r[e+2]&63)<<6)+(r[e+3]&63))),e+=3):r[e]>=224&&r[e]<=239?(t.push(String.fromCodePoint(((r[e]&15)<<12)+((r[e+1]&63)<<6)+(r[e+2]&63))),e+=2):r[e]>=192&&r[e]<=223?(t.push(String.fromCodePoint(((r[e]&31)<<6)+(r[e+1]&63))),e++):t.push(String.fromCodePoint(r[e]));return t.join("")}function Rt(r,t){const e=t&31;return r<<e|r>>>32-e}function ct(r){return(jt[r>>>24&255]&255)<<24|(jt[r>>>16&255]&255)<<16|(jt[r>>>8&255]&255)<<8|jt[r&255]&255}function Ut(r){return r^Rt(r,2)^Rt(r,10)^Rt(r,18)^Rt(r,24)}function Kt(r){return r^Rt(r,13)^Rt(r,23)}function wn(r,t,e){const i=new Array(4),n=new Array(4);for(let s=0;s<4;s++)n[0]=r[4*s]&255,n[1]=r[4*s+1]&255,n[2]=r[4*s+2]&255,n[3]=r[4*s+3]&255,i[s]=n[0]<<24|n[1]<<16|n[2]<<8|n[3];for(let s=0,u;s<32;s+=4)u=i[1]^i[2]^i[3]^e[s+0],i[0]^=Ut(ct(u)),u=i[2]^i[3]^i[0]^e[s+1],i[1]^=Ut(ct(u)),u=i[3]^i[0]^i[1]^e[s+2],i[2]^=Ut(ct(u)),u=i[0]^i[1]^i[2]^e[s+3],i[3]^=Ut(ct(u));for(let s=0;s<16;s+=4)t[s]=i[3-s/4]>>>24&255,t[s+1]=i[3-s/4]>>>16&255,t[s+2]=i[3-s/4]>>>8&255,t[s+3]=i[3-s/4]&255}function Sn(r,t,e){const i=new Array(4),n=new Array(4);for(let s=0;s<4;s++)n[0]=r[0+4*s]&255,n[1]=r[1+4*s]&255,n[2]=r[2+4*s]&255,n[3]=r[3+4*s]&255,i[s]=n[0]<<24|n[1]<<16|n[2]<<8|n[3];i[0]^=2746333894,i[1]^=1453994832,i[2]^=1736282519,i[3]^=2993693404;for(let s=0,u;s<32;s+=4)u=i[1]^i[2]^i[3]^kt[s+0],t[s+0]=i[0]^=Kt(ct(u)),u=i[2]^i[3]^i[0]^kt[s+1],t[s+1]=i[1]^=Kt(ct(u)),u=i[3]^i[0]^i[1]^kt[s+2],t[s+2]=i[2]^=Kt(ct(u)),u=i[0]^i[1]^i[2]^kt[s+3],t[s+3]=i[3]^=Kt(ct(u));if(e===ut)for(let s=0,u;s<16;s++)u=t[s],t[s]=t[31-s],t[31-s]=u}function Ie(r,t,e,{padding:i="pkcs#7",mode:n,iv:s=[],output:u="string"}={}){if(n==="cbc"&&(typeof s=="string"&&(s=fe(s)),s.length!==128/8))throw new Error("iv is invalid");if(typeof t=="string"&&(t=fe(t)),t.length!==128/8)throw new Error("key is invalid");if(typeof r=="string"?e!==ut?r=bn(r):r=fe(r):r=[...r],(i==="pkcs#5"||i==="pkcs#7")&&e!==ut){const m=mt-r.length%mt;for(let w=0;w<m;w++)r.push(m)}const a=new Array(xn);Sn(t,a,e);const l=[];let v=s,g=r.length,x=0;for(;g>=mt;){const m=r.slice(x,x+16),w=new Array(16);if(n==="cbc")for(let S=0;S<mt;S++)e!==ut&&(m[S]^=v[S]);wn(m,w,a);for(let S=0;S<mt;S++)n==="cbc"&&e===ut&&(w[S]^=v[S]),l[x+S]=w[S];n==="cbc"&&(e!==ut?v=w:v=m),g-=mt,x+=mt}if((i==="pkcs#5"||i==="pkcs#7")&&e===ut){const m=l.length,w=l[m-1];for(let S=1;S<=w;S++)if(l[m-S]!==w)throw new Error("padding is invalid");l.splice(m-w,w)}return u!=="array"?e!==ut?mn(l):Tn(l):l}var En={encrypt(r,t,e){return Ie(r,t,1,e)},decrypt(r,t,e){return Ie(r,t,0,e)}},Dn={sm2:ln,sm3:yn,sm4:En},Bn="0123456789abcdefghijklmnopqrstuvwxyz";function ot(r){return Bn.charAt(r)}function Rn(r,t){return r&t}function zt(r,t){return r|t}function Oe(r,t){return r^t}function Ve(r,t){return r&~t}function Fn(r){if(r==0)return-1;var t=0;return(r&65535)==0&&(r>>=16,t+=16),(r&255)==0&&(r>>=8,t+=8),(r&15)==0&&(r>>=4,t+=4),(r&3)==0&&(r>>=2,t+=2),(r&1)==0&&++t,t}function An(r){for(var t=0;r!=0;)r&=r-1,++t;return t}var Dt="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",We="=";function Qt(r){var t,e,i="";for(t=0;t+3<=r.length;t+=3)e=parseInt(r.substring(t,t+3),16),i+=Dt.charAt(e>>6)+Dt.charAt(e&63);for(t+1==r.length?(e=parseInt(r.substring(t,t+1),16),i+=Dt.charAt(e<<2)):t+2==r.length&&(e=parseInt(r.substring(t,t+2),16),i+=Dt.charAt(e>>2)+Dt.charAt((e&3)<<4));(i.length&3)>0;)i+=We;return i}function qe(r){var t="",e,i=0,n=0;for(e=0;e<r.length&&r.charAt(e)!=We;++e){var s=Dt.indexOf(r.charAt(e));s<0||(i==0?(t+=ot(s>>2),n=s&3,i=1):i==1?(t+=ot(n<<2|s>>4),n=s&15,i=2):i==2?(t+=ot(n),t+=ot(s>>2),n=s&3,i=3):(t+=ot(n<<2|s>>4),t+=ot(s&15),i=0))}return i==1&&(t+=ot(n<<2)),t}var St,In={decode:function(r){var t;if(St===void 0){var e="0123456789ABCDEF",i=` \f
\r	\xA0\u2028\u2029`;for(St={},t=0;t<16;++t)St[e.charAt(t)]=t;for(e=e.toLowerCase(),t=10;t<16;++t)St[e.charAt(t)]=t;for(t=0;t<i.length;++t)St[i.charAt(t)]=-1}var n=[],s=0,u=0;for(t=0;t<r.length;++t){var a=r.charAt(t);if(a=="=")break;if(a=St[a],a!=-1){if(a===void 0)throw new Error("Illegal character at offset "+t);s|=a,++u>=2?(n[n.length]=s,s=0,u=0):s<<=4}}if(u)throw new Error("Hex encoding incomplete: 4 bits missing");return n}},bt,ce={decode:function(r){var t;if(bt===void 0){var e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",i=`= \f
\r	\xA0\u2028\u2029`;for(bt=Object.create(null),t=0;t<64;++t)bt[e.charAt(t)]=t;for(bt["-"]=62,bt._=63,t=0;t<i.length;++t)bt[i.charAt(t)]=-1}var n=[],s=0,u=0;for(t=0;t<r.length;++t){var a=r.charAt(t);if(a=="=")break;if(a=bt[a],a!=-1){if(a===void 0)throw new Error("Illegal character at offset "+t);s|=a,++u>=4?(n[n.length]=s>>16,n[n.length]=s>>8&255,n[n.length]=s&255,s=0,u=0):s<<=6}}switch(u){case 1:throw new Error("Base64 encoding incomplete: at least 2 bits missing");case 2:n[n.length]=s>>10;break;case 3:n[n.length]=s>>16,n[n.length]=s>>8&255;break}return n},re:/-----BEGIN [^-]+-----([A-Za-z0-9+\/=\s]+)-----END [^-]+-----|begin-base64[^\n]+\n([A-Za-z0-9+\/=\s]+)====/,unarmor:function(r){var t=ce.re.exec(r);if(t)if(t[1])r=t[1];else if(t[2])r=t[2];else throw new Error("RegExp out of sync");return ce.decode(r)}},Et=1e13,Pt=function(){function r(t){this.buf=[+t||0]}return r.prototype.mulAdd=function(t,e){var i=this.buf,n=i.length,s,u;for(s=0;s<n;++s)u=i[s]*t+e,u<Et?e=0:(e=0|u/Et,u-=e*Et),i[s]=u;e>0&&(i[s]=e)},r.prototype.sub=function(t){var e=this.buf,i=e.length,n,s;for(n=0;n<i;++n)s=e[n]-t,s<0?(s+=Et,t=1):t=0,e[n]=s;for(;e[e.length-1]===0;)e.pop()},r.prototype.toString=function(t){if((t||10)!=10)throw new Error("only base 10 is supported");for(var e=this.buf,i=e[e.length-1].toString(),n=e.length-2;n>=0;--n)i+=(Et+e[n]).toString().substring(1);return i},r.prototype.valueOf=function(){for(var t=this.buf,e=0,i=t.length-1;i>=0;--i)e=e*Et+t[i];return e},r.prototype.simplify=function(){var t=this.buf;return t.length==1?t[0]:this},r}(),Qe="\u2026",On=/^(\d\d)(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])([01]\d|2[0-3])(?:([0-5]\d)(?:([0-5]\d)(?:[.,](\d{1,3}))?)?)?(Z|[-+](?:[0]\d|1[0-2])([0-5]\d)?)?$/,Vn=/^(\d\d\d\d)(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])([01]\d|2[0-3])(?:([0-5]\d)(?:([0-5]\d)(?:[.,](\d{1,3}))?)?)?(Z|[-+](?:[0]\d|1[0-2])([0-5]\d)?)?$/;function At(r,t){return r.length>t&&(r=r.substring(0,t)+Qe),r}var ue=function(){function r(t,e){this.hexDigits="0123456789ABCDEF",t instanceof r?(this.enc=t.enc,this.pos=t.pos):(this.enc=t,this.pos=e)}return r.prototype.get=function(t){if(t===void 0&&(t=this.pos++),t>=this.enc.length)throw new Error("Requesting byte offset ".concat(t," on a stream of length ").concat(this.enc.length));return typeof this.enc=="string"?this.enc.charCodeAt(t):this.enc[t]},r.prototype.hexByte=function(t){return this.hexDigits.charAt(t>>4&15)+this.hexDigits.charAt(t&15)},r.prototype.hexDump=function(t,e,i){for(var n="",s=t;s<e;++s)if(n+=this.hexByte(this.get(s)),i!==!0)switch(s&15){case 7:n+="  ";break;case 15:n+=`
`;break;default:n+=" "}return n},r.prototype.isASCII=function(t,e){for(var i=t;i<e;++i){var n=this.get(i);if(n<32||n>176)return!1}return!0},r.prototype.parseStringISO=function(t,e){for(var i="",n=t;n<e;++n)i+=String.fromCharCode(this.get(n));return i},r.prototype.parseStringUTF=function(t,e){for(var i="",n=t;n<e;){var s=this.get(n++);s<128?i+=String.fromCharCode(s):s>191&&s<224?i+=String.fromCharCode((s&31)<<6|this.get(n++)&63):i+=String.fromCharCode((s&15)<<12|(this.get(n++)&63)<<6|this.get(n++)&63)}return i},r.prototype.parseStringBMP=function(t,e){for(var i="",n,s,u=t;u<e;)n=this.get(u++),s=this.get(u++),i+=String.fromCharCode(n<<8|s);return i},r.prototype.parseTime=function(t,e,i){var n=this.parseStringISO(t,e),s=(i?On:Vn).exec(n);return s?(i&&(s[1]=+s[1],s[1]+=+s[1]<70?2e3:1900),n=s[1]+"-"+s[2]+"-"+s[3]+" "+s[4],s[5]&&(n+=":"+s[5],s[6]&&(n+=":"+s[6],s[7]&&(n+="."+s[7]))),s[8]&&(n+=" UTC",s[8]!="Z"&&(n+=s[8],s[9]&&(n+=":"+s[9]))),n):"Unrecognized time: "+n},r.prototype.parseInteger=function(t,e){for(var i=this.get(t),n=i>127,s=n?255:0,u,a="";i==s&&++t<e;)i=this.get(t);if(u=e-t,u===0)return n?-1:0;if(u>4){for(a=i,u<<=3;((+a^s)&128)==0;)a=+a<<1,--u;a="("+u+` bit)
`}n&&(i=i-256);for(var l=new Pt(i),v=t+1;v<e;++v)l.mulAdd(256,this.get(v));return a+l.toString()},r.prototype.parseBitString=function(t,e,i){for(var n=this.get(t),s=(e-t-1<<3)-n,u="("+s+` bit)
`,a="",l=t+1;l<e;++l){for(var v=this.get(l),g=l==e-1?n:0,x=7;x>=g;--x)a+=v>>x&1?"1":"0";if(a.length>i)return u+At(a,i)}return u+a},r.prototype.parseOctetString=function(t,e,i){if(this.isASCII(t,e))return At(this.parseStringISO(t,e),i);var n=e-t,s="("+n+` byte)
`;i/=2,n>i&&(e=t+i);for(var u=t;u<e;++u)s+=this.hexByte(this.get(u));return n>i&&(s+=Qe),s},r.prototype.parseOID=function(t,e,i){for(var n="",s=new Pt,u=0,a=t;a<e;++a){var l=this.get(a);if(s.mulAdd(128,l&127),u+=7,!(l&128)){if(n==="")if(s=s.simplify(),s instanceof Pt)s.sub(80),n="2."+s.toString();else{var v=s<80?s<40?0:1:2;n=v+"."+(s-v*40)}else n+="."+s.toString();if(n.length>i)return At(n,i);s=new Pt,u=0}}return u>0&&(n+=".incomplete"),n},r}(),qn=function(){function r(t,e,i,n,s){if(!(n instanceof Pe))throw new Error("Invalid tag value.");this.stream=t,this.header=e,this.length=i,this.tag=n,this.sub=s}return r.prototype.typeName=function(){switch(this.tag.tagClass){case 0:switch(this.tag.tagNumber){case 0:return"EOC";case 1:return"BOOLEAN";case 2:return"INTEGER";case 3:return"BIT_STRING";case 4:return"OCTET_STRING";case 5:return"NULL";case 6:return"OBJECT_IDENTIFIER";case 7:return"ObjectDescriptor";case 8:return"EXTERNAL";case 9:return"REAL";case 10:return"ENUMERATED";case 11:return"EMBEDDED_PDV";case 12:return"UTF8String";case 16:return"SEQUENCE";case 17:return"SET";case 18:return"NumericString";case 19:return"PrintableString";case 20:return"TeletexString";case 21:return"VideotexString";case 22:return"IA5String";case 23:return"UTCTime";case 24:return"GeneralizedTime";case 25:return"GraphicString";case 26:return"VisibleString";case 27:return"GeneralString";case 28:return"UniversalString";case 30:return"BMPString"}return"Universal_"+this.tag.tagNumber.toString();case 1:return"Application_"+this.tag.tagNumber.toString();case 2:return"["+this.tag.tagNumber.toString()+"]";case 3:return"Private_"+this.tag.tagNumber.toString()}},r.prototype.content=function(t){if(this.tag===void 0)return null;t===void 0&&(t=1/0);var e=this.posContent(),i=Math.abs(this.length);if(!this.tag.isUniversal())return this.sub!==null?"("+this.sub.length+" elem)":this.stream.parseOctetString(e,e+i,t);switch(this.tag.tagNumber){case 1:return this.stream.get(e)===0?"false":"true";case 2:return this.stream.parseInteger(e,e+i);case 3:return this.sub?"("+this.sub.length+" elem)":this.stream.parseBitString(e,e+i,t);case 4:return this.sub?"("+this.sub.length+" elem)":this.stream.parseOctetString(e,e+i,t);case 6:return this.stream.parseOID(e,e+i,t);case 16:case 17:return this.sub!==null?"("+this.sub.length+" elem)":"(no elem)";case 12:return At(this.stream.parseStringUTF(e,e+i),t);case 18:case 19:case 20:case 21:case 22:case 26:return At(this.stream.parseStringISO(e,e+i),t);case 30:return At(this.stream.parseStringBMP(e,e+i),t);case 23:case 24:return this.stream.parseTime(e,e+i,this.tag.tagNumber==23)}return null},r.prototype.toString=function(){return this.typeName()+"@"+this.stream.pos+"[header:"+this.header+",length:"+this.length+",sub:"+(this.sub===null?"null":this.sub.length)+"]"},r.prototype.toPrettyString=function(t){t===void 0&&(t="");var e=t+this.typeName()+" @"+this.stream.pos;if(this.length>=0&&(e+="+"),e+=this.length,this.tag.tagConstructed?e+=" (constructed)":this.tag.isUniversal()&&(this.tag.tagNumber==3||this.tag.tagNumber==4)&&this.sub!==null&&(e+=" (encapsulates)"),e+=`
`,this.sub!==null){t+="  ";for(var i=0,n=this.sub.length;i<n;++i)e+=this.sub[i].toPrettyString(t)}return e},r.prototype.posStart=function(){return this.stream.pos},r.prototype.posContent=function(){return this.stream.pos+this.header},r.prototype.posEnd=function(){return this.stream.pos+this.header+Math.abs(this.length)},r.prototype.toHexString=function(){return this.stream.hexDump(this.posStart(),this.posEnd(),!0)},r.decodeLength=function(t){var e=t.get(),i=e&127;if(i==e)return i;if(i>6)throw new Error("Length over 48 bits not supported at position "+(t.pos-1));if(i===0)return null;e=0;for(var n=0;n<i;++n)e=e*256+t.get();return e},r.prototype.getHexStringValue=function(){var t=this.toHexString(),e=this.header*2,i=this.length*2;return t.substr(e,i)},r.decode=function(t){var e;t instanceof ue?e=t:e=new ue(t,0);var i=new ue(e),n=new Pe(e),s=r.decodeLength(e),u=e.pos,a=u-i.pos,l=null,v=function(){var x=[];if(s!==null){for(var m=u+s;e.pos<m;)x[x.length]=r.decode(e);if(e.pos!=m)throw new Error("Content size is not correct for container starting at offset "+u)}else try{for(;;){var w=r.decode(e);if(w.tag.isEOC())break;x[x.length]=w}s=u-e.pos}catch(S){throw new Error("Exception while decoding undefined length content: "+S)}return x};if(n.tagConstructed)l=v();else if(n.isUniversal()&&(n.tagNumber==3||n.tagNumber==4))try{if(n.tagNumber==3&&e.get()!=0)throw new Error("BIT STRINGs with unused bits cannot encapsulate.");l=v();for(var g=0;g<l.length;++g)if(l[g].tag.isEOC())throw new Error("EOC is not supposed to be actual content.")}catch{l=null}if(l===null){if(s===null)throw new Error("We can't skip over an invalid tag with undefined length at offset "+u);e.pos=u+Math.abs(s)}return new r(i,a,s,n,l)},r}(),Pe=function(){function r(t){var e=t.get();if(this.tagClass=e>>6,this.tagConstructed=(e&32)!==0,this.tagNumber=e&31,this.tagNumber==31){var i=new Pt;do e=t.get(),i.mulAdd(128,e&127);while(e&128);this.tagNumber=i.simplify()}}return r.prototype.isUniversal=function(){return this.tagClass===0},r.prototype.isEOC=function(){return this.tagClass===0&&this.tagNumber===0},r}(),gt,Pn=0xdeadbeefcafe,Ne=(Pn&16777215)==15715070,j=[2,3,5,7,11,13,17,19,23,29,31,37,41,43,47,53,59,61,67,71,73,79,83,89,97,101,103,107,109,113,127,131,137,139,149,151,157,163,167,173,179,181,191,193,197,199,211,223,227,229,233,239,241,251,257,263,269,271,277,281,283,293,307,311,313,317,331,337,347,349,353,359,367,373,379,383,389,397,401,409,419,421,431,433,439,443,449,457,461,463,467,479,487,491,499,503,509,521,523,541,547,557,563,569,571,577,587,593,599,601,607,613,617,619,631,641,643,647,653,659,661,673,677,683,691,701,709,719,727,733,739,743,751,757,761,769,773,787,797,809,811,821,823,827,829,839,853,857,859,863,877,881,883,887,907,911,919,929,937,941,947,953,967,971,977,983,991,997],Nn=(1<<26)/j[j.length-1],F=function(){function r(t,e,i){t!=null&&(typeof t=="number"?this.fromNumber(t,e,i):e==null&&typeof t!="string"?this.fromString(t,256):this.fromString(t,e))}return r.prototype.toString=function(t){if(this.s<0)return"-"+this.negate().toString(t);var e;if(t==16)e=4;else if(t==8)e=3;else if(t==2)e=1;else if(t==32)e=5;else if(t==4)e=2;else return this.toRadix(t);var i=(1<<e)-1,n,s=!1,u="",a=this.t,l=this.DB-a*this.DB%e;if(a-- >0)for(l<this.DB&&(n=this[a]>>l)>0&&(s=!0,u=ot(n));a>=0;)l<e?(n=(this[a]&(1<<l)-1)<<e-l,n|=this[--a]>>(l+=this.DB-e)):(n=this[a]>>(l-=e)&i,l<=0&&(l+=this.DB,--a)),n>0&&(s=!0),s&&(u+=ot(n));return s?u:"0"},r.prototype.negate=function(){var t=A();return r.ZERO.subTo(this,t),t},r.prototype.abs=function(){return this.s<0?this.negate():this},r.prototype.compareTo=function(t){var e=this.s-t.s;if(e!=0)return e;var i=this.t;if(e=i-t.t,e!=0)return this.s<0?-e:e;for(;--i>=0;)if((e=this[i]-t[i])!=0)return e;return 0},r.prototype.bitLength=function(){return this.t<=0?0:this.DB*(this.t-1)+Zt(this[this.t-1]^this.s&this.DM)},r.prototype.mod=function(t){var e=A();return this.abs().divRemTo(t,null,e),this.s<0&&e.compareTo(r.ZERO)>0&&t.subTo(e,e),e},r.prototype.modPowInt=function(t,e){var i;return t<256||e.isEven()?i=new Me(e):i=new Ce(e),this.exp(t,i)},r.prototype.clone=function(){var t=A();return this.copyTo(t),t},r.prototype.intValue=function(){if(this.s<0){if(this.t==1)return this[0]-this.DV;if(this.t==0)return-1}else{if(this.t==1)return this[0];if(this.t==0)return 0}return(this[1]&(1<<32-this.DB)-1)<<this.DB|this[0]},r.prototype.byteValue=function(){return this.t==0?this.s:this[0]<<24>>24},r.prototype.shortValue=function(){return this.t==0?this.s:this[0]<<16>>16},r.prototype.signum=function(){return this.s<0?-1:this.t<=0||this.t==1&&this[0]<=0?0:1},r.prototype.toByteArray=function(){var t=this.t,e=[];e[0]=this.s;var i=this.DB-t*this.DB%8,n,s=0;if(t-- >0)for(i<this.DB&&(n=this[t]>>i)!=(this.s&this.DM)>>i&&(e[s++]=n|this.s<<this.DB-i);t>=0;)i<8?(n=(this[t]&(1<<i)-1)<<8-i,n|=this[--t]>>(i+=this.DB-8)):(n=this[t]>>(i-=8)&255,i<=0&&(i+=this.DB,--t)),(n&128)!=0&&(n|=-256),s==0&&(this.s&128)!=(n&128)&&++s,(s>0||n!=this.s)&&(e[s++]=n);return e},r.prototype.equals=function(t){return this.compareTo(t)==0},r.prototype.min=function(t){return this.compareTo(t)<0?this:t},r.prototype.max=function(t){return this.compareTo(t)>0?this:t},r.prototype.and=function(t){var e=A();return this.bitwiseTo(t,Rn,e),e},r.prototype.or=function(t){var e=A();return this.bitwiseTo(t,zt,e),e},r.prototype.xor=function(t){var e=A();return this.bitwiseTo(t,Oe,e),e},r.prototype.andNot=function(t){var e=A();return this.bitwiseTo(t,Ve,e),e},r.prototype.not=function(){for(var t=A(),e=0;e<this.t;++e)t[e]=this.DM&~this[e];return t.t=this.t,t.s=~this.s,t},r.prototype.shiftLeft=function(t){var e=A();return t<0?this.rShiftTo(-t,e):this.lShiftTo(t,e),e},r.prototype.shiftRight=function(t){var e=A();return t<0?this.lShiftTo(-t,e):this.rShiftTo(t,e),e},r.prototype.getLowestSetBit=function(){for(var t=0;t<this.t;++t)if(this[t]!=0)return t*this.DB+Fn(this[t]);return this.s<0?this.t*this.DB:-1},r.prototype.bitCount=function(){for(var t=0,e=this.s&this.DM,i=0;i<this.t;++i)t+=An(this[i]^e);return t},r.prototype.testBit=function(t){var e=Math.floor(t/this.DB);return e>=this.t?this.s!=0:(this[e]&1<<t%this.DB)!=0},r.prototype.setBit=function(t){return this.changeBit(t,zt)},r.prototype.clearBit=function(t){return this.changeBit(t,Ve)},r.prototype.flipBit=function(t){return this.changeBit(t,Oe)},r.prototype.add=function(t){var e=A();return this.addTo(t,e),e},r.prototype.subtract=function(t){var e=A();return this.subTo(t,e),e},r.prototype.multiply=function(t){var e=A();return this.multiplyTo(t,e),e},r.prototype.divide=function(t){var e=A();return this.divRemTo(t,e,null),e},r.prototype.remainder=function(t){var e=A();return this.divRemTo(t,null,e),e},r.prototype.divideAndRemainder=function(t){var e=A(),i=A();return this.divRemTo(t,e,i),[e,i]},r.prototype.modPow=function(t,e){var i=t.bitLength(),n,s=lt(1),u;if(i<=0)return s;i<18?n=1:i<48?n=3:i<144?n=4:i<768?n=5:n=6,i<8?u=new Me(e):e.isEven()?u=new Cn(e):u=new Ce(e);var a=[],l=3,v=n-1,g=(1<<n)-1;if(a[1]=u.convert(this),n>1){var x=A();for(u.sqrTo(a[1],x);l<=g;)a[l]=A(),u.mulTo(x,a[l-2],a[l]),l+=2}var m=t.t-1,w,S=!0,D=A(),I;for(i=Zt(t[m])-1;m>=0;){for(i>=v?w=t[m]>>i-v&g:(w=(t[m]&(1<<i+1)-1)<<v-i,m>0&&(w|=t[m-1]>>this.DB+i-v)),l=n;(w&1)==0;)w>>=1,--l;if((i-=l)<0&&(i+=this.DB,--m),S)a[w].copyTo(s),S=!1;else{for(;l>1;)u.sqrTo(s,D),u.sqrTo(D,s),l-=2;l>0?u.sqrTo(s,D):(I=s,s=D,D=I),u.mulTo(D,a[w],s)}for(;m>=0&&(t[m]&1<<i)==0;)u.sqrTo(s,D),I=s,s=D,D=I,--i<0&&(i=this.DB-1,--m)}return u.revert(s)},r.prototype.modInverse=function(t){var e=t.isEven();if(this.isEven()&&e||t.signum()==0)return r.ZERO;for(var i=t.clone(),n=this.clone(),s=lt(1),u=lt(0),a=lt(0),l=lt(1);i.signum()!=0;){for(;i.isEven();)i.rShiftTo(1,i),e?((!s.isEven()||!u.isEven())&&(s.addTo(this,s),u.subTo(t,u)),s.rShiftTo(1,s)):u.isEven()||u.subTo(t,u),u.rShiftTo(1,u);for(;n.isEven();)n.rShiftTo(1,n),e?((!a.isEven()||!l.isEven())&&(a.addTo(this,a),l.subTo(t,l)),a.rShiftTo(1,a)):l.isEven()||l.subTo(t,l),l.rShiftTo(1,l);i.compareTo(n)>=0?(i.subTo(n,i),e&&s.subTo(a,s),u.subTo(l,u)):(n.subTo(i,n),e&&a.subTo(s,a),l.subTo(u,l))}if(n.compareTo(r.ONE)!=0)return r.ZERO;if(l.compareTo(t)>=0)return l.subtract(t);if(l.signum()<0)l.addTo(t,l);else return l;return l.signum()<0?l.add(t):l},r.prototype.pow=function(t){return this.exp(t,new Mn)},r.prototype.gcd=function(t){var e=this.s<0?this.negate():this.clone(),i=t.s<0?t.negate():t.clone();if(e.compareTo(i)<0){var n=e;e=i,i=n}var s=e.getLowestSetBit(),u=i.getLowestSetBit();if(u<0)return e;for(s<u&&(u=s),u>0&&(e.rShiftTo(u,e),i.rShiftTo(u,i));e.signum()>0;)(s=e.getLowestSetBit())>0&&e.rShiftTo(s,e),(s=i.getLowestSetBit())>0&&i.rShiftTo(s,i),e.compareTo(i)>=0?(e.subTo(i,e),e.rShiftTo(1,e)):(i.subTo(e,i),i.rShiftTo(1,i));return u>0&&i.lShiftTo(u,i),i},r.prototype.isProbablePrime=function(t){var e,i=this.abs();if(i.t==1&&i[0]<=j[j.length-1]){for(e=0;e<j.length;++e)if(i[0]==j[e])return!0;return!1}if(i.isEven())return!1;for(e=1;e<j.length;){for(var n=j[e],s=e+1;s<j.length&&n<Nn;)n*=j[s++];for(n=i.modInt(n);e<s;)if(n%j[e++]==0)return!1}return i.millerRabin(t)},r.prototype.copyTo=function(t){for(var e=this.t-1;e>=0;--e)t[e]=this[e];t.t=this.t,t.s=this.s},r.prototype.fromInt=function(t){this.t=1,this.s=t<0?-1:0,t>0?this[0]=t:t<-1?this[0]=t+this.DV:this.t=0},r.prototype.fromString=function(t,e){var i;if(e==16)i=4;else if(e==8)i=3;else if(e==256)i=8;else if(e==2)i=1;else if(e==32)i=5;else if(e==4)i=2;else{this.fromRadix(t,e);return}this.t=0,this.s=0;for(var n=t.length,s=!1,u=0;--n>=0;){var a=i==8?+t[n]&255:Le(t,n);if(a<0){t.charAt(n)=="-"&&(s=!0);continue}s=!1,u==0?this[this.t++]=a:u+i>this.DB?(this[this.t-1]|=(a&(1<<this.DB-u)-1)<<u,this[this.t++]=a>>this.DB-u):this[this.t-1]|=a<<u,u+=i,u>=this.DB&&(u-=this.DB)}i==8&&(+t[0]&128)!=0&&(this.s=-1,u>0&&(this[this.t-1]|=(1<<this.DB-u)-1<<u)),this.clamp(),s&&r.ZERO.subTo(this,this)},r.prototype.clamp=function(){for(var t=this.s&this.DM;this.t>0&&this[this.t-1]==t;)--this.t},r.prototype.dlShiftTo=function(t,e){var i;for(i=this.t-1;i>=0;--i)e[i+t]=this[i];for(i=t-1;i>=0;--i)e[i]=0;e.t=this.t+t,e.s=this.s},r.prototype.drShiftTo=function(t,e){for(var i=t;i<this.t;++i)e[i-t]=this[i];e.t=Math.max(this.t-t,0),e.s=this.s},r.prototype.lShiftTo=function(t,e){for(var i=t%this.DB,n=this.DB-i,s=(1<<n)-1,u=Math.floor(t/this.DB),a=this.s<<i&this.DM,l=this.t-1;l>=0;--l)e[l+u+1]=this[l]>>n|a,a=(this[l]&s)<<i;for(var l=u-1;l>=0;--l)e[l]=0;e[u]=a,e.t=this.t+u+1,e.s=this.s,e.clamp()},r.prototype.rShiftTo=function(t,e){e.s=this.s;var i=Math.floor(t/this.DB);if(i>=this.t){e.t=0;return}var n=t%this.DB,s=this.DB-n,u=(1<<n)-1;e[0]=this[i]>>n;for(var a=i+1;a<this.t;++a)e[a-i-1]|=(this[a]&u)<<s,e[a-i]=this[a]>>n;n>0&&(e[this.t-i-1]|=(this.s&u)<<s),e.t=this.t-i,e.clamp()},r.prototype.subTo=function(t,e){for(var i=0,n=0,s=Math.min(t.t,this.t);i<s;)n+=this[i]-t[i],e[i++]=n&this.DM,n>>=this.DB;if(t.t<this.t){for(n-=t.s;i<this.t;)n+=this[i],e[i++]=n&this.DM,n>>=this.DB;n+=this.s}else{for(n+=this.s;i<t.t;)n-=t[i],e[i++]=n&this.DM,n>>=this.DB;n-=t.s}e.s=n<0?-1:0,n<-1?e[i++]=this.DV+n:n>0&&(e[i++]=n),e.t=i,e.clamp()},r.prototype.multiplyTo=function(t,e){var i=this.abs(),n=t.abs(),s=i.t;for(e.t=s+n.t;--s>=0;)e[s]=0;for(s=0;s<n.t;++s)e[s+i.t]=i.am(0,n[s],e,s,0,i.t);e.s=0,e.clamp(),this.s!=t.s&&r.ZERO.subTo(e,e)},r.prototype.squareTo=function(t){for(var e=this.abs(),i=t.t=2*e.t;--i>=0;)t[i]=0;for(i=0;i<e.t-1;++i){var n=e.am(i,e[i],t,2*i,0,1);(t[i+e.t]+=e.am(i+1,2*e[i],t,2*i+1,n,e.t-i-1))>=e.DV&&(t[i+e.t]-=e.DV,t[i+e.t+1]=1)}t.t>0&&(t[t.t-1]+=e.am(i,e[i],t,2*i,0,1)),t.s=0,t.clamp()},r.prototype.divRemTo=function(t,e,i){var n=t.abs();if(!(n.t<=0)){var s=this.abs();if(s.t<n.t){e!=null&&e.fromInt(0),i!=null&&this.copyTo(i);return}i==null&&(i=A());var u=A(),a=this.s,l=t.s,v=this.DB-Zt(n[n.t-1]);v>0?(n.lShiftTo(v,u),s.lShiftTo(v,i)):(n.copyTo(u),s.copyTo(i));var g=u.t,x=u[g-1];if(x!=0){var m=x*(1<<this.F1)+(g>1?u[g-2]>>this.F2:0),w=this.FV/m,S=(1<<this.F1)/m,D=1<<this.F2,I=i.t,q=I-g,O=e==null?A():e;for(u.dlShiftTo(q,O),i.compareTo(O)>=0&&(i[i.t++]=1,i.subTo(O,i)),r.ONE.dlShiftTo(g,O),O.subTo(u,u);u.t<g;)u[u.t++]=0;for(;--q>=0;){var L=i[--I]==x?this.DM:Math.floor(i[I]*w+(i[I-1]+D)*S);if((i[I]+=u.am(0,L,i,q,0,g))<L)for(u.dlShiftTo(q,O),i.subTo(O,i);i[I]<--L;)i.subTo(O,i)}e!=null&&(i.drShiftTo(g,e),a!=l&&r.ZERO.subTo(e,e)),i.t=g,i.clamp(),v>0&&i.rShiftTo(v,i),a<0&&r.ZERO.subTo(i,i)}}},r.prototype.invDigit=function(){if(this.t<1)return 0;var t=this[0];if((t&1)==0)return 0;var e=t&3;return e=e*(2-(t&15)*e)&15,e=e*(2-(t&255)*e)&255,e=e*(2-((t&65535)*e&65535))&65535,e=e*(2-t*e%this.DV)%this.DV,e>0?this.DV-e:-e},r.prototype.isEven=function(){return(this.t>0?this[0]&1:this.s)==0},r.prototype.exp=function(t,e){if(t>4294967295||t<1)return r.ONE;var i=A(),n=A(),s=e.convert(this),u=Zt(t)-1;for(s.copyTo(i);--u>=0;)if(e.sqrTo(i,n),(t&1<<u)>0)e.mulTo(n,s,i);else{var a=i;i=n,n=a}return e.revert(i)},r.prototype.chunkSize=function(t){return Math.floor(Math.LN2*this.DB/Math.log(t))},r.prototype.toRadix=function(t){if(t==null&&(t=10),this.signum()==0||t<2||t>36)return"0";var e=this.chunkSize(t),i=Math.pow(t,e),n=lt(i),s=A(),u=A(),a="";for(this.divRemTo(n,s,u);s.signum()>0;)a=(i+u.intValue()).toString(t).substr(1)+a,s.divRemTo(n,s,u);return u.intValue().toString(t)+a},r.prototype.fromRadix=function(t,e){this.fromInt(0),e==null&&(e=10);for(var i=this.chunkSize(e),n=Math.pow(e,i),s=!1,u=0,a=0,l=0;l<t.length;++l){var v=Le(t,l);if(v<0){t.charAt(l)=="-"&&this.signum()==0&&(s=!0);continue}a=e*a+v,++u>=i&&(this.dMultiply(n),this.dAddOffset(a,0),u=0,a=0)}u>0&&(this.dMultiply(Math.pow(e,u)),this.dAddOffset(a,0)),s&&r.ZERO.subTo(this,this)},r.prototype.fromNumber=function(t,e,i){if(typeof e=="number")if(t<2)this.fromInt(1);else for(this.fromNumber(t,i),this.testBit(t-1)||this.bitwiseTo(r.ONE.shiftLeft(t-1),zt,this),this.isEven()&&this.dAddOffset(1,0);!this.isProbablePrime(e);)this.dAddOffset(2,0),this.bitLength()>t&&this.subTo(r.ONE.shiftLeft(t-1),this);else{var n=[],s=t&7;n.length=(t>>3)+1,e.nextBytes(n),s>0?n[0]&=(1<<s)-1:n[0]=0,this.fromString(n,256)}},r.prototype.bitwiseTo=function(t,e,i){var n,s,u=Math.min(t.t,this.t);for(n=0;n<u;++n)i[n]=e(this[n],t[n]);if(t.t<this.t){for(s=t.s&this.DM,n=u;n<this.t;++n)i[n]=e(this[n],s);i.t=this.t}else{for(s=this.s&this.DM,n=u;n<t.t;++n)i[n]=e(s,t[n]);i.t=t.t}i.s=e(this.s,t.s),i.clamp()},r.prototype.changeBit=function(t,e){var i=r.ONE.shiftLeft(t);return this.bitwiseTo(i,e,i),i},r.prototype.addTo=function(t,e){for(var i=0,n=0,s=Math.min(t.t,this.t);i<s;)n+=this[i]+t[i],e[i++]=n&this.DM,n>>=this.DB;if(t.t<this.t){for(n+=t.s;i<this.t;)n+=this[i],e[i++]=n&this.DM,n>>=this.DB;n+=this.s}else{for(n+=this.s;i<t.t;)n+=t[i],e[i++]=n&this.DM,n>>=this.DB;n+=t.s}e.s=n<0?-1:0,n>0?e[i++]=n:n<-1&&(e[i++]=this.DV+n),e.t=i,e.clamp()},r.prototype.dMultiply=function(t){this[this.t]=this.am(0,t-1,this,0,0,this.t),++this.t,this.clamp()},r.prototype.dAddOffset=function(t,e){if(t!=0){for(;this.t<=e;)this[this.t++]=0;for(this[e]+=t;this[e]>=this.DV;)this[e]-=this.DV,++e>=this.t&&(this[this.t++]=0),++this[e]}},r.prototype.multiplyLowerTo=function(t,e,i){var n=Math.min(this.t+t.t,e);for(i.s=0,i.t=n;n>0;)i[--n]=0;for(var s=i.t-this.t;n<s;++n)i[n+this.t]=this.am(0,t[n],i,n,0,this.t);for(var s=Math.min(t.t,e);n<s;++n)this.am(0,t[n],i,n,0,e-n);i.clamp()},r.prototype.multiplyUpperTo=function(t,e,i){--e;var n=i.t=this.t+t.t-e;for(i.s=0;--n>=0;)i[n]=0;for(n=Math.max(e-this.t,0);n<t.t;++n)i[this.t+n-e]=this.am(e-n,t[n],i,0,0,this.t+n-e);i.clamp(),i.drShiftTo(1,i)},r.prototype.modInt=function(t){if(t<=0)return 0;var e=this.DV%t,i=this.s<0?t-1:0;if(this.t>0)if(e==0)i=this[0]%t;else for(var n=this.t-1;n>=0;--n)i=(e*i+this[n])%t;return i},r.prototype.millerRabin=function(t){var e=this.subtract(r.ONE),i=e.getLowestSetBit();if(i<=0)return!1;var n=e.shiftRight(i);t=t+1>>1,t>j.length&&(t=j.length);for(var s=A(),u=0;u<t;++u){s.fromInt(j[Math.floor(Math.random()*j.length)]);var a=s.modPow(n,this);if(a.compareTo(r.ONE)!=0&&a.compareTo(e)!=0){for(var l=1;l++<i&&a.compareTo(e)!=0;)if(a=a.modPowInt(2,this),a.compareTo(r.ONE)==0)return!1;if(a.compareTo(e)!=0)return!1}}return!0},r.prototype.square=function(){var t=A();return this.squareTo(t),t},r.prototype.gcda=function(t,e){var i=this.s<0?this.negate():this.clone(),n=t.s<0?t.negate():t.clone();if(i.compareTo(n)<0){var s=i;i=n,n=s}var u=i.getLowestSetBit(),a=n.getLowestSetBit();if(a<0){e(i);return}u<a&&(a=u),a>0&&(i.rShiftTo(a,i),n.rShiftTo(a,n));var l=function(){(u=i.getLowestSetBit())>0&&i.rShiftTo(u,i),(u=n.getLowestSetBit())>0&&n.rShiftTo(u,n),i.compareTo(n)>=0?(i.subTo(n,i),i.rShiftTo(1,i)):(n.subTo(i,n),n.rShiftTo(1,n)),i.signum()>0?setTimeout(l,0):(a>0&&n.lShiftTo(a,n),setTimeout(function(){e(n)},0))};setTimeout(l,10)},r.prototype.fromNumberAsync=function(t,e,i,n){if(typeof e=="number")if(t<2)this.fromInt(1);else{this.fromNumber(t,i),this.testBit(t-1)||this.bitwiseTo(r.ONE.shiftLeft(t-1),zt,this),this.isEven()&&this.dAddOffset(1,0);var s=this,u=function(){s.dAddOffset(2,0),s.bitLength()>t&&s.subTo(r.ONE.shiftLeft(t-1),s),s.isProbablePrime(e)?setTimeout(function(){n()},0):setTimeout(u,0)};setTimeout(u,0)}else{var a=[],l=t&7;a.length=(t>>3)+1,e.nextBytes(a),l>0?a[0]&=(1<<l)-1:a[0]=0,this.fromString(a,256)}},r}(),Mn=function(){function r(){}return r.prototype.convert=function(t){return t},r.prototype.revert=function(t){return t},r.prototype.mulTo=function(t,e,i){t.multiplyTo(e,i)},r.prototype.sqrTo=function(t,e){t.squareTo(e)},r}(),Me=function(){function r(t){this.m=t}return r.prototype.convert=function(t){return t.s<0||t.compareTo(this.m)>=0?t.mod(this.m):t},r.prototype.revert=function(t){return t},r.prototype.reduce=function(t){t.divRemTo(this.m,null,t)},r.prototype.mulTo=function(t,e,i){t.multiplyTo(e,i),this.reduce(i)},r.prototype.sqrTo=function(t,e){t.squareTo(e),this.reduce(e)},r}(),Ce=function(){function r(t){this.m=t,this.mp=t.invDigit(),this.mpl=this.mp&32767,this.mph=this.mp>>15,this.um=(1<<t.DB-15)-1,this.mt2=2*t.t}return r.prototype.convert=function(t){var e=A();return t.abs().dlShiftTo(this.m.t,e),e.divRemTo(this.m,null,e),t.s<0&&e.compareTo(F.ZERO)>0&&this.m.subTo(e,e),e},r.prototype.revert=function(t){var e=A();return t.copyTo(e),this.reduce(e),e},r.prototype.reduce=function(t){for(;t.t<=this.mt2;)t[t.t++]=0;for(var e=0;e<this.m.t;++e){var i=t[e]&32767,n=i*this.mpl+((i*this.mph+(t[e]>>15)*this.mpl&this.um)<<15)&t.DM;for(i=e+this.m.t,t[i]+=this.m.am(0,n,t,e,0,this.m.t);t[i]>=t.DV;)t[i]-=t.DV,t[++i]++}t.clamp(),t.drShiftTo(this.m.t,t),t.compareTo(this.m)>=0&&t.subTo(this.m,t)},r.prototype.mulTo=function(t,e,i){t.multiplyTo(e,i),this.reduce(i)},r.prototype.sqrTo=function(t,e){t.squareTo(e),this.reduce(e)},r}(),Cn=function(){function r(t){this.m=t,this.r2=A(),this.q3=A(),F.ONE.dlShiftTo(2*t.t,this.r2),this.mu=this.r2.divide(t)}return r.prototype.convert=function(t){if(t.s<0||t.t>2*this.m.t)return t.mod(this.m);if(t.compareTo(this.m)<0)return t;var e=A();return t.copyTo(e),this.reduce(e),e},r.prototype.revert=function(t){return t},r.prototype.reduce=function(t){for(t.drShiftTo(this.m.t-1,this.r2),t.t>this.m.t+1&&(t.t=this.m.t+1,t.clamp()),this.mu.multiplyUpperTo(this.r2,this.m.t+1,this.q3),this.m.multiplyLowerTo(this.q3,this.m.t+1,this.r2);t.compareTo(this.r2)<0;)t.dAddOffset(1,this.m.t+1);for(t.subTo(this.r2,t);t.compareTo(this.m)>=0;)t.subTo(this.m,t)},r.prototype.mulTo=function(t,e,i){t.multiplyTo(e,i),this.reduce(i)},r.prototype.sqrTo=function(t,e){t.squareTo(e),this.reduce(e)},r}();function A(){return new F(null)}function P(r,t){return new F(r,t)}var He=typeof navigator<"u";He&&Ne&&navigator.appName=="Microsoft Internet Explorer"?(F.prototype.am=function(t,e,i,n,s,u){for(var a=e&32767,l=e>>15;--u>=0;){var v=this[t]&32767,g=this[t++]>>15,x=l*v+g*a;v=a*v+((x&32767)<<15)+i[n]+(s&1073741823),s=(v>>>30)+(x>>>15)+l*g+(s>>>30),i[n++]=v&1073741823}return s},gt=30):He&&Ne&&navigator.appName!="Netscape"?(F.prototype.am=function(t,e,i,n,s,u){for(;--u>=0;){var a=e*this[t++]+i[n]+s;s=Math.floor(a/67108864),i[n++]=a&67108863}return s},gt=26):(F.prototype.am=function(t,e,i,n,s,u){for(var a=e&16383,l=e>>14;--u>=0;){var v=this[t]&16383,g=this[t++]>>14,x=l*v+g*a;v=a*v+((x&16383)<<14)+i[n]+s,s=(v>>28)+(x>>14)+l*g,i[n++]=v&268435455}return s},gt=28);F.prototype.DB=gt;F.prototype.DM=(1<<gt)-1;F.prototype.DV=1<<gt;var de=52;F.prototype.FV=Math.pow(2,de);F.prototype.F1=de-gt;F.prototype.F2=2*gt-de;var te=[],Vt,Y;Vt="0".charCodeAt(0);for(Y=0;Y<=9;++Y)te[Vt++]=Y;Vt="a".charCodeAt(0);for(Y=10;Y<36;++Y)te[Vt++]=Y;Vt="A".charCodeAt(0);for(Y=10;Y<36;++Y)te[Vt++]=Y;function Le(r,t){var e=te[r.charCodeAt(t)];return e==null?-1:e}function lt(r){var t=A();return t.fromInt(r),t}function Zt(r){var t=1,e;return(e=r>>>16)!=0&&(r=e,t+=16),(e=r>>8)!=0&&(r=e,t+=8),(e=r>>4)!=0&&(r=e,t+=4),(e=r>>2)!=0&&(r=e,t+=2),(e=r>>1)!=0&&(r=e,t+=1),t}F.ZERO=lt(0);F.ONE=lt(1);var Hn=function(){function r(){this.i=0,this.j=0,this.S=[]}return r.prototype.init=function(t){var e,i,n;for(e=0;e<256;++e)this.S[e]=e;for(i=0,e=0;e<256;++e)i=i+this.S[e]+t[e%t.length]&255,n=this.S[e],this.S[e]=this.S[i],this.S[i]=n;this.i=0,this.j=0},r.prototype.next=function(){var t;return this.i=this.i+1&255,this.j=this.j+this.S[this.i]&255,t=this.S[this.i],this.S[this.i]=this.S[this.j],this.S[this.j]=t,this.S[t+this.S[this.i]&255]},r}();function Ln(){return new Hn}var ti=256,$t,pt=null,tt;if(pt==null){pt=[],tt=0;var Gt=void 0;if(typeof window<"u"&&window.crypto&&window.crypto.getRandomValues){var ae=new Uint32Array(256);for(window.crypto.getRandomValues(ae),Gt=0;Gt<ae.length;++Gt)pt[tt++]=ae[Gt]&255}var Yt=0,Xt=function(r){if(Yt=Yt||0,Yt>=256||tt>=ti){window.removeEventListener?window.removeEventListener("mousemove",Xt,!1):window.detachEvent&&window.detachEvent("onmousemove",Xt);return}try{var t=r.x+r.y;pt[tt++]=t&255,Yt+=1}catch{}};typeof window<"u"&&(window.addEventListener?window.addEventListener("mousemove",Xt,!1):window.attachEvent&&window.attachEvent("onmousemove",Xt))}function _n(){if($t==null){for($t=Ln();tt<ti;){var r=Math.floor(65536*Math.random());pt[tt++]=r&255}for($t.init(pt),tt=0;tt<pt.length;++tt)pt[tt]=0;tt=0}return $t.next()}var pe=function(){function r(){}return r.prototype.nextBytes=function(t){for(var e=0;e<t.length;++e)t[e]=_n()},r}();function jn(r,t){if(t<r.length+22)return console.error("Message too long for RSA"),null;for(var e=t-r.length-6,i="",n=0;n<e;n+=2)i+="ff";var s="0001"+i+"00"+r;return P(s,16)}function kn(r,t){if(t<r.length+11)return console.error("Message too long for RSA"),null;for(var e=[],i=r.length-1;i>=0&&t>0;){var n=r.charCodeAt(i--);n<128?e[--t]=n:n>127&&n<2048?(e[--t]=n&63|128,e[--t]=n>>6|192):(e[--t]=n&63|128,e[--t]=n>>6&63|128,e[--t]=n>>12|224)}e[--t]=0;for(var s=new pe,u=[];t>2;){for(u[0]=0;u[0]==0;)s.nextBytes(u);e[--t]=u[0]}return e[--t]=2,e[--t]=0,new F(e)}var Un=function(){function r(){this.n=null,this.e=0,this.d=null,this.p=null,this.q=null,this.dmp1=null,this.dmq1=null,this.coeff=null}return r.prototype.doPublic=function(t){return t.modPowInt(this.e,this.n)},r.prototype.doPrivate=function(t){if(this.p==null||this.q==null)return t.modPow(this.d,this.n);for(var e=t.mod(this.p).modPow(this.dmp1,this.p),i=t.mod(this.q).modPow(this.dmq1,this.q);e.compareTo(i)<0;)e=e.add(this.p);return e.subtract(i).multiply(this.coeff).mod(this.p).multiply(this.q).add(i)},r.prototype.setPublic=function(t,e){t!=null&&e!=null&&t.length>0&&e.length>0?(this.n=P(t,16),this.e=parseInt(e,16)):console.error("Invalid RSA public key")},r.prototype.encrypt=function(t){var e=this.n.bitLength()+7>>3,i=kn(t,e);if(i==null)return null;var n=this.doPublic(i);if(n==null)return null;for(var s=n.toString(16),u=s.length,a=0;a<e*2-u;a++)s="0"+s;return s},r.prototype.setPrivate=function(t,e,i){t!=null&&e!=null&&t.length>0&&e.length>0?(this.n=P(t,16),this.e=parseInt(e,16),this.d=P(i,16)):console.error("Invalid RSA private key")},r.prototype.setPrivateEx=function(t,e,i,n,s,u,a,l){t!=null&&e!=null&&t.length>0&&e.length>0?(this.n=P(t,16),this.e=parseInt(e,16),this.d=P(i,16),this.p=P(n,16),this.q=P(s,16),this.dmp1=P(u,16),this.dmq1=P(a,16),this.coeff=P(l,16)):console.error("Invalid RSA private key")},r.prototype.generate=function(t,e){var i=new pe,n=t>>1;this.e=parseInt(e,16);for(var s=new F(e,16);;){for(;this.p=new F(t-n,1,i),!(this.p.subtract(F.ONE).gcd(s).compareTo(F.ONE)==0&&this.p.isProbablePrime(10)););for(;this.q=new F(n,1,i),!(this.q.subtract(F.ONE).gcd(s).compareTo(F.ONE)==0&&this.q.isProbablePrime(10)););if(this.p.compareTo(this.q)<=0){var u=this.p;this.p=this.q,this.q=u}var a=this.p.subtract(F.ONE),l=this.q.subtract(F.ONE),v=a.multiply(l);if(v.gcd(s).compareTo(F.ONE)==0){this.n=this.p.multiply(this.q),this.d=s.modInverse(v),this.dmp1=this.d.mod(a),this.dmq1=this.d.mod(l),this.coeff=this.q.modInverse(this.p);break}}},r.prototype.decrypt=function(t){var e=P(t,16),i=this.doPrivate(e);return i==null?null:Kn(i,this.n.bitLength()+7>>3)},r.prototype.generateAsync=function(t,e,i){var n=new pe,s=t>>1;this.e=parseInt(e,16);var u=new F(e,16),a=this,l=function(){var v=function(){if(a.p.compareTo(a.q)<=0){var m=a.p;a.p=a.q,a.q=m}var w=a.p.subtract(F.ONE),S=a.q.subtract(F.ONE),D=w.multiply(S);D.gcd(u).compareTo(F.ONE)==0?(a.n=a.p.multiply(a.q),a.d=u.modInverse(D),a.dmp1=a.d.mod(w),a.dmq1=a.d.mod(S),a.coeff=a.q.modInverse(a.p),setTimeout(function(){i()},0)):setTimeout(l,0)},g=function(){a.q=A(),a.q.fromNumberAsync(s,1,n,function(){a.q.subtract(F.ONE).gcda(u,function(m){m.compareTo(F.ONE)==0&&a.q.isProbablePrime(10)?setTimeout(v,0):setTimeout(g,0)})})},x=function(){a.p=A(),a.p.fromNumberAsync(t-s,1,n,function(){a.p.subtract(F.ONE).gcda(u,function(m){m.compareTo(F.ONE)==0&&a.p.isProbablePrime(10)?setTimeout(g,0):setTimeout(x,0)})})};setTimeout(x,0)};setTimeout(l,0)},r.prototype.sign=function(t,e,i){var n=zn(i),s=n+e(t).toString(),u=jn(s,this.n.bitLength()/4);if(u==null)return null;var a=this.doPrivate(u);if(a==null)return null;var l=a.toString(16);return(l.length&1)==0?l:"0"+l},r.prototype.verify=function(t,e,i){var n=P(e,16),s=this.doPublic(n);if(s==null)return null;var u=s.toString(16).replace(/^1f+00/,""),a=Zn(u);return a==i(t).toString()},r}();function Kn(r,t){for(var e=r.toByteArray(),i=0;i<e.length&&e[i]==0;)++i;if(e.length-i!=t-1||e[i]!=2)return null;for(++i;e[i]!=0;)if(++i>=e.length)return null;for(var n="";++i<e.length;){var s=e[i]&255;s<128?n+=String.fromCharCode(s):s>191&&s<224?(n+=String.fromCharCode((s&31)<<6|e[i+1]&63),++i):(n+=String.fromCharCode((s&15)<<12|(e[i+1]&63)<<6|e[i+2]&63),i+=2)}return n}var Wt={md2:"3020300c06082a864886f70d020205000410",md5:"3020300c06082a864886f70d020505000410",sha1:"3021300906052b0e03021a05000414",sha224:"302d300d06096086480165030402040500041c",sha256:"3031300d060960864801650304020105000420",sha384:"3041300d060960864801650304020205000430",sha512:"3051300d060960864801650304020305000440",ripemd160:"3021300906052b2403020105000414"};function zn(r){return Wt[r]||""}function Zn(r){for(var t in Wt)if(Wt.hasOwnProperty(t)){var e=Wt[t],i=e.length;if(r.substr(0,i)==e)return r.substr(i)}return r}/*!
Copyright (c) 2011, Yahoo! Inc. All rights reserved.
Code licensed under the BSD License:
http://developer.yahoo.com/yui/license.html
version: 2.9.0
*/var M={};M.lang={extend:function(r,t,e){if(!t||!r)throw new Error("YAHOO.lang.extend failed, please check that all dependencies are included.");var i=function(){};if(i.prototype=t.prototype,r.prototype=new i,r.prototype.constructor=r,r.superclass=t.prototype,t.prototype.constructor==Object.prototype.constructor&&(t.prototype.constructor=t),e){var n;for(n in e)r.prototype[n]=e[n];var s=function(){},u=["toString","valueOf"];try{/MSIE/.test(navigator.userAgent)&&(s=function(a,l){for(n=0;n<u.length;n=n+1){var v=u[n],g=l[v];typeof g=="function"&&g!=Object.prototype[v]&&(a[v]=g)}})}catch{}s(r.prototype,e)}}};/**
 * @fileOverview
 * @name asn1-1.0.js
 * <AUTHOR>
 * @version asn1 1.0.13 (2017-Jun-02)
 * @since jsrsasign 2.1
 * @license <a href="https://kjur.github.io/jsrsasign/license/">MIT License</a>
 */var p={};(typeof p.asn1>"u"||!p.asn1)&&(p.asn1={});p.asn1.ASN1Util=new function(){this.integerToByteHex=function(r){var t=r.toString(16);return t.length%2==1&&(t="0"+t),t},this.bigIntToMinTwosComplementsHex=function(r){var t=r.toString(16);if(t.substr(0,1)!="-")t.length%2==1?t="0"+t:t.match(/^[0-7]/)||(t="00"+t);else{var e=t.substr(1),i=e.length;i%2==1?i+=1:t.match(/^[0-7]/)||(i+=2);for(var n="",s=0;s<i;s++)n+="f";var u=new F(n,16),a=u.xor(r).add(F.ONE);t=a.toString(16).replace(/^-/,"")}return t},this.getPEMStringFromHex=function(r,t){return hextopem(r,t)},this.newObject=function(r){var t=p,e=t.asn1,i=e.DERBoolean,n=e.DERInteger,s=e.DERBitString,u=e.DEROctetString,a=e.DERNull,l=e.DERObjectIdentifier,v=e.DEREnumerated,g=e.DERUTF8String,x=e.DERNumericString,m=e.DERPrintableString,w=e.DERTeletexString,S=e.DERIA5String,D=e.DERUTCTime,I=e.DERGeneralizedTime,q=e.DERSequence,O=e.DERSet,L=e.DERTaggedObject,N=e.ASN1Util.newObject,it=Object.keys(r);if(it.length!=1)throw"key of param shall be only one.";var B=it[0];if(":bool:int:bitstr:octstr:null:oid:enum:utf8str:numstr:prnstr:telstr:ia5str:utctime:gentime:seq:set:tag:".indexOf(":"+B+":")==-1)throw"undefined key: "+B;if(B=="bool")return new i(r[B]);if(B=="int")return new n(r[B]);if(B=="bitstr")return new s(r[B]);if(B=="octstr")return new u(r[B]);if(B=="null")return new a(r[B]);if(B=="oid")return new l(r[B]);if(B=="enum")return new v(r[B]);if(B=="utf8str")return new g(r[B]);if(B=="numstr")return new x(r[B]);if(B=="prnstr")return new m(r[B]);if(B=="telstr")return new w(r[B]);if(B=="ia5str")return new S(r[B]);if(B=="utctime")return new D(r[B]);if(B=="gentime")return new I(r[B]);if(B=="seq"){for(var rt=r[B],nt=[],Z=0;Z<rt.length;Z++){var ht=N(rt[Z]);nt.push(ht)}return new q({array:nt})}if(B=="set"){for(var rt=r[B],nt=[],Z=0;Z<rt.length;Z++){var ht=N(rt[Z]);nt.push(ht)}return new O({array:nt})}if(B=="tag"){var E=r[B];if(Object.prototype.toString.call(E)==="[object Array]"&&E.length==3){var ee=N(E[2]);return new L({tag:E[0],explicit:E[1],obj:ee})}else{var wt={};if(E.explicit!==void 0&&(wt.explicit=E.explicit),E.tag!==void 0&&(wt.tag=E.tag),E.obj===void 0)throw"obj shall be specified for 'tag'.";return wt.obj=N(E.obj),new L(wt)}}},this.jsonToASN1HEX=function(r){var t=this.newObject(r);return t.getEncodedHex()}};p.asn1.ASN1Util.oidHexToInt=function(r){for(var n="",t=parseInt(r.substr(0,2),16),e=Math.floor(t/40),i=t%40,n=e+"."+i,s="",u=2;u<r.length;u+=2){var a=parseInt(r.substr(u,2),16),l=("00000000"+a.toString(2)).slice(-8);if(s=s+l.substr(1,7),l.substr(0,1)=="0"){var v=new F(s,2);n=n+"."+v.toString(10),s=""}}return n};p.asn1.ASN1Util.oidIntToHex=function(r){var t=function(a){var l=a.toString(16);return l.length==1&&(l="0"+l),l},e=function(a){var l="",v=new F(a,10),g=v.toString(2),x=7-g.length%7;x==7&&(x=0);for(var m="",w=0;w<x;w++)m+="0";g=m+g;for(var w=0;w<g.length-1;w+=7){var S=g.substr(w,7);w!=g.length-7&&(S="1"+S),l+=t(parseInt(S,2))}return l};if(!r.match(/^[0-9.]+$/))throw"malformed oid string: "+r;var i="",n=r.split("."),s=parseInt(n[0])*40+parseInt(n[1]);i+=t(s),n.splice(0,2);for(var u=0;u<n.length;u++)i+=e(n[u]);return i};p.asn1.ASN1Object=function(){var r="";this.getLengthHexFromValue=function(){if(typeof this.hV>"u"||this.hV==null)throw"this.hV is null or undefined.";if(this.hV.length%2==1)throw"value hex must be even length: n="+r.length+",v="+this.hV;var t=this.hV.length/2,e=t.toString(16);if(e.length%2==1&&(e="0"+e),t<128)return e;var i=e.length/2;if(i>15)throw"ASN.1 length too long to represent by 8x: n = "+t.toString(16);var n=128+i;return n.toString(16)+e},this.getEncodedHex=function(){return(this.hTLV==null||this.isModified)&&(this.hV=this.getFreshValueHex(),this.hL=this.getLengthHexFromValue(),this.hTLV=this.hT+this.hL+this.hV,this.isModified=!1),this.hTLV},this.getValueHex=function(){return this.getEncodedHex(),this.hV},this.getFreshValueHex=function(){return""}};p.asn1.DERAbstractString=function(r){p.asn1.DERAbstractString.superclass.constructor.call(this),this.getString=function(){return this.s},this.setString=function(t){this.hTLV=null,this.isModified=!0,this.s=t,this.hV=stohex(this.s)},this.setStringHex=function(t){this.hTLV=null,this.isModified=!0,this.s=null,this.hV=t},this.getFreshValueHex=function(){return this.hV},typeof r<"u"&&(typeof r=="string"?this.setString(r):typeof r.str<"u"?this.setString(r.str):typeof r.hex<"u"&&this.setStringHex(r.hex))};M.lang.extend(p.asn1.DERAbstractString,p.asn1.ASN1Object);p.asn1.DERAbstractTime=function(r){p.asn1.DERAbstractTime.superclass.constructor.call(this),this.localDateToUTC=function(t){utc=t.getTime()+t.getTimezoneOffset()*6e4;var e=new Date(utc);return e},this.formatDate=function(t,e,i){var n=this.zeroPadding,s=this.localDateToUTC(t),u=String(s.getFullYear());e=="utc"&&(u=u.substr(2,2));var a=n(String(s.getMonth()+1),2),l=n(String(s.getDate()),2),v=n(String(s.getHours()),2),g=n(String(s.getMinutes()),2),x=n(String(s.getSeconds()),2),m=u+a+l+v+g+x;if(i===!0){var w=s.getMilliseconds();if(w!=0){var S=n(String(w),3);S=S.replace(/[0]+$/,""),m=m+"."+S}}return m+"Z"},this.zeroPadding=function(t,e){return t.length>=e?t:new Array(e-t.length+1).join("0")+t},this.getString=function(){return this.s},this.setString=function(t){this.hTLV=null,this.isModified=!0,this.s=t,this.hV=stohex(t)},this.setByDateValue=function(t,e,i,n,s,u){var a=new Date(Date.UTC(t,e-1,i,n,s,u,0));this.setByDate(a)},this.getFreshValueHex=function(){return this.hV}};M.lang.extend(p.asn1.DERAbstractTime,p.asn1.ASN1Object);p.asn1.DERAbstractStructured=function(r){p.asn1.DERAbstractString.superclass.constructor.call(this),this.setByASN1ObjectArray=function(t){this.hTLV=null,this.isModified=!0,this.asn1Array=t},this.appendASN1Object=function(t){this.hTLV=null,this.isModified=!0,this.asn1Array.push(t)},this.asn1Array=new Array,typeof r<"u"&&typeof r.array<"u"&&(this.asn1Array=r.array)};M.lang.extend(p.asn1.DERAbstractStructured,p.asn1.ASN1Object);p.asn1.DERBoolean=function(){p.asn1.DERBoolean.superclass.constructor.call(this),this.hT="01",this.hTLV="0101ff"};M.lang.extend(p.asn1.DERBoolean,p.asn1.ASN1Object);p.asn1.DERInteger=function(r){p.asn1.DERInteger.superclass.constructor.call(this),this.hT="02",this.setByBigInteger=function(t){this.hTLV=null,this.isModified=!0,this.hV=p.asn1.ASN1Util.bigIntToMinTwosComplementsHex(t)},this.setByInteger=function(t){var e=new F(String(t),10);this.setByBigInteger(e)},this.setValueHex=function(t){this.hV=t},this.getFreshValueHex=function(){return this.hV},typeof r<"u"&&(typeof r.bigint<"u"?this.setByBigInteger(r.bigint):typeof r.int<"u"?this.setByInteger(r.int):typeof r=="number"?this.setByInteger(r):typeof r.hex<"u"&&this.setValueHex(r.hex))};M.lang.extend(p.asn1.DERInteger,p.asn1.ASN1Object);p.asn1.DERBitString=function(r){if(r!==void 0&&typeof r.obj<"u"){var t=p.asn1.ASN1Util.newObject(r.obj);r.hex="00"+t.getEncodedHex()}p.asn1.DERBitString.superclass.constructor.call(this),this.hT="03",this.setHexValueIncludingUnusedBits=function(e){this.hTLV=null,this.isModified=!0,this.hV=e},this.setUnusedBitsAndHexValue=function(e,i){if(e<0||7<e)throw"unused bits shall be from 0 to 7: u = "+e;var n="0"+e;this.hTLV=null,this.isModified=!0,this.hV=n+i},this.setByBinaryString=function(e){e=e.replace(/0+$/,"");var i=8-e.length%8;i==8&&(i=0);for(var n=0;n<=i;n++)e+="0";for(var s="",n=0;n<e.length-1;n+=8){var u=e.substr(n,8),a=parseInt(u,2).toString(16);a.length==1&&(a="0"+a),s+=a}this.hTLV=null,this.isModified=!0,this.hV="0"+i+s},this.setByBooleanArray=function(e){for(var i="",n=0;n<e.length;n++)e[n]==!0?i+="1":i+="0";this.setByBinaryString(i)},this.newFalseArray=function(e){for(var i=new Array(e),n=0;n<e;n++)i[n]=!1;return i},this.getFreshValueHex=function(){return this.hV},typeof r<"u"&&(typeof r=="string"&&r.toLowerCase().match(/^[0-9a-f]+$/)?this.setHexValueIncludingUnusedBits(r):typeof r.hex<"u"?this.setHexValueIncludingUnusedBits(r.hex):typeof r.bin<"u"?this.setByBinaryString(r.bin):typeof r.array<"u"&&this.setByBooleanArray(r.array))};M.lang.extend(p.asn1.DERBitString,p.asn1.ASN1Object);p.asn1.DEROctetString=function(r){if(r!==void 0&&typeof r.obj<"u"){var t=p.asn1.ASN1Util.newObject(r.obj);r.hex=t.getEncodedHex()}p.asn1.DEROctetString.superclass.constructor.call(this,r),this.hT="04"};M.lang.extend(p.asn1.DEROctetString,p.asn1.DERAbstractString);p.asn1.DERNull=function(){p.asn1.DERNull.superclass.constructor.call(this),this.hT="05",this.hTLV="0500"};M.lang.extend(p.asn1.DERNull,p.asn1.ASN1Object);p.asn1.DERObjectIdentifier=function(r){var t=function(i){var n=i.toString(16);return n.length==1&&(n="0"+n),n},e=function(i){var n="",s=new F(i,10),u=s.toString(2),a=7-u.length%7;a==7&&(a=0);for(var l="",v=0;v<a;v++)l+="0";u=l+u;for(var v=0;v<u.length-1;v+=7){var g=u.substr(v,7);v!=u.length-7&&(g="1"+g),n+=t(parseInt(g,2))}return n};p.asn1.DERObjectIdentifier.superclass.constructor.call(this),this.hT="06",this.setValueHex=function(i){this.hTLV=null,this.isModified=!0,this.s=null,this.hV=i},this.setValueOidString=function(i){if(!i.match(/^[0-9.]+$/))throw"malformed oid string: "+i;var n="",s=i.split("."),u=parseInt(s[0])*40+parseInt(s[1]);n+=t(u),s.splice(0,2);for(var a=0;a<s.length;a++)n+=e(s[a]);this.hTLV=null,this.isModified=!0,this.s=null,this.hV=n},this.setValueName=function(i){var n=p.asn1.x509.OID.name2oid(i);if(n!=="")this.setValueOidString(n);else throw"DERObjectIdentifier oidName undefined: "+i},this.getFreshValueHex=function(){return this.hV},r!==void 0&&(typeof r=="string"?r.match(/^[0-2].[0-9.]+$/)?this.setValueOidString(r):this.setValueName(r):r.oid!==void 0?this.setValueOidString(r.oid):r.hex!==void 0?this.setValueHex(r.hex):r.name!==void 0&&this.setValueName(r.name))};M.lang.extend(p.asn1.DERObjectIdentifier,p.asn1.ASN1Object);p.asn1.DEREnumerated=function(r){p.asn1.DEREnumerated.superclass.constructor.call(this),this.hT="0a",this.setByBigInteger=function(t){this.hTLV=null,this.isModified=!0,this.hV=p.asn1.ASN1Util.bigIntToMinTwosComplementsHex(t)},this.setByInteger=function(t){var e=new F(String(t),10);this.setByBigInteger(e)},this.setValueHex=function(t){this.hV=t},this.getFreshValueHex=function(){return this.hV},typeof r<"u"&&(typeof r.int<"u"?this.setByInteger(r.int):typeof r=="number"?this.setByInteger(r):typeof r.hex<"u"&&this.setValueHex(r.hex))};M.lang.extend(p.asn1.DEREnumerated,p.asn1.ASN1Object);p.asn1.DERUTF8String=function(r){p.asn1.DERUTF8String.superclass.constructor.call(this,r),this.hT="0c"};M.lang.extend(p.asn1.DERUTF8String,p.asn1.DERAbstractString);p.asn1.DERNumericString=function(r){p.asn1.DERNumericString.superclass.constructor.call(this,r),this.hT="12"};M.lang.extend(p.asn1.DERNumericString,p.asn1.DERAbstractString);p.asn1.DERPrintableString=function(r){p.asn1.DERPrintableString.superclass.constructor.call(this,r),this.hT="13"};M.lang.extend(p.asn1.DERPrintableString,p.asn1.DERAbstractString);p.asn1.DERTeletexString=function(r){p.asn1.DERTeletexString.superclass.constructor.call(this,r),this.hT="14"};M.lang.extend(p.asn1.DERTeletexString,p.asn1.DERAbstractString);p.asn1.DERIA5String=function(r){p.asn1.DERIA5String.superclass.constructor.call(this,r),this.hT="16"};M.lang.extend(p.asn1.DERIA5String,p.asn1.DERAbstractString);p.asn1.DERUTCTime=function(r){p.asn1.DERUTCTime.superclass.constructor.call(this,r),this.hT="17",this.setByDate=function(t){this.hTLV=null,this.isModified=!0,this.date=t,this.s=this.formatDate(this.date,"utc"),this.hV=stohex(this.s)},this.getFreshValueHex=function(){return typeof this.date>"u"&&typeof this.s>"u"&&(this.date=new Date,this.s=this.formatDate(this.date,"utc"),this.hV=stohex(this.s)),this.hV},r!==void 0&&(r.str!==void 0?this.setString(r.str):typeof r=="string"&&r.match(/^[0-9]{12}Z$/)?this.setString(r):r.hex!==void 0?this.setStringHex(r.hex):r.date!==void 0&&this.setByDate(r.date))};M.lang.extend(p.asn1.DERUTCTime,p.asn1.DERAbstractTime);p.asn1.DERGeneralizedTime=function(r){p.asn1.DERGeneralizedTime.superclass.constructor.call(this,r),this.hT="18",this.withMillis=!1,this.setByDate=function(t){this.hTLV=null,this.isModified=!0,this.date=t,this.s=this.formatDate(this.date,"gen",this.withMillis),this.hV=stohex(this.s)},this.getFreshValueHex=function(){return this.date===void 0&&this.s===void 0&&(this.date=new Date,this.s=this.formatDate(this.date,"gen",this.withMillis),this.hV=stohex(this.s)),this.hV},r!==void 0&&(r.str!==void 0?this.setString(r.str):typeof r=="string"&&r.match(/^[0-9]{14}Z$/)?this.setString(r):r.hex!==void 0?this.setStringHex(r.hex):r.date!==void 0&&this.setByDate(r.date),r.millis===!0&&(this.withMillis=!0))};M.lang.extend(p.asn1.DERGeneralizedTime,p.asn1.DERAbstractTime);p.asn1.DERSequence=function(r){p.asn1.DERSequence.superclass.constructor.call(this,r),this.hT="30",this.getFreshValueHex=function(){for(var t="",e=0;e<this.asn1Array.length;e++){var i=this.asn1Array[e];t+=i.getEncodedHex()}return this.hV=t,this.hV}};M.lang.extend(p.asn1.DERSequence,p.asn1.DERAbstractStructured);p.asn1.DERSet=function(r){p.asn1.DERSet.superclass.constructor.call(this,r),this.hT="31",this.sortFlag=!0,this.getFreshValueHex=function(){for(var t=new Array,e=0;e<this.asn1Array.length;e++){var i=this.asn1Array[e];t.push(i.getEncodedHex())}return this.sortFlag==!0&&t.sort(),this.hV=t.join(""),this.hV},typeof r<"u"&&typeof r.sortflag<"u"&&r.sortflag==!1&&(this.sortFlag=!1)};M.lang.extend(p.asn1.DERSet,p.asn1.DERAbstractStructured);p.asn1.DERTaggedObject=function(r){p.asn1.DERTaggedObject.superclass.constructor.call(this),this.hT="a0",this.hV="",this.isExplicit=!0,this.asn1Object=null,this.setASN1Object=function(t,e,i){this.hT=e,this.isExplicit=t,this.asn1Object=i,this.isExplicit?(this.hV=this.asn1Object.getEncodedHex(),this.hTLV=null,this.isModified=!0):(this.hV=null,this.hTLV=i.getEncodedHex(),this.hTLV=this.hTLV.replace(/^../,e),this.isModified=!1)},this.getFreshValueHex=function(){return this.hV},typeof r<"u"&&(typeof r.tag<"u"&&(this.hT=r.tag),typeof r.explicit<"u"&&(this.isExplicit=r.explicit),typeof r.obj<"u"&&(this.asn1Object=r.obj,this.setASN1Object(this.isExplicit,this.hT,this.asn1Object)))};M.lang.extend(p.asn1.DERTaggedObject,p.asn1.ASN1Object);var $n=globalThis&&globalThis.__extends||function(){var r=function(t,e){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(i,n){i.__proto__=n}||function(i,n){for(var s in n)Object.prototype.hasOwnProperty.call(n,s)&&(i[s]=n[s])},r(t,e)};return function(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");r(t,e);function i(){this.constructor=t}t.prototype=e===null?Object.create(e):(i.prototype=e.prototype,new i)}}(),_e=function(r){$n(t,r);function t(e){var i=r.call(this)||this;return e&&(typeof e=="string"?i.parseKey(e):(t.hasPrivateKeyProperty(e)||t.hasPublicKeyProperty(e))&&i.parsePropertiesFrom(e)),i}return t.prototype.parseKey=function(e){try{var i=0,n=0,s=/^\s*(?:[0-9A-Fa-f][0-9A-Fa-f]\s*)+$/,u=s.test(e)?In.decode(e):ce.unarmor(e),a=qn.decode(u);if(a.sub.length===3&&(a=a.sub[2].sub[0]),a.sub.length===9){i=a.sub[1].getHexStringValue(),this.n=P(i,16),n=a.sub[2].getHexStringValue(),this.e=parseInt(n,16);var l=a.sub[3].getHexStringValue();this.d=P(l,16);var v=a.sub[4].getHexStringValue();this.p=P(v,16);var g=a.sub[5].getHexStringValue();this.q=P(g,16);var x=a.sub[6].getHexStringValue();this.dmp1=P(x,16);var m=a.sub[7].getHexStringValue();this.dmq1=P(m,16);var w=a.sub[8].getHexStringValue();this.coeff=P(w,16)}else if(a.sub.length===2)if(a.sub[0].sub){var S=a.sub[1],D=S.sub[0];i=D.sub[0].getHexStringValue(),this.n=P(i,16),n=D.sub[1].getHexStringValue(),this.e=parseInt(n,16)}else i=a.sub[0].getHexStringValue(),this.n=P(i,16),n=a.sub[1].getHexStringValue(),this.e=parseInt(n,16);else return!1;return!0}catch{return!1}},t.prototype.getPrivateBaseKey=function(){var e={array:[new p.asn1.DERInteger({int:0}),new p.asn1.DERInteger({bigint:this.n}),new p.asn1.DERInteger({int:this.e}),new p.asn1.DERInteger({bigint:this.d}),new p.asn1.DERInteger({bigint:this.p}),new p.asn1.DERInteger({bigint:this.q}),new p.asn1.DERInteger({bigint:this.dmp1}),new p.asn1.DERInteger({bigint:this.dmq1}),new p.asn1.DERInteger({bigint:this.coeff})]},i=new p.asn1.DERSequence(e);return i.getEncodedHex()},t.prototype.getPrivateBaseKeyB64=function(){return Qt(this.getPrivateBaseKey())},t.prototype.getPublicBaseKey=function(){var e=new p.asn1.DERSequence({array:[new p.asn1.DERObjectIdentifier({oid:"1.2.840.113549.1.1.1"}),new p.asn1.DERNull]}),i=new p.asn1.DERSequence({array:[new p.asn1.DERInteger({bigint:this.n}),new p.asn1.DERInteger({int:this.e})]}),n=new p.asn1.DERBitString({hex:"00"+i.getEncodedHex()}),s=new p.asn1.DERSequence({array:[e,n]});return s.getEncodedHex()},t.prototype.getPublicBaseKeyB64=function(){return Qt(this.getPublicBaseKey())},t.wordwrap=function(e,i){if(i=i||64,!e)return e;var n="(.{1,"+i+`})( +|$
?)|(.{1,`+i+"})";return e.match(RegExp(n,"g")).join(`
`)},t.prototype.getPrivateKey=function(){var e=`-----BEGIN RSA PRIVATE KEY-----
`;return e+=t.wordwrap(this.getPrivateBaseKeyB64())+`
`,e+="-----END RSA PRIVATE KEY-----",e},t.prototype.getPublicKey=function(){var e=`-----BEGIN PUBLIC KEY-----
`;return e+=t.wordwrap(this.getPublicBaseKeyB64())+`
`,e+="-----END PUBLIC KEY-----",e},t.hasPublicKeyProperty=function(e){return e=e||{},e.hasOwnProperty("n")&&e.hasOwnProperty("e")},t.hasPrivateKeyProperty=function(e){return e=e||{},e.hasOwnProperty("n")&&e.hasOwnProperty("e")&&e.hasOwnProperty("d")&&e.hasOwnProperty("p")&&e.hasOwnProperty("q")&&e.hasOwnProperty("dmp1")&&e.hasOwnProperty("dmq1")&&e.hasOwnProperty("coeff")},t.prototype.parsePropertiesFrom=function(e){this.n=e.n,this.e=e.e,e.hasOwnProperty("d")&&(this.d=e.d,this.p=e.p,this.q=e.q,this.dmp1=e.dmp1,this.dmq1=e.dmq1,this.coeff=e.coeff)},t}(Un),le,Gn=typeof process<"u"?(le=process.env)===null||le===void 0?void 0:le.npm_package_version:void 0,Yn=function(){function r(t){t===void 0&&(t={}),t=t||{},this.default_key_size=t.default_key_size?parseInt(t.default_key_size,10):1024,this.default_public_exponent=t.default_public_exponent||"010001",this.log=t.log||!1,this.key=null}return r.prototype.setKey=function(t){this.log&&this.key&&console.warn("A key was already set, overriding existing."),this.key=new _e(t)},r.prototype.setPrivateKey=function(t){this.setKey(t)},r.prototype.setPublicKey=function(t){this.setKey(t)},r.prototype.decrypt=function(t){try{return this.getKey().decrypt(qe(t))}catch{return!1}},r.prototype.encrypt=function(t){try{return Qt(this.getKey().encrypt(t))}catch{return!1}},r.prototype.sign=function(t,e,i){try{return Qt(this.getKey().sign(t,e,i))}catch{return!1}},r.prototype.verify=function(t,e,i){try{return this.getKey().verify(t,qe(e),i)}catch{return!1}},r.prototype.getKey=function(t){if(!this.key){if(this.key=new _e,t&&{}.toString.call(t)==="[object Function]"){this.key.generateAsync(this.default_key_size,this.default_public_exponent,t);return}this.key.generate(this.default_key_size,this.default_public_exponent)}return this.key},r.prototype.getPrivateKey=function(){return this.getKey().getPrivateKey()},r.prototype.getPrivateKeyB64=function(){return this.getKey().getPrivateBaseKeyB64()},r.prototype.getPublicKey=function(){return this.getKey().getPublicKey()},r.prototype.getPublicKeyB64=function(){return this.getKey().getPublicBaseKeyB64()},r.version=Gn,r}();class Wn{constructor(t){we(this,"type");this.type=t}Encrypt(t,e){switch(this.type){case"sm2":return this.sm2Encrypt(t,e);case"rsa":return this.rsaEncrypt(t,e)}}sm2Encrypt(t,e){return Dn.sm2.doEncrypt(t,e,0)}rsaEncrypt(t,e){const i=new Yn;return i.setPublicKey(e),i.encrypt(t)}}export{Wn as C};
