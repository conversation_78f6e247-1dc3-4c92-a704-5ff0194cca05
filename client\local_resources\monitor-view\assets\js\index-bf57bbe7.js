import{d as x,r as h,a3 as y,h as i,o as t,c as p,B as o,w as l,a as c,t as r,j as N,aj as g,y as k,F as C,P as B}from"./main-dd669ad8.js";import{_ as L}from"./_plugin-vue_export-helper-361d09b5.js";const D={class:"device-table"},F=x({__name:"index",props:{trendData:{default:()=>[]},pointList:{default:[]}},setup(s){const m=s,d=h(0);y(()=>m.pointList,e=>{e&&d.value++});const b=e=>e.deviceName+":"+(e!=null&&e.aliasCodeName?e.aliasCodeName:e.codeName);return(e,E)=>{const _=i("el-table-column"),f=i("el-table");return t(),p("div",D,[(t(),o(f,{data:s.trendData,border:"","cell-class-name":({row:n})=>n.isExtend?"external__cell":"",key:d.value},{default:l(()=>{var n;return[(n=s.pointList)!=null&&n.length?(t(),o(_,{key:0,prop:"time",label:"\u65F6\u95F4",align:"center",width:"180"},{default:l(({row:a})=>[c("span",null,r(a.isExtend?a.time:N(g)(a.time)),1)]),_:1})):k("",!0),(t(!0),p(C,null,B(s.pointList,(a,u)=>(t(),o(_,{key:u,prop:a.value},{header:l(()=>[c("span",null,r(b(a)),1)]),default:l(({row:v})=>[c("span",null,r(v.attributes[u].value),1)]),_:2},1032,["prop"]))),128))]}),_:1},8,["data","cell-class-name"]))])}}});const T=L(F,[["__scopeId","data-v-a54bb7fb"]]);export{T as default};
