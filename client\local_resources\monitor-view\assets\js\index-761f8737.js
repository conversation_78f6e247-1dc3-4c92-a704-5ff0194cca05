import{ae as ca,d as fs,aJ as ls,r as or,a3 as g0,f as os,a7 as cs,h as Tr,o as tt,c as _0,b as Qe,a as Cn,w as xr,i as Vt,F as hs,P as us,j as T0,aw as xs,B as Gt,y as On,aA as ds,aR as vs,ak as Xt,aj as E0,aa as ps}from"./main-dd669ad8.js";import{c as ms}from"./cloneDeep-174273b5.js";import gs from"./index-9b3dae6b.js";import _s from"./index-bf57bbe7.js";import Ts from"./index-59e587c7.js";import{l as Es,m as ws,n as Ss,o as As}from"./monitor-1739c0cb.js";import{_ as Fs}from"./_plugin-vue_export-helper-361d09b5.js";import"./GroupEdit-84622418.js";import"./common-9b09fb7e.js";import"./PointEdit-ec03476c.js";import"./product-327919f5.js";import"./device-b598f94e.js";import"./project-54c43e68.js";import"./useHandleData-6b868160.js";import"./LineChart-11127dd5.js";import"./index-2ff848b4.js";import"./BarChart-a2925762.js";const w0=async(e,t,r={},n=!0,a=".xlsx")=>{n&&ca({title:"\u6E29\u99A8\u63D0\u793A",message:"\u5982\u679C\u6570\u636E\u5E9E\u5927\u4F1A\u5BFC\u81F4\u4E0B\u8F7D\u7F13\u6162\u54E6\uFF0C\u8BF7\u60A8\u8010\u5FC3\u7B49\u5F85\uFF01",type:"info",duration:3e3});try{const i=await e(r),s=new Blob([i]);if("msSaveOrOpenBlob"in navigator)return window.navigator.msSaveOrOpenBlob(s,t+a);const f=window.URL.createObjectURL(s),o=document.createElement("a");o.style.display="none",o.download=`${t}${a}`,o.href=f,document.body.appendChild(o),o.click(),document.body.removeChild(o),window.URL.revokeObjectURL(f)}catch{}};/*! xlsx.js (C) 2013-present SheetJS -- http://sheetjs.com */var Qt={};Qt.version="0.18.5";var ha=1252,ys=[874,932,936,949,950,1250,1251,1252,1253,1254,1255,1256,1257,1258,1e4],ua=function(e){ys.indexOf(e)!=-1&&(ha=e)};function Cs(){ua(1252)}var Ot=function(e){ua(e)};function Os(){Ot(1200),Cs()}function Ds(e){for(var t=[],r=0;r<e.length>>1;++r)t[r]=String.fromCharCode(e.charCodeAt(2*r+1)+(e.charCodeAt(2*r)<<8));return t.join("")}var $t=function(t){return String.fromCharCode(t)},S0=function(t){return String.fromCharCode(t)},Xr,Lr="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";function Dt(e){for(var t="",r=0,n=0,a=0,i=0,s=0,f=0,o=0,l=0;l<e.length;)r=e.charCodeAt(l++),i=r>>2,n=e.charCodeAt(l++),s=(r&3)<<4|n>>4,a=e.charCodeAt(l++),f=(n&15)<<2|a>>6,o=a&63,isNaN(n)?f=o=64:isNaN(a)&&(o=64),t+=Lr.charAt(i)+Lr.charAt(s)+Lr.charAt(f)+Lr.charAt(o);return t}function Nr(e){var t="",r=0,n=0,a=0,i=0,s=0,f=0,o=0;e=e.replace(/[^\w\+\/\=]/g,"");for(var l=0;l<e.length;)i=Lr.indexOf(e.charAt(l++)),s=Lr.indexOf(e.charAt(l++)),r=i<<2|s>>4,t+=String.fromCharCode(r),f=Lr.indexOf(e.charAt(l++)),n=(s&15)<<4|f>>2,f!==64&&(t+=String.fromCharCode(n)),o=Lr.indexOf(e.charAt(l++)),a=(f&3)<<6|o,o!==64&&(t+=String.fromCharCode(a));return t}var ve=function(){return typeof Buffer<"u"&&typeof process<"u"&&typeof process.versions<"u"&&!!process.versions.node}(),Ir=function(){if(typeof Buffer<"u"){var e=!Buffer.from;if(!e)try{Buffer.from("foo","utf8")}catch{e=!0}return e?function(t,r){return r?new Buffer(t,r):new Buffer(t)}:Buffer.from.bind(Buffer)}return function(){}}();function Kr(e){return ve?Buffer.alloc?Buffer.alloc(e):new Buffer(e):typeof Uint8Array<"u"?new Uint8Array(e):new Array(e)}function A0(e){return ve?Buffer.allocUnsafe?Buffer.allocUnsafe(e):new Buffer(e):typeof Uint8Array<"u"?new Uint8Array(e):new Array(e)}var pr=function(t){return ve?Ir(t,"binary"):t.split("").map(function(r){return r.charCodeAt(0)&255})};function dn(e){if(typeof ArrayBuffer>"u")return pr(e);for(var t=new ArrayBuffer(e.length),r=new Uint8Array(t),n=0;n!=e.length;++n)r[n]=e.charCodeAt(n)&255;return t}function Pt(e){if(Array.isArray(e))return e.map(function(n){return String.fromCharCode(n)}).join("");for(var t=[],r=0;r<e.length;++r)t[r]=String.fromCharCode(e[r]);return t.join("")}function Rs(e){if(typeof Uint8Array>"u")throw new Error("Unsupported");return new Uint8Array(e)}var He=ve?function(e){return Buffer.concat(e.map(function(t){return Buffer.isBuffer(t)?t:Ir(t)}))}:function(e){if(typeof Uint8Array<"u"){var t=0,r=0;for(t=0;t<e.length;++t)r+=e[t].length;var n=new Uint8Array(r),a=0;for(t=0,r=0;t<e.length;r+=a,++t)if(a=e[t].length,e[t]instanceof Uint8Array)n.set(e[t],r);else{if(typeof e[t]=="string")throw"wtf";n.set(new Uint8Array(e[t]),r)}return n}return[].concat.apply([],e.map(function(i){return Array.isArray(i)?i:[].slice.call(i)}))};function Ns(e){for(var t=[],r=0,n=e.length+250,a=Kr(e.length+255),i=0;i<e.length;++i){var s=e.charCodeAt(i);if(s<128)a[r++]=s;else if(s<2048)a[r++]=192|s>>6&31,a[r++]=128|s&63;else if(s>=55296&&s<57344){s=(s&1023)+64;var f=e.charCodeAt(++i)&1023;a[r++]=240|s>>8&7,a[r++]=128|s>>2&63,a[r++]=128|f>>6&15|(s&3)<<4,a[r++]=128|f&63}else a[r++]=224|s>>12&15,a[r++]=128|s>>6&63,a[r++]=128|s&63;r>n&&(t.push(a.slice(0,r)),r=0,a=Kr(65535),n=65530)}return t.push(a.slice(0,r)),He(t)}var wt=/\u0000/g,zt=/[\u0001-\u0006]/g;function lt(e){for(var t="",r=e.length-1;r>=0;)t+=e.charAt(r--);return t}function mr(e,t){var r=""+e;return r.length>=t?r:De("0",t-r.length)+r}function Hn(e,t){var r=""+e;return r.length>=t?r:De(" ",t-r.length)+r}function en(e,t){var r=""+e;return r.length>=t?r:r+De(" ",t-r.length)}function ks(e,t){var r=""+Math.round(e);return r.length>=t?r:De("0",t-r.length)+r}function Is(e,t){var r=""+e;return r.length>=t?r:De("0",t-r.length)+r}var F0=Math.pow(2,32);function nt(e,t){if(e>F0||e<-F0)return ks(e,t);var r=Math.round(e);return Is(r,t)}function rn(e,t){return t=t||0,e.length>=7+t&&(e.charCodeAt(t)|32)===103&&(e.charCodeAt(t+1)|32)===101&&(e.charCodeAt(t+2)|32)===110&&(e.charCodeAt(t+3)|32)===101&&(e.charCodeAt(t+4)|32)===114&&(e.charCodeAt(t+5)|32)===97&&(e.charCodeAt(t+6)|32)===108}var y0=[["Sun","Sunday"],["Mon","Monday"],["Tue","Tuesday"],["Wed","Wednesday"],["Thu","Thursday"],["Fri","Friday"],["Sat","Saturday"]],Dn=[["J","Jan","January"],["F","Feb","February"],["M","Mar","March"],["A","Apr","April"],["M","May","May"],["J","Jun","June"],["J","Jul","July"],["A","Aug","August"],["S","Sep","September"],["O","Oct","October"],["N","Nov","November"],["D","Dec","December"]];function Ps(e){return e||(e={}),e[0]="General",e[1]="0",e[2]="0.00",e[3]="#,##0",e[4]="#,##0.00",e[9]="0%",e[10]="0.00%",e[11]="0.00E+00",e[12]="# ?/?",e[13]="# ??/??",e[14]="m/d/yy",e[15]="d-mmm-yy",e[16]="d-mmm",e[17]="mmm-yy",e[18]="h:mm AM/PM",e[19]="h:mm:ss AM/PM",e[20]="h:mm",e[21]="h:mm:ss",e[22]="m/d/yy h:mm",e[37]="#,##0 ;(#,##0)",e[38]="#,##0 ;[Red](#,##0)",e[39]="#,##0.00;(#,##0.00)",e[40]="#,##0.00;[Red](#,##0.00)",e[45]="mm:ss",e[46]="[h]:mm:ss",e[47]="mmss.0",e[48]="##0.0E+0",e[49]="@",e[56]='"\u4E0A\u5348/\u4E0B\u5348 "hh"\u6642"mm"\u5206"ss"\u79D2 "',e}var Re={0:"General",1:"0",2:"0.00",3:"#,##0",4:"#,##0.00",9:"0%",10:"0.00%",11:"0.00E+00",12:"# ?/?",13:"# ??/??",14:"m/d/yy",15:"d-mmm-yy",16:"d-mmm",17:"mmm-yy",18:"h:mm AM/PM",19:"h:mm:ss AM/PM",20:"h:mm",21:"h:mm:ss",22:"m/d/yy h:mm",37:"#,##0 ;(#,##0)",38:"#,##0 ;[Red](#,##0)",39:"#,##0.00;(#,##0.00)",40:"#,##0.00;[Red](#,##0.00)",45:"mm:ss",46:"[h]:mm:ss",47:"mmss.0",48:"##0.0E+0",49:"@",56:'"\u4E0A\u5348/\u4E0B\u5348 "hh"\u6642"mm"\u5206"ss"\u79D2 "'},C0={5:37,6:38,7:39,8:40,23:0,24:0,25:0,26:0,27:14,28:14,29:14,30:14,31:14,50:14,51:14,52:14,53:14,54:14,55:14,56:14,57:14,58:14,59:1,60:2,61:3,62:4,67:9,68:10,69:12,70:13,71:14,72:14,73:15,74:16,75:17,76:20,77:21,78:22,79:45,80:46,81:47,82:0},Ls={5:'"$"#,##0_);\\("$"#,##0\\)',63:'"$"#,##0_);\\("$"#,##0\\)',6:'"$"#,##0_);[Red]\\("$"#,##0\\)',64:'"$"#,##0_);[Red]\\("$"#,##0\\)',7:'"$"#,##0.00_);\\("$"#,##0.00\\)',65:'"$"#,##0.00_);\\("$"#,##0.00\\)',8:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',66:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',41:'_(* #,##0_);_(* \\(#,##0\\);_(* "-"_);_(@_)',42:'_("$"* #,##0_);_("$"* \\(#,##0\\);_("$"* "-"_);_(@_)',43:'_(* #,##0.00_);_(* \\(#,##0.00\\);_(* "-"??_);_(@_)',44:'_("$"* #,##0.00_);_("$"* \\(#,##0.00\\);_("$"* "-"??_);_(@_)'};function tn(e,t,r){for(var n=e<0?-1:1,a=e*n,i=0,s=1,f=0,o=1,l=0,c=0,v=Math.floor(a);l<t&&(v=Math.floor(a),f=v*s+i,c=v*l+o,!(a-v<5e-8));)a=1/(a-v),i=s,s=f,o=l,l=c;if(c>t&&(l>t?(c=o,f=i):(c=l,f=s)),!r)return[0,n*f,c];var x=Math.floor(n*f/c);return[x,n*f-x*c,c]}function Kt(e,t,r){if(e>2958465||e<0)return null;var n=e|0,a=Math.floor(86400*(e-n)),i=0,s=[],f={D:n,T:a,u:86400*(e-n)-a,y:0,m:0,d:0,H:0,M:0,S:0,q:0};if(Math.abs(f.u)<1e-6&&(f.u=0),t&&t.date1904&&(n+=1462),f.u>.9999&&(f.u=0,++a==86400&&(f.T=a=0,++n,++f.D)),n===60)s=r?[1317,10,29]:[1900,2,29],i=3;else if(n===0)s=r?[1317,8,29]:[1900,1,0],i=6;else{n>60&&--n;var o=new Date(1900,0,1);o.setDate(o.getDate()+n-1),s=[o.getFullYear(),o.getMonth()+1,o.getDate()],i=o.getDay(),n<60&&(i=(i+6)%7),r&&(i=Vs(o,s))}return f.y=s[0],f.m=s[1],f.d=s[2],f.S=a%60,a=Math.floor(a/60),f.M=a%60,a=Math.floor(a/60),f.H=a,f.q=i,f}var xa=new Date(1899,11,31,0,0,0),Bs=xa.getTime(),Ms=new Date(1900,2,1,0,0,0);function da(e,t){var r=e.getTime();return t?r-=1461*24*60*60*1e3:e>=Ms&&(r+=24*60*60*1e3),(r-(Bs+(e.getTimezoneOffset()-xa.getTimezoneOffset())*6e4))/(24*60*60*1e3)}function Vn(e){return e.indexOf(".")==-1?e:e.replace(/(?:\.0*|(\.\d*[1-9])0+)$/,"$1")}function Us(e){return e.indexOf("E")==-1?e:e.replace(/(?:\.0*|(\.\d*[1-9])0+)[Ee]/,"$1E").replace(/(E[+-])(\d)$/,"$10$2")}function bs(e){var t=e<0?12:11,r=Vn(e.toFixed(12));return r.length<=t||(r=e.toPrecision(10),r.length<=t)?r:e.toExponential(5)}function Ws(e){var t=Vn(e.toFixed(11));return t.length>(e<0?12:11)||t==="0"||t==="-0"?e.toPrecision(6):t}function Hs(e){var t=Math.floor(Math.log(Math.abs(e))*Math.LOG10E),r;return t>=-4&&t<=-1?r=e.toPrecision(10+t):Math.abs(t)<=9?r=bs(e):t===10?r=e.toFixed(10).substr(0,12):r=Ws(e),Vn(Us(r.toUpperCase()))}function Mn(e,t){switch(typeof e){case"string":return e;case"boolean":return e?"TRUE":"FALSE";case"number":return(e|0)===e?e.toString(10):Hs(e);case"undefined":return"";case"object":if(e==null)return"";if(e instanceof Date)return Mr(14,da(e,t&&t.date1904),t)}throw new Error("unsupported value in General format: "+e)}function Vs(e,t){t[0]-=581;var r=e.getDay();return e<60&&(r=(r+6)%7),r}function Gs(e,t,r,n){var a="",i=0,s=0,f=r.y,o,l=0;switch(e){case 98:f=r.y+543;case 121:switch(t.length){case 1:case 2:o=f%100,l=2;break;default:o=f%1e4,l=4;break}break;case 109:switch(t.length){case 1:case 2:o=r.m,l=t.length;break;case 3:return Dn[r.m-1][1];case 5:return Dn[r.m-1][0];default:return Dn[r.m-1][2]}break;case 100:switch(t.length){case 1:case 2:o=r.d,l=t.length;break;case 3:return y0[r.q][0];default:return y0[r.q][1]}break;case 104:switch(t.length){case 1:case 2:o=1+(r.H+11)%12,l=t.length;break;default:throw"bad hour format: "+t}break;case 72:switch(t.length){case 1:case 2:o=r.H,l=t.length;break;default:throw"bad hour format: "+t}break;case 77:switch(t.length){case 1:case 2:o=r.M,l=t.length;break;default:throw"bad minute format: "+t}break;case 115:if(t!="s"&&t!="ss"&&t!=".0"&&t!=".00"&&t!=".000")throw"bad second format: "+t;return r.u===0&&(t=="s"||t=="ss")?mr(r.S,t.length):(n>=2?s=n===3?1e3:100:s=n===1?10:1,i=Math.round(s*(r.S+r.u)),i>=60*s&&(i=0),t==="s"?i===0?"0":""+i/s:(a=mr(i,2+n),t==="ss"?a.substr(0,2):"."+a.substr(2,t.length-1)));case 90:switch(t){case"[h]":case"[hh]":o=r.D*24+r.H;break;case"[m]":case"[mm]":o=(r.D*24+r.H)*60+r.M;break;case"[s]":case"[ss]":o=((r.D*24+r.H)*60+r.M)*60+Math.round(r.S+r.u);break;default:throw"bad abstime format: "+t}l=t.length===3?1:2;break;case 101:o=f,l=1;break}var c=l>0?mr(o,l):"";return c}function Br(e){var t=3;if(e.length<=t)return e;for(var r=e.length%t,n=e.substr(0,r);r!=e.length;r+=t)n+=(n.length>0?",":"")+e.substr(r,t);return n}var va=/%/g;function Xs(e,t,r){var n=t.replace(va,""),a=t.length-n.length;return Or(e,n,r*Math.pow(10,2*a))+De("%",a)}function $s(e,t,r){for(var n=t.length-1;t.charCodeAt(n-1)===44;)--n;return Or(e,t.substr(0,n),r/Math.pow(10,3*(t.length-n)))}function pa(e,t){var r,n=e.indexOf("E")-e.indexOf(".")-1;if(e.match(/^#+0.0E\+0$/)){if(t==0)return"0.0E+0";if(t<0)return"-"+pa(e,-t);var a=e.indexOf(".");a===-1&&(a=e.indexOf("E"));var i=Math.floor(Math.log(t)*Math.LOG10E)%a;if(i<0&&(i+=a),r=(t/Math.pow(10,i)).toPrecision(n+1+(a+i)%a),r.indexOf("e")===-1){var s=Math.floor(Math.log(t)*Math.LOG10E);for(r.indexOf(".")===-1?r=r.charAt(0)+"."+r.substr(1)+"E+"+(s-r.length+i):r+="E+"+(s-i);r.substr(0,2)==="0.";)r=r.charAt(0)+r.substr(2,a)+"."+r.substr(2+a),r=r.replace(/^0+([1-9])/,"$1").replace(/^0+\./,"0.");r=r.replace(/\+-/,"-")}r=r.replace(/^([+-]?)(\d*)\.(\d*)[Ee]/,function(f,o,l,c){return o+l+c.substr(0,(a+i)%a)+"."+c.substr(i)+"E"})}else r=t.toExponential(n);return e.match(/E\+00$/)&&r.match(/e[+-]\d$/)&&(r=r.substr(0,r.length-1)+"0"+r.charAt(r.length-1)),e.match(/E\-/)&&r.match(/e\+/)&&(r=r.replace(/e\+/,"e")),r.replace("e","E")}var ma=/# (\?+)( ?)\/( ?)(\d+)/;function zs(e,t,r){var n=parseInt(e[4],10),a=Math.round(t*n),i=Math.floor(a/n),s=a-i*n,f=n;return r+(i===0?"":""+i)+" "+(s===0?De(" ",e[1].length+1+e[4].length):Hn(s,e[1].length)+e[2]+"/"+e[3]+mr(f,e[4].length))}function Ks(e,t,r){return r+(t===0?"":""+t)+De(" ",e[1].length+2+e[4].length)}var ga=/^#*0*\.([0#]+)/,_a=/\).*[0#]/,Ta=/\(###\) ###\\?-####/;function Je(e){for(var t="",r,n=0;n!=e.length;++n)switch(r=e.charCodeAt(n)){case 35:break;case 63:t+=" ";break;case 48:t+="0";break;default:t+=String.fromCharCode(r)}return t}function O0(e,t){var r=Math.pow(10,t);return""+Math.round(e*r)/r}function D0(e,t){var r=e-Math.floor(e),n=Math.pow(10,t);return t<(""+Math.round(r*n)).length?0:Math.round(r*n)}function js(e,t){return t<(""+Math.round((e-Math.floor(e))*Math.pow(10,t))).length?1:0}function Ys(e){return e<2147483647&&e>-2147483648?""+(e>=0?e|0:e-1|0):""+Math.floor(e)}function cr(e,t,r){if(e.charCodeAt(0)===40&&!t.match(_a)){var n=t.replace(/\( */,"").replace(/ \)/,"").replace(/\)/,"");return r>=0?cr("n",n,r):"("+cr("n",n,-r)+")"}if(t.charCodeAt(t.length-1)===44)return $s(e,t,r);if(t.indexOf("%")!==-1)return Xs(e,t,r);if(t.indexOf("E")!==-1)return pa(t,r);if(t.charCodeAt(0)===36)return"$"+cr(e,t.substr(t.charAt(1)==" "?2:1),r);var a,i,s,f,o=Math.abs(r),l=r<0?"-":"";if(t.match(/^00+$/))return l+nt(o,t.length);if(t.match(/^[#?]+$/))return a=nt(r,0),a==="0"&&(a=""),a.length>t.length?a:Je(t.substr(0,t.length-a.length))+a;if(i=t.match(ma))return zs(i,o,l);if(t.match(/^#+0+$/))return l+nt(o,t.length-t.indexOf("0"));if(i=t.match(ga))return a=O0(r,i[1].length).replace(/^([^\.]+)$/,"$1."+Je(i[1])).replace(/\.$/,"."+Je(i[1])).replace(/\.(\d*)$/,function(T,u){return"."+u+De("0",Je(i[1]).length-u.length)}),t.indexOf("0.")!==-1?a:a.replace(/^0\./,".");if(t=t.replace(/^#+([0.])/,"$1"),i=t.match(/^(0*)\.(#*)$/))return l+O0(o,i[2].length).replace(/\.(\d*[1-9])0*$/,".$1").replace(/^(-?\d*)$/,"$1.").replace(/^0\./,i[1].length?"0.":".");if(i=t.match(/^#{1,3},##0(\.?)$/))return l+Br(nt(o,0));if(i=t.match(/^#,##0\.([#0]*0)$/))return r<0?"-"+cr(e,t,-r):Br(""+(Math.floor(r)+js(r,i[1].length)))+"."+mr(D0(r,i[1].length),i[1].length);if(i=t.match(/^#,#*,#0/))return cr(e,t.replace(/^#,#*,/,""),r);if(i=t.match(/^([0#]+)(\\?-([0#]+))+$/))return a=lt(cr(e,t.replace(/[\\-]/g,""),r)),s=0,lt(lt(t.replace(/\\/g,"")).replace(/[0#]/g,function(T){return s<a.length?a.charAt(s++):T==="0"?"0":""}));if(t.match(Ta))return a=cr(e,"##########",r),"("+a.substr(0,3)+") "+a.substr(3,3)+"-"+a.substr(6);var c="";if(i=t.match(/^([#0?]+)( ?)\/( ?)([#0?]+)/))return s=Math.min(i[4].length,7),f=tn(o,Math.pow(10,s)-1,!1),a=""+l,c=Or("n",i[1],f[1]),c.charAt(c.length-1)==" "&&(c=c.substr(0,c.length-1)+"0"),a+=c+i[2]+"/"+i[3],c=en(f[2],s),c.length<i[4].length&&(c=Je(i[4].substr(i[4].length-c.length))+c),a+=c,a;if(i=t.match(/^# ([#0?]+)( ?)\/( ?)([#0?]+)/))return s=Math.min(Math.max(i[1].length,i[4].length),7),f=tn(o,Math.pow(10,s)-1,!0),l+(f[0]||(f[1]?"":"0"))+" "+(f[1]?Hn(f[1],s)+i[2]+"/"+i[3]+en(f[2],s):De(" ",2*s+1+i[2].length+i[3].length));if(i=t.match(/^[#0?]+$/))return a=nt(r,0),t.length<=a.length?a:Je(t.substr(0,t.length-a.length))+a;if(i=t.match(/^([#0?]+)\.([#0]+)$/)){a=""+r.toFixed(Math.min(i[2].length,10)).replace(/([^0])0+$/,"$1"),s=a.indexOf(".");var v=t.indexOf(".")-s,x=t.length-a.length-v;return Je(t.substr(0,v)+a+t.substr(t.length-x))}if(i=t.match(/^00,000\.([#0]*0)$/))return s=D0(r,i[1].length),r<0?"-"+cr(e,t,-r):Br(Ys(r)).replace(/^\d,\d{3}$/,"0$&").replace(/^\d*$/,function(T){return"00,"+(T.length<3?mr(0,3-T.length):"")+T})+"."+mr(s,i[1].length);switch(t){case"###,##0.00":return cr(e,"#,##0.00",r);case"###,###":case"##,###":case"#,###":var d=Br(nt(o,0));return d!=="0"?l+d:"";case"###,###.00":return cr(e,"###,##0.00",r).replace(/^0\./,".");case"#,###.00":return cr(e,"#,##0.00",r).replace(/^0\./,".")}throw new Error("unsupported format |"+t+"|")}function Js(e,t,r){for(var n=t.length-1;t.charCodeAt(n-1)===44;)--n;return Or(e,t.substr(0,n),r/Math.pow(10,3*(t.length-n)))}function Zs(e,t,r){var n=t.replace(va,""),a=t.length-n.length;return Or(e,n,r*Math.pow(10,2*a))+De("%",a)}function Ea(e,t){var r,n=e.indexOf("E")-e.indexOf(".")-1;if(e.match(/^#+0.0E\+0$/)){if(t==0)return"0.0E+0";if(t<0)return"-"+Ea(e,-t);var a=e.indexOf(".");a===-1&&(a=e.indexOf("E"));var i=Math.floor(Math.log(t)*Math.LOG10E)%a;if(i<0&&(i+=a),r=(t/Math.pow(10,i)).toPrecision(n+1+(a+i)%a),!r.match(/[Ee]/)){var s=Math.floor(Math.log(t)*Math.LOG10E);r.indexOf(".")===-1?r=r.charAt(0)+"."+r.substr(1)+"E+"+(s-r.length+i):r+="E+"+(s-i),r=r.replace(/\+-/,"-")}r=r.replace(/^([+-]?)(\d*)\.(\d*)[Ee]/,function(f,o,l,c){return o+l+c.substr(0,(a+i)%a)+"."+c.substr(i)+"E"})}else r=t.toExponential(n);return e.match(/E\+00$/)&&r.match(/e[+-]\d$/)&&(r=r.substr(0,r.length-1)+"0"+r.charAt(r.length-1)),e.match(/E\-/)&&r.match(/e\+/)&&(r=r.replace(/e\+/,"e")),r.replace("e","E")}function Er(e,t,r){if(e.charCodeAt(0)===40&&!t.match(_a)){var n=t.replace(/\( */,"").replace(/ \)/,"").replace(/\)/,"");return r>=0?Er("n",n,r):"("+Er("n",n,-r)+")"}if(t.charCodeAt(t.length-1)===44)return Js(e,t,r);if(t.indexOf("%")!==-1)return Zs(e,t,r);if(t.indexOf("E")!==-1)return Ea(t,r);if(t.charCodeAt(0)===36)return"$"+Er(e,t.substr(t.charAt(1)==" "?2:1),r);var a,i,s,f,o=Math.abs(r),l=r<0?"-":"";if(t.match(/^00+$/))return l+mr(o,t.length);if(t.match(/^[#?]+$/))return a=""+r,r===0&&(a=""),a.length>t.length?a:Je(t.substr(0,t.length-a.length))+a;if(i=t.match(ma))return Ks(i,o,l);if(t.match(/^#+0+$/))return l+mr(o,t.length-t.indexOf("0"));if(i=t.match(ga))return a=(""+r).replace(/^([^\.]+)$/,"$1."+Je(i[1])).replace(/\.$/,"."+Je(i[1])),a=a.replace(/\.(\d*)$/,function(T,u){return"."+u+De("0",Je(i[1]).length-u.length)}),t.indexOf("0.")!==-1?a:a.replace(/^0\./,".");if(t=t.replace(/^#+([0.])/,"$1"),i=t.match(/^(0*)\.(#*)$/))return l+(""+o).replace(/\.(\d*[1-9])0*$/,".$1").replace(/^(-?\d*)$/,"$1.").replace(/^0\./,i[1].length?"0.":".");if(i=t.match(/^#{1,3},##0(\.?)$/))return l+Br(""+o);if(i=t.match(/^#,##0\.([#0]*0)$/))return r<0?"-"+Er(e,t,-r):Br(""+r)+"."+De("0",i[1].length);if(i=t.match(/^#,#*,#0/))return Er(e,t.replace(/^#,#*,/,""),r);if(i=t.match(/^([0#]+)(\\?-([0#]+))+$/))return a=lt(Er(e,t.replace(/[\\-]/g,""),r)),s=0,lt(lt(t.replace(/\\/g,"")).replace(/[0#]/g,function(T){return s<a.length?a.charAt(s++):T==="0"?"0":""}));if(t.match(Ta))return a=Er(e,"##########",r),"("+a.substr(0,3)+") "+a.substr(3,3)+"-"+a.substr(6);var c="";if(i=t.match(/^([#0?]+)( ?)\/( ?)([#0?]+)/))return s=Math.min(i[4].length,7),f=tn(o,Math.pow(10,s)-1,!1),a=""+l,c=Or("n",i[1],f[1]),c.charAt(c.length-1)==" "&&(c=c.substr(0,c.length-1)+"0"),a+=c+i[2]+"/"+i[3],c=en(f[2],s),c.length<i[4].length&&(c=Je(i[4].substr(i[4].length-c.length))+c),a+=c,a;if(i=t.match(/^# ([#0?]+)( ?)\/( ?)([#0?]+)/))return s=Math.min(Math.max(i[1].length,i[4].length),7),f=tn(o,Math.pow(10,s)-1,!0),l+(f[0]||(f[1]?"":"0"))+" "+(f[1]?Hn(f[1],s)+i[2]+"/"+i[3]+en(f[2],s):De(" ",2*s+1+i[2].length+i[3].length));if(i=t.match(/^[#0?]+$/))return a=""+r,t.length<=a.length?a:Je(t.substr(0,t.length-a.length))+a;if(i=t.match(/^([#0]+)\.([#0]+)$/)){a=""+r.toFixed(Math.min(i[2].length,10)).replace(/([^0])0+$/,"$1"),s=a.indexOf(".");var v=t.indexOf(".")-s,x=t.length-a.length-v;return Je(t.substr(0,v)+a+t.substr(t.length-x))}if(i=t.match(/^00,000\.([#0]*0)$/))return r<0?"-"+Er(e,t,-r):Br(""+r).replace(/^\d,\d{3}$/,"0$&").replace(/^\d*$/,function(T){return"00,"+(T.length<3?mr(0,3-T.length):"")+T})+"."+mr(0,i[1].length);switch(t){case"###,###":case"##,###":case"#,###":var d=Br(""+o);return d!=="0"?l+d:"";default:if(t.match(/\.[0#?]*$/))return Er(e,t.slice(0,t.lastIndexOf(".")),r)+Je(t.slice(t.lastIndexOf(".")))}throw new Error("unsupported format |"+t+"|")}function Or(e,t,r){return(r|0)===r?Er(e,t,r):cr(e,t,r)}function qs(e){for(var t=[],r=!1,n=0,a=0;n<e.length;++n)switch(e.charCodeAt(n)){case 34:r=!r;break;case 95:case 42:case 92:++n;break;case 59:t[t.length]=e.substr(a,n-a),a=n+1}if(t[t.length]=e.substr(a),r===!0)throw new Error("Format |"+e+"| unterminated string ");return t}var wa=/\[[HhMmSs\u0E0A\u0E19\u0E17]*\]/;function Sa(e){for(var t=0,r="",n="";t<e.length;)switch(r=e.charAt(t)){case"G":rn(e,t)&&(t+=6),t++;break;case'"':for(;e.charCodeAt(++t)!==34&&t<e.length;);++t;break;case"\\":t+=2;break;case"_":t+=2;break;case"@":++t;break;case"B":case"b":if(e.charAt(t+1)==="1"||e.charAt(t+1)==="2")return!0;case"M":case"D":case"Y":case"H":case"S":case"E":case"m":case"d":case"y":case"h":case"s":case"e":case"g":return!0;case"A":case"a":case"\u4E0A":if(e.substr(t,3).toUpperCase()==="A/P"||e.substr(t,5).toUpperCase()==="AM/PM"||e.substr(t,5).toUpperCase()==="\u4E0A\u5348/\u4E0B\u5348")return!0;++t;break;case"[":for(n=r;e.charAt(t++)!=="]"&&t<e.length;)n+=e.charAt(t);if(n.match(wa))return!0;break;case".":case"0":case"#":for(;t<e.length&&("0#?.,E+-%".indexOf(r=e.charAt(++t))>-1||r=="\\"&&e.charAt(t+1)=="-"&&"0#".indexOf(e.charAt(t+2))>-1););break;case"?":for(;e.charAt(++t)===r;);break;case"*":++t,(e.charAt(t)==" "||e.charAt(t)=="*")&&++t;break;case"(":case")":++t;break;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":for(;t<e.length&&"0123456789".indexOf(e.charAt(++t))>-1;);break;case" ":++t;break;default:++t;break}return!1}function Qs(e,t,r,n){for(var a=[],i="",s=0,f="",o="t",l,c,v,x="H";s<e.length;)switch(f=e.charAt(s)){case"G":if(!rn(e,s))throw new Error("unrecognized character "+f+" in "+e);a[a.length]={t:"G",v:"General"},s+=7;break;case'"':for(i="";(v=e.charCodeAt(++s))!==34&&s<e.length;)i+=String.fromCharCode(v);a[a.length]={t:"t",v:i},++s;break;case"\\":var d=e.charAt(++s),T=d==="("||d===")"?d:"t";a[a.length]={t:T,v:d},++s;break;case"_":a[a.length]={t:"t",v:" "},s+=2;break;case"@":a[a.length]={t:"T",v:t},++s;break;case"B":case"b":if(e.charAt(s+1)==="1"||e.charAt(s+1)==="2"){if(l==null&&(l=Kt(t,r,e.charAt(s+1)==="2"),l==null))return"";a[a.length]={t:"X",v:e.substr(s,2)},o=f,s+=2;break}case"M":case"D":case"Y":case"H":case"S":case"E":f=f.toLowerCase();case"m":case"d":case"y":case"h":case"s":case"e":case"g":if(t<0||l==null&&(l=Kt(t,r),l==null))return"";for(i=f;++s<e.length&&e.charAt(s).toLowerCase()===f;)i+=f;f==="m"&&o.toLowerCase()==="h"&&(f="M"),f==="h"&&(f=x),a[a.length]={t:f,v:i},o=f;break;case"A":case"a":case"\u4E0A":var u={t:f,v:f};if(l==null&&(l=Kt(t,r)),e.substr(s,3).toUpperCase()==="A/P"?(l!=null&&(u.v=l.H>=12?"P":"A"),u.t="T",x="h",s+=3):e.substr(s,5).toUpperCase()==="AM/PM"?(l!=null&&(u.v=l.H>=12?"PM":"AM"),u.t="T",s+=5,x="h"):e.substr(s,5).toUpperCase()==="\u4E0A\u5348/\u4E0B\u5348"?(l!=null&&(u.v=l.H>=12?"\u4E0B\u5348":"\u4E0A\u5348"),u.t="T",s+=5,x="h"):(u.t="t",++s),l==null&&u.t==="T")return"";a[a.length]=u,o=f;break;case"[":for(i=f;e.charAt(s++)!=="]"&&s<e.length;)i+=e.charAt(s);if(i.slice(-1)!=="]")throw'unterminated "[" block: |'+i+"|";if(i.match(wa)){if(l==null&&(l=Kt(t,r),l==null))return"";a[a.length]={t:"Z",v:i.toLowerCase()},o=i.charAt(1)}else i.indexOf("$")>-1&&(i=(i.match(/\$([^-\[\]]*)/)||[])[1]||"$",Sa(e)||(a[a.length]={t:"t",v:i}));break;case".":if(l!=null){for(i=f;++s<e.length&&(f=e.charAt(s))==="0";)i+=f;a[a.length]={t:"s",v:i};break}case"0":case"#":for(i=f;++s<e.length&&"0#?.,E+-%".indexOf(f=e.charAt(s))>-1;)i+=f;a[a.length]={t:"n",v:i};break;case"?":for(i=f;e.charAt(++s)===f;)i+=f;a[a.length]={t:f,v:i},o=f;break;case"*":++s,(e.charAt(s)==" "||e.charAt(s)=="*")&&++s;break;case"(":case")":a[a.length]={t:n===1?"t":f,v:f},++s;break;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":for(i=f;s<e.length&&"0123456789".indexOf(e.charAt(++s))>-1;)i+=e.charAt(s);a[a.length]={t:"D",v:i};break;case" ":a[a.length]={t:f,v:f},++s;break;case"$":a[a.length]={t:"t",v:"$"},++s;break;default:if(",$-+/():!^&'~{}<>=\u20ACacfijklopqrtuvwxzP".indexOf(f)===-1)throw new Error("unrecognized character "+f+" in "+e);a[a.length]={t:"t",v:f},++s;break}var g=0,R=0,C;for(s=a.length-1,o="t";s>=0;--s)switch(a[s].t){case"h":case"H":a[s].t=x,o="h",g<1&&(g=1);break;case"s":(C=a[s].v.match(/\.0+$/))&&(R=Math.max(R,C[0].length-1)),g<3&&(g=3);case"d":case"y":case"M":case"e":o=a[s].t;break;case"m":o==="s"&&(a[s].t="M",g<2&&(g=2));break;case"X":break;case"Z":g<1&&a[s].v.match(/[Hh]/)&&(g=1),g<2&&a[s].v.match(/[Mm]/)&&(g=2),g<3&&a[s].v.match(/[Ss]/)&&(g=3)}switch(g){case 0:break;case 1:l.u>=.5&&(l.u=0,++l.S),l.S>=60&&(l.S=0,++l.M),l.M>=60&&(l.M=0,++l.H);break;case 2:l.u>=.5&&(l.u=0,++l.S),l.S>=60&&(l.S=0,++l.M);break}var y="",U;for(s=0;s<a.length;++s)switch(a[s].t){case"t":case"T":case" ":case"D":break;case"X":a[s].v="",a[s].t=";";break;case"d":case"m":case"y":case"h":case"H":case"M":case"s":case"e":case"b":case"Z":a[s].v=Gs(a[s].t.charCodeAt(0),a[s].v,l,R),a[s].t="t";break;case"n":case"?":for(U=s+1;a[U]!=null&&((f=a[U].t)==="?"||f==="D"||(f===" "||f==="t")&&a[U+1]!=null&&(a[U+1].t==="?"||a[U+1].t==="t"&&a[U+1].v==="/")||a[s].t==="("&&(f===" "||f==="n"||f===")")||f==="t"&&(a[U].v==="/"||a[U].v===" "&&a[U+1]!=null&&a[U+1].t=="?"));)a[s].v+=a[U].v,a[U]={v:"",t:";"},++U;y+=a[s].v,s=U-1;break;case"G":a[s].t="t",a[s].v=Mn(t,r);break}var Y="",Q,O;if(y.length>0){y.charCodeAt(0)==40?(Q=t<0&&y.charCodeAt(0)===45?-t:t,O=Or("n",y,Q)):(Q=t<0&&n>1?-t:t,O=Or("n",y,Q),Q<0&&a[0]&&a[0].t=="t"&&(O=O.substr(1),a[0].v="-"+a[0].v)),U=O.length-1;var V=a.length;for(s=0;s<a.length;++s)if(a[s]!=null&&a[s].t!="t"&&a[s].v.indexOf(".")>-1){V=s;break}var B=a.length;if(V===a.length&&O.indexOf("E")===-1){for(s=a.length-1;s>=0;--s)a[s]==null||"n?".indexOf(a[s].t)===-1||(U>=a[s].v.length-1?(U-=a[s].v.length,a[s].v=O.substr(U+1,a[s].v.length)):U<0?a[s].v="":(a[s].v=O.substr(0,U+1),U=-1),a[s].t="t",B=s);U>=0&&B<a.length&&(a[B].v=O.substr(0,U+1)+a[B].v)}else if(V!==a.length&&O.indexOf("E")===-1){for(U=O.indexOf(".")-1,s=V;s>=0;--s)if(!(a[s]==null||"n?".indexOf(a[s].t)===-1)){for(c=a[s].v.indexOf(".")>-1&&s===V?a[s].v.indexOf(".")-1:a[s].v.length-1,Y=a[s].v.substr(c+1);c>=0;--c)U>=0&&(a[s].v.charAt(c)==="0"||a[s].v.charAt(c)==="#")&&(Y=O.charAt(U--)+Y);a[s].v=Y,a[s].t="t",B=s}for(U>=0&&B<a.length&&(a[B].v=O.substr(0,U+1)+a[B].v),U=O.indexOf(".")+1,s=V;s<a.length;++s)if(!(a[s]==null||"n?(".indexOf(a[s].t)===-1&&s!==V)){for(c=a[s].v.indexOf(".")>-1&&s===V?a[s].v.indexOf(".")+1:0,Y=a[s].v.substr(0,c);c<a[s].v.length;++c)U<O.length&&(Y+=O.charAt(U++));a[s].v=Y,a[s].t="t",B=s}}}for(s=0;s<a.length;++s)a[s]!=null&&"n?".indexOf(a[s].t)>-1&&(Q=n>1&&t<0&&s>0&&a[s-1].v==="-"?-t:t,a[s].v=Or(a[s].t,a[s].v,Q),a[s].t="t");var I="";for(s=0;s!==a.length;++s)a[s]!=null&&(I+=a[s].v);return I}var R0=/\[(=|>[=]?|<[>=]?)(-?\d+(?:\.\d*)?)\]/;function N0(e,t){if(t==null)return!1;var r=parseFloat(t[2]);switch(t[1]){case"=":if(e==r)return!0;break;case">":if(e>r)return!0;break;case"<":if(e<r)return!0;break;case"<>":if(e!=r)return!0;break;case">=":if(e>=r)return!0;break;case"<=":if(e<=r)return!0;break}return!1}function ef(e,t){var r=qs(e),n=r.length,a=r[n-1].indexOf("@");if(n<4&&a>-1&&--n,r.length>4)throw new Error("cannot find right format for |"+r.join("|")+"|");if(typeof t!="number")return[4,r.length===4||a>-1?r[r.length-1]:"@"];switch(r.length){case 1:r=a>-1?["General","General","General",r[0]]:[r[0],r[0],r[0],"@"];break;case 2:r=a>-1?[r[0],r[0],r[0],r[1]]:[r[0],r[1],r[0],"@"];break;case 3:r=a>-1?[r[0],r[1],r[0],r[2]]:[r[0],r[1],r[2],"@"];break}var i=t>0?r[0]:t<0?r[1]:r[2];if(r[0].indexOf("[")===-1&&r[1].indexOf("[")===-1)return[n,i];if(r[0].match(/\[[=<>]/)!=null||r[1].match(/\[[=<>]/)!=null){var s=r[0].match(R0),f=r[1].match(R0);return N0(t,s)?[n,r[0]]:N0(t,f)?[n,r[1]]:[n,r[s!=null&&f!=null?2:1]]}return[n,i]}function Mr(e,t,r){r==null&&(r={});var n="";switch(typeof e){case"string":e=="m/d/yy"&&r.dateNF?n=r.dateNF:n=e;break;case"number":e==14&&r.dateNF?n=r.dateNF:n=(r.table!=null?r.table:Re)[e],n==null&&(n=r.table&&r.table[C0[e]]||Re[C0[e]]),n==null&&(n=Ls[e]||"General");break}if(rn(n,0))return Mn(t,r);t instanceof Date&&(t=da(t,r.date1904));var a=ef(n,t);if(rn(a[1]))return Mn(t,r);if(t===!0)t="TRUE";else if(t===!1)t="FALSE";else if(t===""||t==null)return"";return Qs(a[1],t,r,a[0])}function Aa(e,t){if(typeof t!="number"){t=+t||-1;for(var r=0;r<392;++r){if(Re[r]==null){t<0&&(t=r);continue}if(Re[r]==e){t=r;break}}t<0&&(t=391)}return Re[t]=e,t}function vn(e){for(var t=0;t!=392;++t)e[t]!==void 0&&Aa(e[t],t)}function pn(){Re=Ps()}var Fa=/[dD]+|[mM]+|[yYeE]+|[Hh]+|[Ss]+/g;function rf(e){var t=typeof e=="number"?Re[e]:e;return t=t.replace(Fa,"(\\d+)"),new RegExp("^"+t+"$")}function tf(e,t,r){var n=-1,a=-1,i=-1,s=-1,f=-1,o=-1;(t.match(Fa)||[]).forEach(function(v,x){var d=parseInt(r[x+1],10);switch(v.toLowerCase().charAt(0)){case"y":n=d;break;case"d":i=d;break;case"h":s=d;break;case"s":o=d;break;case"m":s>=0?f=d:a=d;break}}),o>=0&&f==-1&&a>=0&&(f=a,a=-1);var l=(""+(n>=0?n:new Date().getFullYear())).slice(-4)+"-"+("00"+(a>=1?a:1)).slice(-2)+"-"+("00"+(i>=1?i:1)).slice(-2);l.length==7&&(l="0"+l),l.length==8&&(l="20"+l);var c=("00"+(s>=0?s:0)).slice(-2)+":"+("00"+(f>=0?f:0)).slice(-2)+":"+("00"+(o>=0?o:0)).slice(-2);return s==-1&&f==-1&&o==-1?l:n==-1&&a==-1&&i==-1?c:l+"T"+c}var nf=function(){var e={};e.version="1.2.0";function t(){for(var O=0,V=new Array(256),B=0;B!=256;++B)O=B,O=O&1?-306674912^O>>>1:O>>>1,O=O&1?-306674912^O>>>1:O>>>1,O=O&1?-306674912^O>>>1:O>>>1,O=O&1?-306674912^O>>>1:O>>>1,O=O&1?-306674912^O>>>1:O>>>1,O=O&1?-306674912^O>>>1:O>>>1,O=O&1?-306674912^O>>>1:O>>>1,O=O&1?-306674912^O>>>1:O>>>1,V[B]=O;return typeof Int32Array<"u"?new Int32Array(V):V}var r=t();function n(O){var V=0,B=0,I=0,M=typeof Int32Array<"u"?new Int32Array(4096):new Array(4096);for(I=0;I!=256;++I)M[I]=O[I];for(I=0;I!=256;++I)for(B=O[I],V=256+I;V<4096;V+=256)B=M[V]=B>>>8^O[B&255];var H=[];for(I=1;I!=16;++I)H[I-1]=typeof Int32Array<"u"?M.subarray(I*256,I*256+256):M.slice(I*256,I*256+256);return H}var a=n(r),i=a[0],s=a[1],f=a[2],o=a[3],l=a[4],c=a[5],v=a[6],x=a[7],d=a[8],T=a[9],u=a[10],g=a[11],R=a[12],C=a[13],y=a[14];function U(O,V){for(var B=V^-1,I=0,M=O.length;I<M;)B=B>>>8^r[(B^O.charCodeAt(I++))&255];return~B}function Y(O,V){for(var B=V^-1,I=O.length-15,M=0;M<I;)B=y[O[M++]^B&255]^C[O[M++]^B>>8&255]^R[O[M++]^B>>16&255]^g[O[M++]^B>>>24]^u[O[M++]]^T[O[M++]]^d[O[M++]]^x[O[M++]]^v[O[M++]]^c[O[M++]]^l[O[M++]]^o[O[M++]]^f[O[M++]]^s[O[M++]]^i[O[M++]]^r[O[M++]];for(I+=15;M<I;)B=B>>>8^r[(B^O[M++])&255];return~B}function Q(O,V){for(var B=V^-1,I=0,M=O.length,H=0,K=0;I<M;)H=O.charCodeAt(I++),H<128?B=B>>>8^r[(B^H)&255]:H<2048?(B=B>>>8^r[(B^(192|H>>6&31))&255],B=B>>>8^r[(B^(128|H&63))&255]):H>=55296&&H<57344?(H=(H&1023)+64,K=O.charCodeAt(I++)&1023,B=B>>>8^r[(B^(240|H>>8&7))&255],B=B>>>8^r[(B^(128|H>>2&63))&255],B=B>>>8^r[(B^(128|K>>6&15|(H&3)<<4))&255],B=B>>>8^r[(B^(128|K&63))&255]):(B=B>>>8^r[(B^(224|H>>12&15))&255],B=B>>>8^r[(B^(128|H>>6&63))&255],B=B>>>8^r[(B^(128|H&63))&255]);return~B}return e.table=r,e.bstr=U,e.buf=Y,e.str=Q,e}(),we=function(){var t={};t.version="1.2.1";function r(h,_){for(var p=h.split("/"),m=_.split("/"),E=0,w=0,k=Math.min(p.length,m.length);E<k;++E){if(w=p[E].length-m[E].length)return w;if(p[E]!=m[E])return p[E]<m[E]?-1:1}return p.length-m.length}function n(h){if(h.charAt(h.length-1)=="/")return h.slice(0,-1).indexOf("/")===-1?h:n(h.slice(0,-1));var _=h.lastIndexOf("/");return _===-1?h:h.slice(0,_+1)}function a(h){if(h.charAt(h.length-1)=="/")return a(h.slice(0,-1));var _=h.lastIndexOf("/");return _===-1?h:h.slice(_+1)}function i(h,_){typeof _=="string"&&(_=new Date(_));var p=_.getHours();p=p<<6|_.getMinutes(),p=p<<5|_.getSeconds()>>>1,h.write_shift(2,p);var m=_.getFullYear()-1980;m=m<<4|_.getMonth()+1,m=m<<5|_.getDate(),h.write_shift(2,m)}function s(h){var _=h.read_shift(2)&65535,p=h.read_shift(2)&65535,m=new Date,E=p&31;p>>>=5;var w=p&15;p>>>=4,m.setMilliseconds(0),m.setFullYear(p+1980),m.setMonth(w-1),m.setDate(E);var k=_&31;_>>>=5;var W=_&63;return _>>>=6,m.setHours(_),m.setMinutes(W),m.setSeconds(k<<1),m}function f(h){ar(h,0);for(var _={},p=0;h.l<=h.length-4;){var m=h.read_shift(2),E=h.read_shift(2),w=h.l+E,k={};switch(m){case 21589:p=h.read_shift(1),p&1&&(k.mtime=h.read_shift(4)),E>5&&(p&2&&(k.atime=h.read_shift(4)),p&4&&(k.ctime=h.read_shift(4))),k.mtime&&(k.mt=new Date(k.mtime*1e3));break}h.l=w,_[m]=k}return _}var o;function l(){return o||(o={})}function c(h,_){if(h[0]==80&&h[1]==75)return m0(h,_);if((h[0]|32)==109&&(h[1]|32)==105)return rs(h,_);if(h.length<512)throw new Error("CFB file size "+h.length+" < 512");var p=3,m=512,E=0,w=0,k=0,W=0,N=0,P=[],L=h.slice(0,512);ar(L,0);var z=v(L);switch(p=z[0],p){case 3:m=512;break;case 4:m=4096;break;case 0:if(z[1]==0)return m0(h,_);default:throw new Error("Major Version: Expected 3 or 4 saw "+p)}m!==512&&(L=h.slice(0,m),ar(L,28));var q=h.slice(0,m);x(L,p);var te=L.read_shift(4,"i");if(p===3&&te!==0)throw new Error("# Directory Sectors: Expected 0 saw "+te);L.l+=4,k=L.read_shift(4,"i"),L.l+=4,L.chk("00100000","Mini Stream Cutoff Size: "),W=L.read_shift(4,"i"),E=L.read_shift(4,"i"),N=L.read_shift(4,"i"),w=L.read_shift(4,"i");for(var j=-1,re=0;re<109&&(j=L.read_shift(4,"i"),!(j<0));++re)P[re]=j;var ce=d(h,m);g(N,w,ce,m,P);var ye=C(ce,k,P,m);ye[k].name="!Directory",E>0&&W!==K&&(ye[W].name="!MiniFAT"),ye[P[0]].name="!FAT",ye.fat_addrs=P,ye.ssz=m;var Ce={},$e=[],mt=[],gt=[];y(k,ye,ce,$e,E,Ce,mt,W),T(mt,gt,$e),$e.shift();var _t={FileIndex:mt,FullPaths:gt};return _&&_.raw&&(_t.raw={header:q,sectors:ce}),_t}function v(h){if(h[h.l]==80&&h[h.l+1]==75)return[0,0];h.chk(ae,"Header Signature: "),h.l+=16;var _=h.read_shift(2,"u");return[h.read_shift(2,"u"),_]}function x(h,_){var p=9;switch(h.l+=2,p=h.read_shift(2)){case 9:if(_!=3)throw new Error("Sector Shift: Expected 9 saw "+p);break;case 12:if(_!=4)throw new Error("Sector Shift: Expected 12 saw "+p);break;default:throw new Error("Sector Shift: Expected 9 or 12 saw "+p)}h.chk("0600","Mini Sector Shift: "),h.chk("000000000000","Reserved: ")}function d(h,_){for(var p=Math.ceil(h.length/_)-1,m=[],E=1;E<p;++E)m[E-1]=h.slice(E*_,(E+1)*_);return m[p-1]=h.slice(p*_),m}function T(h,_,p){for(var m=0,E=0,w=0,k=0,W=0,N=p.length,P=[],L=[];m<N;++m)P[m]=L[m]=m,_[m]=p[m];for(;W<L.length;++W)m=L[W],E=h[m].L,w=h[m].R,k=h[m].C,P[m]===m&&(E!==-1&&P[E]!==E&&(P[m]=P[E]),w!==-1&&P[w]!==w&&(P[m]=P[w])),k!==-1&&(P[k]=m),E!==-1&&m!=P[m]&&(P[E]=P[m],L.lastIndexOf(E)<W&&L.push(E)),w!==-1&&m!=P[m]&&(P[w]=P[m],L.lastIndexOf(w)<W&&L.push(w));for(m=1;m<N;++m)P[m]===m&&(w!==-1&&P[w]!==w?P[m]=P[w]:E!==-1&&P[E]!==E&&(P[m]=P[E]));for(m=1;m<N;++m)if(h[m].type!==0){if(W=m,W!=P[W])do W=P[W],_[m]=_[W]+"/"+_[m];while(W!==0&&P[W]!==-1&&W!=P[W]);P[m]=-1}for(_[0]+="/",m=1;m<N;++m)h[m].type!==2&&(_[m]+="/")}function u(h,_,p){for(var m=h.start,E=h.size,w=[],k=m;p&&E>0&&k>=0;)w.push(_.slice(k*H,k*H+H)),E-=H,k=$r(p,k*4);return w.length===0?b(0):He(w).slice(0,h.size)}function g(h,_,p,m,E){var w=K;if(h===K){if(_!==0)throw new Error("DIFAT chain shorter than expected")}else if(h!==-1){var k=p[h],W=(m>>>2)-1;if(!k)return;for(var N=0;N<W&&(w=$r(k,N*4))!==K;++N)E.push(w);g($r(k,m-4),_-1,p,m,E)}}function R(h,_,p,m,E){var w=[],k=[];E||(E=[]);var W=m-1,N=0,P=0;for(N=_;N>=0;){E[N]=!0,w[w.length]=N,k.push(h[N]);var L=p[Math.floor(N*4/m)];if(P=N*4&W,m<4+P)throw new Error("FAT boundary crossed: "+N+" 4 "+m);if(!h[L])break;N=$r(h[L],P)}return{nodes:w,data:b0([k])}}function C(h,_,p,m){var E=h.length,w=[],k=[],W=[],N=[],P=m-1,L=0,z=0,q=0,te=0;for(L=0;L<E;++L)if(W=[],q=L+_,q>=E&&(q-=E),!k[q]){N=[];var j=[];for(z=q;z>=0;){j[z]=!0,k[z]=!0,W[W.length]=z,N.push(h[z]);var re=p[Math.floor(z*4/m)];if(te=z*4&P,m<4+te)throw new Error("FAT boundary crossed: "+z+" 4 "+m);if(!h[re]||(z=$r(h[re],te),j[z]))break}w[q]={nodes:W,data:b0([N])}}return w}function y(h,_,p,m,E,w,k,W){for(var N=0,P=m.length?2:0,L=_[h].data,z=0,q=0,te;z<L.length;z+=128){var j=L.slice(z,z+128);ar(j,64),q=j.read_shift(2),te=Kn(j,0,q-P),m.push(te);var re={name:te,type:j.read_shift(1),color:j.read_shift(1),L:j.read_shift(4,"i"),R:j.read_shift(4,"i"),C:j.read_shift(4,"i"),clsid:j.read_shift(16),state:j.read_shift(4,"i"),start:0,size:0},ce=j.read_shift(2)+j.read_shift(2)+j.read_shift(2)+j.read_shift(2);ce!==0&&(re.ct=U(j,j.l-8));var ye=j.read_shift(2)+j.read_shift(2)+j.read_shift(2)+j.read_shift(2);ye!==0&&(re.mt=U(j,j.l-8)),re.start=j.read_shift(4,"i"),re.size=j.read_shift(4,"i"),re.size<0&&re.start<0&&(re.size=re.type=0,re.start=K,re.name=""),re.type===5?(N=re.start,E>0&&N!==K&&(_[N].name="!StreamData")):re.size>=4096?(re.storage="fat",_[re.start]===void 0&&(_[re.start]=R(p,re.start,_.fat_addrs,_.ssz)),_[re.start].name=re.name,re.content=_[re.start].data.slice(0,re.size)):(re.storage="minifat",re.size<0?re.size=0:N!==K&&re.start!==K&&_[N]&&(re.content=u(re,_[N].data,(_[W]||{}).data))),re.content&&ar(re.content,0),w[te]=re,k.push(re)}}function U(h,_){return new Date((sr(h,_+4)/1e7*Math.pow(2,32)+sr(h,_)/1e7-11644473600)*1e3)}function Y(h,_){return l(),c(o.readFileSync(h),_)}function Q(h,_){var p=_&&_.type;switch(p||ve&&Buffer.isBuffer(h)&&(p="buffer"),p||"base64"){case"file":return Y(h,_);case"base64":return c(pr(Nr(h)),_);case"binary":return c(pr(h),_)}return c(h,_)}function O(h,_){var p=_||{},m=p.root||"Root Entry";if(h.FullPaths||(h.FullPaths=[]),h.FileIndex||(h.FileIndex=[]),h.FullPaths.length!==h.FileIndex.length)throw new Error("inconsistent CFB structure");h.FullPaths.length===0&&(h.FullPaths[0]=m+"/",h.FileIndex[0]={name:m,type:5}),p.CLSID&&(h.FileIndex[0].clsid=p.CLSID),V(h)}function V(h){var _="Sh33tJ5";if(!we.find(h,"/"+_)){var p=b(4);p[0]=55,p[1]=p[3]=50,p[2]=54,h.FileIndex.push({name:_,type:2,content:p,size:4,L:69,R:69,C:69}),h.FullPaths.push(h.FullPaths[0]+_),B(h)}}function B(h,_){O(h);for(var p=!1,m=!1,E=h.FullPaths.length-1;E>=0;--E){var w=h.FileIndex[E];switch(w.type){case 0:m?p=!0:(h.FileIndex.pop(),h.FullPaths.pop());break;case 1:case 2:case 5:m=!0,isNaN(w.R*w.L*w.C)&&(p=!0),w.R>-1&&w.L>-1&&w.R==w.L&&(p=!0);break;default:p=!0;break}}if(!(!p&&!_)){var k=new Date(1987,1,19),W=0,N=Object.create?Object.create(null):{},P=[];for(E=0;E<h.FullPaths.length;++E)N[h.FullPaths[E]]=!0,h.FileIndex[E].type!==0&&P.push([h.FullPaths[E],h.FileIndex[E]]);for(E=0;E<P.length;++E){var L=n(P[E][0]);m=N[L],m||(P.push([L,{name:a(L).replace("/",""),type:1,clsid:he,ct:k,mt:k,content:null}]),N[L]=!0)}for(P.sort(function(te,j){return r(te[0],j[0])}),h.FullPaths=[],h.FileIndex=[],E=0;E<P.length;++E)h.FullPaths[E]=P[E][0],h.FileIndex[E]=P[E][1];for(E=0;E<P.length;++E){var z=h.FileIndex[E],q=h.FullPaths[E];if(z.name=a(q).replace("/",""),z.L=z.R=z.C=-(z.color=1),z.size=z.content?z.content.length:0,z.start=0,z.clsid=z.clsid||he,E===0)z.C=P.length>1?1:-1,z.size=0,z.type=5;else if(q.slice(-1)=="/"){for(W=E+1;W<P.length&&n(h.FullPaths[W])!=q;++W);for(z.C=W>=P.length?-1:W,W=E+1;W<P.length&&n(h.FullPaths[W])!=n(q);++W);z.R=W>=P.length?-1:W,z.type=1}else n(h.FullPaths[E+1]||"")==n(q)&&(z.R=E+1),z.type=2}}}function I(h,_){var p=_||{};if(p.fileType=="mad")return ts(h,p);switch(B(h),p.fileType){case"zip":return Yi(h,p)}var m=function(te){for(var j=0,re=0,ce=0;ce<te.FileIndex.length;++ce){var ye=te.FileIndex[ce];if(!!ye.content){var Ce=ye.content.length;Ce>0&&(Ce<4096?j+=Ce+63>>6:re+=Ce+511>>9)}}for(var $e=te.FullPaths.length+3>>2,mt=j+7>>3,gt=j+127>>7,_t=mt+re+$e+gt,Gr=_t+127>>7,yn=Gr<=109?0:Math.ceil((Gr-109)/127);_t+Gr+yn+127>>7>Gr;)yn=++Gr<=109?0:Math.ceil((Gr-109)/127);var yr=[1,yn,Gr,gt,$e,re,j,0];return te.FileIndex[0].size=j<<6,yr[7]=(te.FileIndex[0].start=yr[0]+yr[1]+yr[2]+yr[3]+yr[4]+yr[5])+(yr[6]+7>>3),yr}(h),E=b(m[7]<<9),w=0,k=0;{for(w=0;w<8;++w)E.write_shift(1,ne[w]);for(w=0;w<8;++w)E.write_shift(2,0);for(E.write_shift(2,62),E.write_shift(2,3),E.write_shift(2,65534),E.write_shift(2,9),E.write_shift(2,6),w=0;w<3;++w)E.write_shift(2,0);for(E.write_shift(4,0),E.write_shift(4,m[2]),E.write_shift(4,m[0]+m[1]+m[2]+m[3]-1),E.write_shift(4,0),E.write_shift(4,1<<12),E.write_shift(4,m[3]?m[0]+m[1]+m[2]-1:K),E.write_shift(4,m[3]),E.write_shift(-4,m[1]?m[0]-1:K),E.write_shift(4,m[1]),w=0;w<109;++w)E.write_shift(-4,w<m[2]?m[1]+w:-1)}if(m[1])for(k=0;k<m[1];++k){for(;w<236+k*127;++w)E.write_shift(-4,w<m[2]?m[1]+w:-1);E.write_shift(-4,k===m[1]-1?K:k+1)}var W=function(te){for(k+=te;w<k-1;++w)E.write_shift(-4,w+1);te&&(++w,E.write_shift(-4,K))};for(k=w=0,k+=m[1];w<k;++w)E.write_shift(-4,fe.DIFSECT);for(k+=m[2];w<k;++w)E.write_shift(-4,fe.FATSECT);W(m[3]),W(m[4]);for(var N=0,P=0,L=h.FileIndex[0];N<h.FileIndex.length;++N)L=h.FileIndex[N],L.content&&(P=L.content.length,!(P<4096)&&(L.start=k,W(P+511>>9)));for(W(m[6]+7>>3);E.l&511;)E.write_shift(-4,fe.ENDOFCHAIN);for(k=w=0,N=0;N<h.FileIndex.length;++N)L=h.FileIndex[N],L.content&&(P=L.content.length,!(!P||P>=4096)&&(L.start=k,W(P+63>>6)));for(;E.l&511;)E.write_shift(-4,fe.ENDOFCHAIN);for(w=0;w<m[4]<<2;++w){var z=h.FullPaths[w];if(!z||z.length===0){for(N=0;N<17;++N)E.write_shift(4,0);for(N=0;N<3;++N)E.write_shift(4,-1);for(N=0;N<12;++N)E.write_shift(4,0);continue}L=h.FileIndex[w],w===0&&(L.start=L.size?L.start-1:K);var q=w===0&&p.root||L.name;if(P=2*(q.length+1),E.write_shift(64,q,"utf16le"),E.write_shift(2,P),E.write_shift(1,L.type),E.write_shift(1,L.color),E.write_shift(-4,L.L),E.write_shift(-4,L.R),E.write_shift(-4,L.C),L.clsid)E.write_shift(16,L.clsid,"hex");else for(N=0;N<4;++N)E.write_shift(4,0);E.write_shift(4,L.state||0),E.write_shift(4,0),E.write_shift(4,0),E.write_shift(4,0),E.write_shift(4,0),E.write_shift(4,L.start),E.write_shift(4,L.size),E.write_shift(4,0)}for(w=1;w<h.FileIndex.length;++w)if(L=h.FileIndex[w],L.size>=4096)if(E.l=L.start+1<<9,ve&&Buffer.isBuffer(L.content))L.content.copy(E,E.l,0,L.size),E.l+=L.size+511&-512;else{for(N=0;N<L.size;++N)E.write_shift(1,L.content[N]);for(;N&511;++N)E.write_shift(1,0)}for(w=1;w<h.FileIndex.length;++w)if(L=h.FileIndex[w],L.size>0&&L.size<4096)if(ve&&Buffer.isBuffer(L.content))L.content.copy(E,E.l,0,L.size),E.l+=L.size+63&-64;else{for(N=0;N<L.size;++N)E.write_shift(1,L.content[N]);for(;N&63;++N)E.write_shift(1,0)}if(ve)E.l=E.length;else for(;E.l<E.length;)E.write_shift(1,0);return E}function M(h,_){var p=h.FullPaths.map(function(N){return N.toUpperCase()}),m=p.map(function(N){var P=N.split("/");return P[P.length-(N.slice(-1)=="/"?2:1)]}),E=!1;_.charCodeAt(0)===47?(E=!0,_=p[0].slice(0,-1)+_):E=_.indexOf("/")!==-1;var w=_.toUpperCase(),k=E===!0?p.indexOf(w):m.indexOf(w);if(k!==-1)return h.FileIndex[k];var W=!w.match(zt);for(w=w.replace(wt,""),W&&(w=w.replace(zt,"!")),k=0;k<p.length;++k)if((W?p[k].replace(zt,"!"):p[k]).replace(wt,"")==w||(W?m[k].replace(zt,"!"):m[k]).replace(wt,"")==w)return h.FileIndex[k];return null}var H=64,K=-2,ae="d0cf11e0a1b11ae1",ne=[208,207,17,224,161,177,26,225],he="00000000000000000000000000000000",fe={MAXREGSECT:-6,DIFSECT:-4,FATSECT:-3,ENDOFCHAIN:K,FREESECT:-1,HEADER_SIGNATURE:ae,HEADER_MINOR_VERSION:"3e00",MAXREGSID:-6,NOSTREAM:-1,HEADER_CLSID:he,EntryTypes:["unknown","storage","stream","lockbytes","property","root"]};function Fe(h,_,p){l();var m=I(h,p);o.writeFileSync(_,m)}function pe(h){for(var _=new Array(h.length),p=0;p<h.length;++p)_[p]=String.fromCharCode(h[p]);return _.join("")}function Ue(h,_){var p=I(h,_);switch(_&&_.type||"buffer"){case"file":return l(),o.writeFileSync(_.filename,p),p;case"binary":return typeof p=="string"?p:pe(p);case"base64":return Dt(typeof p=="string"?p:pe(p));case"buffer":if(ve)return Buffer.isBuffer(p)?p:Ir(p);case"array":return typeof p=="string"?pr(p):p}return p}var je;function S(h){try{var _=h.InflateRaw,p=new _;if(p._processChunk(new Uint8Array([3,0]),p._finishFlushFlag),p.bytesRead)je=h;else throw new Error("zlib does not expose bytesRead")}catch(m){console.error("cannot use native zlib: "+(m.message||m))}}function D(h,_){if(!je)return v0(h,_);var p=je.InflateRaw,m=new p,E=m._processChunk(h.slice(h.l),m._finishFlushFlag);return h.l+=m.bytesRead,E}function F(h){return je?je.deflateRawSync(h):o0(h)}var A=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],X=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258],le=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577];function oe(h){var _=(h<<1|h<<11)&139536|(h<<5|h<<15)&558144;return(_>>16|_>>8|_)&255}for(var se=typeof Uint8Array<"u",ee=se?new Uint8Array(1<<8):[],Se=0;Se<1<<8;++Se)ee[Se]=oe(Se);function xe(h,_){var p=ee[h&255];return _<=8?p>>>8-_:(p=p<<8|ee[h>>8&255],_<=16?p>>>16-_:(p=p<<8|ee[h>>16&255],p>>>24-_))}function Ye(h,_){var p=_&7,m=_>>>3;return(h[m]|(p<=6?0:h[m+1]<<8))>>>p&3}function me(h,_){var p=_&7,m=_>>>3;return(h[m]|(p<=5?0:h[m+1]<<8))>>>p&7}function Ar(h,_){var p=_&7,m=_>>>3;return(h[m]|(p<=4?0:h[m+1]<<8))>>>p&15}function Oe(h,_){var p=_&7,m=_>>>3;return(h[m]|(p<=3?0:h[m+1]<<8))>>>p&31}function ie(h,_){var p=_&7,m=_>>>3;return(h[m]|(p<=1?0:h[m+1]<<8))>>>p&127}function lr(h,_,p){var m=_&7,E=_>>>3,w=(1<<p)-1,k=h[E]>>>m;return p<8-m||(k|=h[E+1]<<8-m,p<16-m)||(k|=h[E+2]<<16-m,p<24-m)||(k|=h[E+3]<<24-m),k&w}function Fr(h,_,p){var m=_&7,E=_>>>3;return m<=5?h[E]|=(p&7)<<m:(h[E]|=p<<m&255,h[E+1]=(p&7)>>8-m),_+3}function Hr(h,_,p){var m=_&7,E=_>>>3;return p=(p&1)<<m,h[E]|=p,_+1}function rt(h,_,p){var m=_&7,E=_>>>3;return p<<=m,h[E]|=p&255,p>>>=8,h[E+1]=p,_+8}function l0(h,_,p){var m=_&7,E=_>>>3;return p<<=m,h[E]|=p&255,p>>>=8,h[E+1]=p&255,h[E+2]=p>>>8,_+16}function wn(h,_){var p=h.length,m=2*p>_?2*p:_+5,E=0;if(p>=_)return h;if(ve){var w=A0(m);if(h.copy)h.copy(w);else for(;E<h.length;++E)w[E]=h[E];return w}else if(se){var k=new Uint8Array(m);if(k.set)k.set(h);else for(;E<p;++E)k[E]=h[E];return k}return h.length=m,h}function _r(h){for(var _=new Array(h),p=0;p<h;++p)_[p]=0;return _}function Wt(h,_,p){var m=1,E=0,w=0,k=0,W=0,N=h.length,P=se?new Uint16Array(32):_r(32);for(w=0;w<32;++w)P[w]=0;for(w=N;w<p;++w)h[w]=0;N=h.length;var L=se?new Uint16Array(N):_r(N);for(w=0;w<N;++w)P[E=h[w]]++,m<E&&(m=E),L[w]=0;for(P[0]=0,w=1;w<=m;++w)P[w+16]=W=W+P[w-1]<<1;for(w=0;w<N;++w)W=h[w],W!=0&&(L[w]=P[W+16]++);var z=0;for(w=0;w<N;++w)if(z=h[w],z!=0)for(W=xe(L[w],m)>>m-z,k=(1<<m+4-z)-1;k>=0;--k)_[W|k<<z]=z&15|w<<4;return m}var Sn=se?new Uint16Array(512):_r(512),An=se?new Uint16Array(32):_r(32);if(!se){for(var Vr=0;Vr<512;++Vr)Sn[Vr]=0;for(Vr=0;Vr<32;++Vr)An[Vr]=0}(function(){for(var h=[],_=0;_<32;_++)h.push(5);Wt(h,An,32);var p=[];for(_=0;_<=143;_++)p.push(8);for(;_<=255;_++)p.push(9);for(;_<=279;_++)p.push(7);for(;_<=287;_++)p.push(8);Wt(p,Sn,288)})();var $i=function(){for(var _=se?new Uint8Array(32768):[],p=0,m=0;p<le.length-1;++p)for(;m<le[p+1];++m)_[m]=p;for(;m<32768;++m)_[m]=29;var E=se?new Uint8Array(259):[];for(p=0,m=0;p<X.length-1;++p)for(;m<X[p+1];++m)E[m]=p;function w(W,N){for(var P=0;P<W.length;){var L=Math.min(65535,W.length-P),z=P+L==W.length;for(N.write_shift(1,+z),N.write_shift(2,L),N.write_shift(2,~L&65535);L-- >0;)N[N.l++]=W[P++]}return N.l}function k(W,N){for(var P=0,L=0,z=se?new Uint16Array(32768):[];L<W.length;){var q=Math.min(65535,W.length-L);if(q<10){for(P=Fr(N,P,+(L+q==W.length)),P&7&&(P+=8-(P&7)),N.l=P/8|0,N.write_shift(2,q),N.write_shift(2,~q&65535);q-- >0;)N[N.l++]=W[L++];P=N.l*8;continue}P=Fr(N,P,+(L+q==W.length)+2);for(var te=0;q-- >0;){var j=W[L];te=(te<<5^j)&32767;var re=-1,ce=0;if((re=z[te])&&(re|=L&-32768,re>L&&(re-=32768),re<L))for(;W[re+ce]==W[L+ce]&&ce<250;)++ce;if(ce>2){j=E[ce],j<=22?P=rt(N,P,ee[j+1]>>1)-1:(rt(N,P,3),P+=5,rt(N,P,ee[j-23]>>5),P+=3);var ye=j<8?0:j-4>>2;ye>0&&(l0(N,P,ce-X[j]),P+=ye),j=_[L-re],P=rt(N,P,ee[j]>>3),P-=3;var Ce=j<4?0:j-2>>1;Ce>0&&(l0(N,P,L-re-le[j]),P+=Ce);for(var $e=0;$e<ce;++$e)z[te]=L&32767,te=(te<<5^W[L])&32767,++L;q-=ce-1}else j<=143?j=j+48:P=Hr(N,P,1),P=rt(N,P,ee[j]),z[te]=L&32767,++L}P=rt(N,P,0)-1}return N.l=(P+7)/8|0,N.l}return function(N,P){return N.length<8?w(N,P):k(N,P)}}();function o0(h){var _=b(50+Math.floor(h.length*1.1)),p=$i(h,_);return _.slice(0,p)}var c0=se?new Uint16Array(32768):_r(32768),h0=se?new Uint16Array(32768):_r(32768),u0=se?new Uint16Array(128):_r(128),x0=1,d0=1;function zi(h,_){var p=Oe(h,_)+257;_+=5;var m=Oe(h,_)+1;_+=5;var E=Ar(h,_)+4;_+=4;for(var w=0,k=se?new Uint8Array(19):_r(19),W=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],N=1,P=se?new Uint8Array(8):_r(8),L=se?new Uint8Array(8):_r(8),z=k.length,q=0;q<E;++q)k[A[q]]=w=me(h,_),N<w&&(N=w),P[w]++,_+=3;var te=0;for(P[0]=0,q=1;q<=N;++q)L[q]=te=te+P[q-1]<<1;for(q=0;q<z;++q)(te=k[q])!=0&&(W[q]=L[te]++);var j=0;for(q=0;q<z;++q)if(j=k[q],j!=0){te=ee[W[q]]>>8-j;for(var re=(1<<7-j)-1;re>=0;--re)u0[te|re<<j]=j&7|q<<3}var ce=[];for(N=1;ce.length<p+m;)switch(te=u0[ie(h,_)],_+=te&7,te>>>=3){case 16:for(w=3+Ye(h,_),_+=2,te=ce[ce.length-1];w-- >0;)ce.push(te);break;case 17:for(w=3+me(h,_),_+=3;w-- >0;)ce.push(0);break;case 18:for(w=11+ie(h,_),_+=7;w-- >0;)ce.push(0);break;default:ce.push(te),N<te&&(N=te);break}var ye=ce.slice(0,p),Ce=ce.slice(p);for(q=p;q<286;++q)ye[q]=0;for(q=m;q<30;++q)Ce[q]=0;return x0=Wt(ye,c0,286),d0=Wt(Ce,h0,30),_}function Ki(h,_){if(h[0]==3&&!(h[1]&3))return[Kr(_),2];for(var p=0,m=0,E=A0(_||1<<18),w=0,k=E.length>>>0,W=0,N=0;(m&1)==0;){if(m=me(h,p),p+=3,m>>>1==0){p&7&&(p+=8-(p&7));var P=h[p>>>3]|h[(p>>>3)+1]<<8;if(p+=32,P>0)for(!_&&k<w+P&&(E=wn(E,w+P),k=E.length);P-- >0;)E[w++]=h[p>>>3],p+=8;continue}else m>>1==1?(W=9,N=5):(p=zi(h,p),W=x0,N=d0);for(;;){!_&&k<w+32767&&(E=wn(E,w+32767),k=E.length);var L=lr(h,p,W),z=m>>>1==1?Sn[L]:c0[L];if(p+=z&15,z>>>=4,(z>>>8&255)===0)E[w++]=z;else{if(z==256)break;z-=257;var q=z<8?0:z-4>>2;q>5&&(q=0);var te=w+X[z];q>0&&(te+=lr(h,p,q),p+=q),L=lr(h,p,N),z=m>>>1==1?An[L]:h0[L],p+=z&15,z>>>=4;var j=z<4?0:z-2>>1,re=le[z];for(j>0&&(re+=lr(h,p,j),p+=j),!_&&k<te&&(E=wn(E,te+100),k=E.length);w<te;)E[w]=E[w-re],++w}}}return _?[E,p+7>>>3]:[E.slice(0,w),p+7>>>3]}function v0(h,_){var p=h.slice(h.l||0),m=Ki(p,_);return h.l+=m[1],m[0]}function p0(h,_){if(h)typeof console<"u"&&console.error(_);else throw new Error(_)}function m0(h,_){var p=h;ar(p,0);var m=[],E=[],w={FileIndex:m,FullPaths:E};O(w,{root:_.root});for(var k=p.length-4;(p[k]!=80||p[k+1]!=75||p[k+2]!=5||p[k+3]!=6)&&k>=0;)--k;p.l=k+4,p.l+=4;var W=p.read_shift(2);p.l+=6;var N=p.read_shift(4);for(p.l=N,k=0;k<W;++k){p.l+=20;var P=p.read_shift(4),L=p.read_shift(4),z=p.read_shift(2),q=p.read_shift(2),te=p.read_shift(2);p.l+=8;var j=p.read_shift(4),re=f(p.slice(p.l+z,p.l+z+q));p.l+=z+q+te;var ce=p.l;p.l=j+4,ji(p,P,L,w,re),p.l=ce}return w}function ji(h,_,p,m,E){h.l+=2;var w=h.read_shift(2),k=h.read_shift(2),W=s(h);if(w&8257)throw new Error("Unsupported ZIP encryption");for(var N=h.read_shift(4),P=h.read_shift(4),L=h.read_shift(4),z=h.read_shift(2),q=h.read_shift(2),te="",j=0;j<z;++j)te+=String.fromCharCode(h[h.l++]);if(q){var re=f(h.slice(h.l,h.l+q));(re[21589]||{}).mt&&(W=re[21589].mt),((E||{})[21589]||{}).mt&&(W=E[21589].mt)}h.l+=q;var ce=h.slice(h.l,h.l+P);switch(k){case 8:ce=D(h,L);break;case 0:break;default:throw new Error("Unsupported ZIP Compression method "+k)}var ye=!1;w&8&&(N=h.read_shift(4),N==134695760&&(N=h.read_shift(4),ye=!0),P=h.read_shift(4),L=h.read_shift(4)),P!=_&&p0(ye,"Bad compressed size: "+_+" != "+P),L!=p&&p0(ye,"Bad uncompressed size: "+p+" != "+L),Fn(m,te,ce,{unsafe:!0,mt:W})}function Yi(h,_){var p=_||{},m=[],E=[],w=b(1),k=p.compression?8:0,W=0,N=0,P=0,L=0,z=0,q=h.FullPaths[0],te=q,j=h.FileIndex[0],re=[],ce=0;for(N=1;N<h.FullPaths.length;++N)if(te=h.FullPaths[N].slice(q.length),j=h.FileIndex[N],!(!j.size||!j.content||te=="Sh33tJ5")){var ye=L,Ce=b(te.length);for(P=0;P<te.length;++P)Ce.write_shift(1,te.charCodeAt(P)&127);Ce=Ce.slice(0,Ce.l),re[z]=nf.buf(j.content,0);var $e=j.content;k==8&&($e=F($e)),w=b(30),w.write_shift(4,67324752),w.write_shift(2,20),w.write_shift(2,W),w.write_shift(2,k),j.mt?i(w,j.mt):w.write_shift(4,0),w.write_shift(-4,re[z]),w.write_shift(4,$e.length),w.write_shift(4,j.content.length),w.write_shift(2,Ce.length),w.write_shift(2,0),L+=w.length,m.push(w),L+=Ce.length,m.push(Ce),L+=$e.length,m.push($e),w=b(46),w.write_shift(4,33639248),w.write_shift(2,0),w.write_shift(2,20),w.write_shift(2,W),w.write_shift(2,k),w.write_shift(4,0),w.write_shift(-4,re[z]),w.write_shift(4,$e.length),w.write_shift(4,j.content.length),w.write_shift(2,Ce.length),w.write_shift(2,0),w.write_shift(2,0),w.write_shift(2,0),w.write_shift(2,0),w.write_shift(4,0),w.write_shift(4,ye),ce+=w.l,E.push(w),ce+=Ce.length,E.push(Ce),++z}return w=b(22),w.write_shift(4,101010256),w.write_shift(2,0),w.write_shift(2,0),w.write_shift(2,z),w.write_shift(2,z),w.write_shift(4,ce),w.write_shift(4,L),w.write_shift(2,0),He([He(m),He(E),w])}var Ht={htm:"text/html",xml:"text/xml",gif:"image/gif",jpg:"image/jpeg",png:"image/png",mso:"application/x-mso",thmx:"application/vnd.ms-officetheme",sh33tj5:"application/octet-stream"};function Ji(h,_){if(h.ctype)return h.ctype;var p=h.name||"",m=p.match(/\.([^\.]+)$/);return m&&Ht[m[1]]||_&&(m=(p=_).match(/[\.\\]([^\.\\])+$/),m&&Ht[m[1]])?Ht[m[1]]:"application/octet-stream"}function Zi(h){for(var _=Dt(h),p=[],m=0;m<_.length;m+=76)p.push(_.slice(m,m+76));return p.join(`\r
`)+`\r
`}function qi(h){var _=h.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7E-\xFF=]/g,function(P){var L=P.charCodeAt(0).toString(16).toUpperCase();return"="+(L.length==1?"0"+L:L)});_=_.replace(/ $/mg,"=20").replace(/\t$/mg,"=09"),_.charAt(0)==`
`&&(_="=0D"+_.slice(1)),_=_.replace(/\r(?!\n)/mg,"=0D").replace(/\n\n/mg,`
=0A`).replace(/([^\r\n])\n/mg,"$1=0A");for(var p=[],m=_.split(`\r
`),E=0;E<m.length;++E){var w=m[E];if(w.length==0){p.push("");continue}for(var k=0;k<w.length;){var W=76,N=w.slice(k,k+W);N.charAt(W-1)=="="?W--:N.charAt(W-2)=="="?W-=2:N.charAt(W-3)=="="&&(W-=3),N=w.slice(k,k+W),k+=W,k<w.length&&(N+="="),p.push(N)}}return p.join(`\r
`)}function Qi(h){for(var _=[],p=0;p<h.length;++p){for(var m=h[p];p<=h.length&&m.charAt(m.length-1)=="=";)m=m.slice(0,m.length-1)+h[++p];_.push(m)}for(var E=0;E<_.length;++E)_[E]=_[E].replace(/[=][0-9A-Fa-f]{2}/g,function(w){return String.fromCharCode(parseInt(w.slice(1),16))});return pr(_.join(`\r
`))}function es(h,_,p){for(var m="",E="",w="",k,W=0;W<10;++W){var N=_[W];if(!N||N.match(/^\s*$/))break;var P=N.match(/^(.*?):\s*([^\s].*)$/);if(P)switch(P[1].toLowerCase()){case"content-location":m=P[2].trim();break;case"content-type":w=P[2].trim();break;case"content-transfer-encoding":E=P[2].trim();break}}switch(++W,E.toLowerCase()){case"base64":k=pr(Nr(_.slice(W).join("")));break;case"quoted-printable":k=Qi(_.slice(W));break;default:throw new Error("Unsupported Content-Transfer-Encoding "+E)}var L=Fn(h,m.slice(p.length),k,{unsafe:!0});w&&(L.ctype=w)}function rs(h,_){if(pe(h.slice(0,13)).toLowerCase()!="mime-version:")throw new Error("Unsupported MAD header");var p=_&&_.root||"",m=(ve&&Buffer.isBuffer(h)?h.toString("binary"):pe(h)).split(`\r
`),E=0,w="";for(E=0;E<m.length;++E)if(w=m[E],!!/^Content-Location:/i.test(w)&&(w=w.slice(w.indexOf("file")),p||(p=w.slice(0,w.lastIndexOf("/")+1)),w.slice(0,p.length)!=p))for(;p.length>0&&(p=p.slice(0,p.length-1),p=p.slice(0,p.lastIndexOf("/")+1),w.slice(0,p.length)!=p););var k=(m[1]||"").match(/boundary="(.*?)"/);if(!k)throw new Error("MAD cannot find boundary");var W="--"+(k[1]||""),N=[],P=[],L={FileIndex:N,FullPaths:P};O(L);var z,q=0;for(E=0;E<m.length;++E){var te=m[E];te!==W&&te!==W+"--"||(q++&&es(L,m.slice(z,E),p),z=E)}return L}function ts(h,_){var p=_||{},m=p.boundary||"SheetJS";m="------="+m;for(var E=["MIME-Version: 1.0",'Content-Type: multipart/related; boundary="'+m.slice(2)+'"',"","",""],w=h.FullPaths[0],k=w,W=h.FileIndex[0],N=1;N<h.FullPaths.length;++N)if(k=h.FullPaths[N].slice(w.length),W=h.FileIndex[N],!(!W.size||!W.content||k=="Sh33tJ5")){k=k.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7E-\xFF]/g,function(ce){return"_x"+ce.charCodeAt(0).toString(16)+"_"}).replace(/[\u0080-\uFFFF]/g,function(ce){return"_u"+ce.charCodeAt(0).toString(16)+"_"});for(var P=W.content,L=ve&&Buffer.isBuffer(P)?P.toString("binary"):pe(P),z=0,q=Math.min(1024,L.length),te=0,j=0;j<=q;++j)(te=L.charCodeAt(j))>=32&&te<128&&++z;var re=z>=q*4/5;E.push(m),E.push("Content-Location: "+(p.root||"file:///C:/SheetJS/")+k),E.push("Content-Transfer-Encoding: "+(re?"quoted-printable":"base64")),E.push("Content-Type: "+Ji(W,k)),E.push(""),E.push(re?qi(L):Zi(L))}return E.push(m+`--\r
`),E.join(`\r
`)}function ns(h){var _={};return O(_,h),_}function Fn(h,_,p,m){var E=m&&m.unsafe;E||O(h);var w=!E&&we.find(h,_);if(!w){var k=h.FullPaths[0];_.slice(0,k.length)==k?k=_:(k.slice(-1)!="/"&&(k+="/"),k=(k+_).replace("//","/")),w={name:a(_),type:2},h.FileIndex.push(w),h.FullPaths.push(k),E||we.utils.cfb_gc(h)}return w.content=p,w.size=p?p.length:0,m&&(m.CLSID&&(w.clsid=m.CLSID),m.mt&&(w.mt=m.mt),m.ct&&(w.ct=m.ct)),w}function as(h,_){O(h);var p=we.find(h,_);if(p){for(var m=0;m<h.FileIndex.length;++m)if(h.FileIndex[m]==p)return h.FileIndex.splice(m,1),h.FullPaths.splice(m,1),!0}return!1}function is(h,_,p){O(h);var m=we.find(h,_);if(m){for(var E=0;E<h.FileIndex.length;++E)if(h.FileIndex[E]==m)return h.FileIndex[E].name=a(p),h.FullPaths[E]=p,!0}return!1}function ss(h){B(h,!0)}return t.find=M,t.read=Q,t.parse=c,t.write=Ue,t.writeFile=Fe,t.utils={cfb_new:ns,cfb_add:Fn,cfb_del:as,cfb_mov:is,cfb_gc:ss,ReadShift:At,CheckField:Ga,prep_blob:ar,bconcat:He,use_zlib:S,_deflateRaw:o0,_inflateRaw:v0,consts:fe},t}();function af(e){return typeof e=="string"?dn(e):Array.isArray(e)?Rs(e):e}function Lt(e,t,r){if(typeof Deno<"u"){if(r&&typeof t=="string")switch(r){case"utf8":t=new TextEncoder(r).encode(t);break;case"binary":t=dn(t);break;default:throw new Error("Unsupported encoding "+r)}return Deno.writeFileSync(e,t)}var n=r=="utf8"?Cr(t):t;if(typeof IE_SaveFile<"u")return IE_SaveFile(n,e);if(typeof Blob<"u"){var a=new Blob([af(n)],{type:"application/octet-stream"});if(typeof navigator<"u"&&navigator.msSaveBlob)return navigator.msSaveBlob(a,e);if(typeof saveAs<"u")return saveAs(a,e);if(typeof URL<"u"&&typeof document<"u"&&document.createElement&&URL.createObjectURL){var i=URL.createObjectURL(a);if(typeof chrome=="object"&&typeof(chrome.downloads||{}).download=="function")return URL.revokeObjectURL&&typeof setTimeout<"u"&&setTimeout(function(){URL.revokeObjectURL(i)},6e4),chrome.downloads.download({url:i,filename:e,saveAs:!0});var s=document.createElement("a");if(s.download!=null)return s.download=e,s.href=i,document.body.appendChild(s),s.click(),document.body.removeChild(s),URL.revokeObjectURL&&typeof setTimeout<"u"&&setTimeout(function(){URL.revokeObjectURL(i)},6e4),i}}if(typeof $<"u"&&typeof File<"u"&&typeof Folder<"u")try{var f=File(e);return f.open("w"),f.encoding="binary",Array.isArray(t)&&(t=Pt(t)),f.write(t),f.close(),t}catch(o){if(!o.message||!o.message.match(/onstruct/))throw o}throw new Error("cannot save file "+e)}function Xe(e){for(var t=Object.keys(e),r=[],n=0;n<t.length;++n)Object.prototype.hasOwnProperty.call(e,t[n])&&r.push(t[n]);return r}function k0(e,t){for(var r=[],n=Xe(e),a=0;a!==n.length;++a)r[e[n[a]][t]]==null&&(r[e[n[a]][t]]=n[a]);return r}function Gn(e){for(var t=[],r=Xe(e),n=0;n!==r.length;++n)t[e[r[n]]]=r[n];return t}function mn(e){for(var t=[],r=Xe(e),n=0;n!==r.length;++n)t[e[r[n]]]=parseInt(r[n],10);return t}function sf(e){for(var t=[],r=Xe(e),n=0;n!==r.length;++n)t[e[r[n]]]==null&&(t[e[r[n]]]=[]),t[e[r[n]]].push(r[n]);return t}var nn=new Date(1899,11,30,0,0,0);function rr(e,t){var r=e.getTime();t&&(r-=1462*24*60*60*1e3);var n=nn.getTime()+(e.getTimezoneOffset()-nn.getTimezoneOffset())*6e4;return(r-n)/(24*60*60*1e3)}var ya=new Date,ff=nn.getTime()+(ya.getTimezoneOffset()-nn.getTimezoneOffset())*6e4,I0=ya.getTimezoneOffset();function Ca(e){var t=new Date;return t.setTime(e*24*60*60*1e3+ff),t.getTimezoneOffset()!==I0&&t.setTime(t.getTime()+(t.getTimezoneOffset()-I0)*6e4),t}var P0=new Date("2017-02-19T19:06:09.000Z"),Oa=isNaN(P0.getFullYear())?new Date("2/19/17"):P0,lf=Oa.getFullYear()==2017;function qe(e,t){var r=new Date(e);if(lf)return t>0?r.setTime(r.getTime()+r.getTimezoneOffset()*60*1e3):t<0&&r.setTime(r.getTime()-r.getTimezoneOffset()*60*1e3),r;if(e instanceof Date)return e;if(Oa.getFullYear()==1917&&!isNaN(r.getFullYear())){var n=r.getFullYear();return e.indexOf(""+n)>-1||r.setFullYear(r.getFullYear()+100),r}var a=e.match(/\d+/g)||["2017","2","19","0","0","0"],i=new Date(+a[0],+a[1]-1,+a[2],+a[3]||0,+a[4]||0,+a[5]||0);return e.indexOf("Z")>-1&&(i=new Date(i.getTime()-i.getTimezoneOffset()*60*1e3)),i}function gn(e,t){if(ve&&Buffer.isBuffer(e)){if(t){if(e[0]==255&&e[1]==254)return Cr(e.slice(2).toString("utf16le"));if(e[1]==254&&e[2]==255)return Cr(Ds(e.slice(2).toString("binary")))}return e.toString("binary")}if(typeof TextDecoder<"u")try{if(t){if(e[0]==255&&e[1]==254)return Cr(new TextDecoder("utf-16le").decode(e.slice(2)));if(e[0]==254&&e[1]==255)return Cr(new TextDecoder("utf-16be").decode(e.slice(2)))}var r={"\u20AC":"\x80","\u201A":"\x82",\u0192:"\x83","\u201E":"\x84","\u2026":"\x85","\u2020":"\x86","\u2021":"\x87","\u02C6":"\x88","\u2030":"\x89",\u0160:"\x8A","\u2039":"\x8B",\u0152:"\x8C",\u017D:"\x8E","\u2018":"\x91","\u2019":"\x92","\u201C":"\x93","\u201D":"\x94","\u2022":"\x95","\u2013":"\x96","\u2014":"\x97","\u02DC":"\x98","\u2122":"\x99",\u0161:"\x9A","\u203A":"\x9B",\u0153:"\x9C",\u017E:"\x9E",\u0178:"\x9F"};return Array.isArray(e)&&(e=new Uint8Array(e)),new TextDecoder("latin1").decode(e).replace(/[€‚ƒ„…†‡ˆ‰Š‹ŒŽ‘’“”•–—˜™š›œžŸ]/g,function(i){return r[i]||i})}catch{}for(var n=[],a=0;a!=e.length;++a)n.push(String.fromCharCode(e[a]));return n.join("")}function tr(e){if(typeof JSON<"u"&&!Array.isArray(e))return JSON.parse(JSON.stringify(e));if(typeof e!="object"||e==null)return e;if(e instanceof Date)return new Date(e.getTime());var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=tr(e[r]));return t}function De(e,t){for(var r="";r.length<t;)r+=e;return r}function Dr(e){var t=Number(e);if(!isNaN(t))return isFinite(t)?t:NaN;if(!/\d/.test(e))return t;var r=1,n=e.replace(/([\d]),([\d])/g,"$1$2").replace(/[$]/g,"").replace(/[%]/g,function(){return r*=100,""});return!isNaN(t=Number(n))||(n=n.replace(/[(](.*)[)]/,function(a,i){return r=-r,i}),!isNaN(t=Number(n)))?t/r:t}var of=["january","february","march","april","may","june","july","august","september","october","november","december"];function Rt(e){var t=new Date(e),r=new Date(NaN),n=t.getYear(),a=t.getMonth(),i=t.getDate();if(isNaN(i))return r;var s=e.toLowerCase();if(s.match(/jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec/)){if(s=s.replace(/[^a-z]/g,"").replace(/([^a-z]|^)[ap]m?([^a-z]|$)/,""),s.length>3&&of.indexOf(s)==-1)return r}else if(s.match(/[a-z]/))return r;return n<0||n>8099?r:(a>0||i>1)&&n!=101?t:e.match(/[^-0-9:,\/\\]/)?r:t}function ue(e,t,r){if(e.FullPaths){if(typeof r=="string"){var n;return ve?n=Ir(r):n=Ns(r),we.utils.cfb_add(e,t,n)}we.utils.cfb_add(e,t,r)}else e.file(t,r)}function Xn(){return we.utils.cfb_new()}var Ie=`<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\r
`,cf={"&quot;":'"',"&apos;":"'","&gt;":">","&lt;":"<","&amp;":"&"},$n=Gn(cf),zn=/[&<>'"]/g,hf=/[\u0000-\u0008\u000b-\u001f]/g;function Te(e){var t=e+"";return t.replace(zn,function(r){return $n[r]}).replace(hf,function(r){return"_x"+("000"+r.charCodeAt(0).toString(16)).slice(-4)+"_"})}function L0(e){return Te(e).replace(/ /g,"_x0020_")}var Da=/[\u0000-\u001f]/g;function uf(e){var t=e+"";return t.replace(zn,function(r){return $n[r]}).replace(/\n/g,"<br/>").replace(Da,function(r){return"&#x"+("000"+r.charCodeAt(0).toString(16)).slice(-4)+";"})}function xf(e){var t=e+"";return t.replace(zn,function(r){return $n[r]}).replace(Da,function(r){return"&#x"+r.charCodeAt(0).toString(16).toUpperCase()+";"})}function df(e){return e.replace(/(\r\n|[\r\n])/g,"&#10;")}function vf(e){switch(e){case 1:case!0:case"1":case"true":case"TRUE":return!0;default:return!1}}function Rn(e){for(var t="",r=0,n=0,a=0,i=0,s=0,f=0;r<e.length;){if(n=e.charCodeAt(r++),n<128){t+=String.fromCharCode(n);continue}if(a=e.charCodeAt(r++),n>191&&n<224){s=(n&31)<<6,s|=a&63,t+=String.fromCharCode(s);continue}if(i=e.charCodeAt(r++),n<240){t+=String.fromCharCode((n&15)<<12|(a&63)<<6|i&63);continue}s=e.charCodeAt(r++),f=((n&7)<<18|(a&63)<<12|(i&63)<<6|s&63)-65536,t+=String.fromCharCode(55296+(f>>>10&1023)),t+=String.fromCharCode(56320+(f&1023))}return t}function B0(e){var t=Kr(2*e.length),r,n,a=1,i=0,s=0,f;for(n=0;n<e.length;n+=a)a=1,(f=e.charCodeAt(n))<128?r=f:f<224?(r=(f&31)*64+(e.charCodeAt(n+1)&63),a=2):f<240?(r=(f&15)*4096+(e.charCodeAt(n+1)&63)*64+(e.charCodeAt(n+2)&63),a=3):(a=4,r=(f&7)*262144+(e.charCodeAt(n+1)&63)*4096+(e.charCodeAt(n+2)&63)*64+(e.charCodeAt(n+3)&63),r-=65536,s=55296+(r>>>10&1023),r=56320+(r&1023)),s!==0&&(t[i++]=s&255,t[i++]=s>>>8,s=0),t[i++]=r%256,t[i++]=r>>>8;return t.slice(0,i).toString("ucs2")}function M0(e){return Ir(e,"binary").toString("utf8")}var jt="foo bar baz\xE2\x98\x83\xF0\x9F\x8D\xA3",St=ve&&(M0(jt)==Rn(jt)&&M0||B0(jt)==Rn(jt)&&B0)||Rn,Cr=ve?function(e){return Ir(e,"utf8").toString("binary")}:function(e){for(var t=[],r=0,n=0,a=0;r<e.length;)switch(n=e.charCodeAt(r++),!0){case n<128:t.push(String.fromCharCode(n));break;case n<2048:t.push(String.fromCharCode(192+(n>>6))),t.push(String.fromCharCode(128+(n&63)));break;case(n>=55296&&n<57344):n-=55296,a=e.charCodeAt(r++)-56320+(n<<10),t.push(String.fromCharCode(240+(a>>18&7))),t.push(String.fromCharCode(144+(a>>12&63))),t.push(String.fromCharCode(128+(a>>6&63))),t.push(String.fromCharCode(128+(a&63)));break;default:t.push(String.fromCharCode(224+(n>>12))),t.push(String.fromCharCode(128+(n>>6&63))),t.push(String.fromCharCode(128+(n&63)))}return t.join("")},pf=function(){var e=[["nbsp"," "],["middot","\xB7"],["quot",'"'],["apos","'"],["gt",">"],["lt","<"],["amp","&"]].map(function(t){return[new RegExp("&"+t[0]+";","ig"),t[1]]});return function(r){for(var n=r.replace(/^[\t\n\r ]+/,"").replace(/[\t\n\r ]+$/,"").replace(/>\s+/g,">").replace(/\s+</g,"<").replace(/[\t\n\r ]+/g," ").replace(/<\s*[bB][rR]\s*\/?>/g,`
`).replace(/<[^>]*>/g,""),a=0;a<e.length;++a)n=n.replace(e[a][0],e[a][1]);return n}}(),Ra=/(^\s|\s$|\n)/;function Ve(e,t){return"<"+e+(t.match(Ra)?' xml:space="preserve"':"")+">"+t+"</"+e+">"}function Nt(e){return Xe(e).map(function(t){return" "+t+'="'+e[t]+'"'}).join("")}function J(e,t,r){return"<"+e+(r!=null?Nt(r):"")+(t!=null?(t.match(Ra)?' xml:space="preserve"':"")+">"+t+"</"+e:"/")+">"}function Un(e,t){try{return e.toISOString().replace(/\.\d*/,"")}catch(r){if(t)throw r}return""}function mf(e,t){switch(typeof e){case"string":var r=J("vt:lpwstr",Te(e));return t&&(r=r.replace(/&quot;/g,"_x0022_")),r;case"number":return J((e|0)==e?"vt:i4":"vt:r8",Te(String(e)));case"boolean":return J("vt:bool",e?"true":"false")}if(e instanceof Date)return J("vt:filetime",Un(e));throw new Error("Unable to serialize "+e)}var Le={CORE_PROPS:"http://schemas.openxmlformats.org/package/2006/metadata/core-properties",CUST_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/custom-properties",EXT_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/extended-properties",CT:"http://schemas.openxmlformats.org/package/2006/content-types",RELS:"http://schemas.openxmlformats.org/package/2006/relationships",TCMNT:"http://schemas.microsoft.com/office/spreadsheetml/2018/threadedcomments",dc:"http://purl.org/dc/elements/1.1/",dcterms:"http://purl.org/dc/terms/",dcmitype:"http://purl.org/dc/dcmitype/",mx:"http://schemas.microsoft.com/office/mac/excel/2008/main",r:"http://schemas.openxmlformats.org/officeDocument/2006/relationships",sjs:"http://schemas.openxmlformats.org/package/2006/sheetjs/core-properties",vt:"http://schemas.openxmlformats.org/officeDocument/2006/docPropsVTypes",xsi:"http://www.w3.org/2001/XMLSchema-instance",xsd:"http://www.w3.org/2001/XMLSchema"},xt=["http://schemas.openxmlformats.org/spreadsheetml/2006/main","http://purl.oclc.org/ooxml/spreadsheetml/main","http://schemas.microsoft.com/office/excel/2006/main","http://schemas.microsoft.com/office/excel/2006/2"],ir={o:"urn:schemas-microsoft-com:office:office",x:"urn:schemas-microsoft-com:office:excel",ss:"urn:schemas-microsoft-com:office:spreadsheet",dt:"uuid:C2F41010-65B3-11d1-A29F-00AA00C14882",mv:"http://macVmlSchemaUri",v:"urn:schemas-microsoft-com:vml",html:"http://www.w3.org/TR/REC-html40"};function gf(e,t){for(var r=1-2*(e[t+7]>>>7),n=((e[t+7]&127)<<4)+(e[t+6]>>>4&15),a=e[t+6]&15,i=5;i>=0;--i)a=a*256+e[t+i];return n==2047?a==0?r*(1/0):NaN:(n==0?n=-1022:(n-=1023,a+=Math.pow(2,52)),r*Math.pow(2,n-52)*a)}function _f(e,t,r){var n=(t<0||1/t==-1/0?1:0)<<7,a=0,i=0,s=n?-t:t;isFinite(s)?s==0?a=i=0:(a=Math.floor(Math.log(s)/Math.LN2),i=s*Math.pow(2,52-a),a<=-1023&&(!isFinite(i)||i<Math.pow(2,52))?a=-1022:(i-=Math.pow(2,52),a+=1023)):(a=2047,i=isNaN(t)?26985:0);for(var f=0;f<=5;++f,i/=256)e[r+f]=i&255;e[r+6]=(a&15)<<4|i&15,e[r+7]=a>>4|n}var U0=function(e){for(var t=[],r=10240,n=0;n<e[0].length;++n)if(e[0][n])for(var a=0,i=e[0][n].length;a<i;a+=r)t.push.apply(t,e[0][n].slice(a,a+r));return t},b0=ve?function(e){return e[0].length>0&&Buffer.isBuffer(e[0][0])?Buffer.concat(e[0].map(function(t){return Buffer.isBuffer(t)?t:Ir(t)})):U0(e)}:U0,W0=function(e,t,r){for(var n=[],a=t;a<r;a+=2)n.push(String.fromCharCode(Et(e,a)));return n.join("").replace(wt,"")},Kn=ve?function(e,t,r){return Buffer.isBuffer(e)?e.toString("utf16le",t,r).replace(wt,""):W0(e,t,r)}:W0,H0=function(e,t,r){for(var n=[],a=t;a<t+r;++a)n.push(("0"+e[a].toString(16)).slice(-2));return n.join("")},Na=ve?function(e,t,r){return Buffer.isBuffer(e)?e.toString("hex",t,t+r):H0(e,t,r)}:H0,V0=function(e,t,r){for(var n=[],a=t;a<r;a++)n.push(String.fromCharCode(st(e,a)));return n.join("")},Bt=ve?function(t,r,n){return Buffer.isBuffer(t)?t.toString("utf8",r,n):V0(t,r,n)}:V0,ka=function(e,t){var r=sr(e,t);return r>0?Bt(e,t+4,t+4+r-1):""},Ia=ka,Pa=function(e,t){var r=sr(e,t);return r>0?Bt(e,t+4,t+4+r-1):""},La=Pa,Ba=function(e,t){var r=2*sr(e,t);return r>0?Bt(e,t+4,t+4+r-1):""},Ma=Ba,Ua=function(t,r){var n=sr(t,r);return n>0?Kn(t,r+4,r+4+n):""},ba=Ua,Wa=function(e,t){var r=sr(e,t);return r>0?Bt(e,t+4,t+4+r):""},Ha=Wa,Va=function(e,t){return gf(e,t)},an=Va,jn=function(t){return Array.isArray(t)||typeof Uint8Array<"u"&&t instanceof Uint8Array};ve&&(Ia=function(t,r){if(!Buffer.isBuffer(t))return ka(t,r);var n=t.readUInt32LE(r);return n>0?t.toString("utf8",r+4,r+4+n-1):""},La=function(t,r){if(!Buffer.isBuffer(t))return Pa(t,r);var n=t.readUInt32LE(r);return n>0?t.toString("utf8",r+4,r+4+n-1):""},Ma=function(t,r){if(!Buffer.isBuffer(t))return Ba(t,r);var n=2*t.readUInt32LE(r);return t.toString("utf16le",r+4,r+4+n-1)},ba=function(t,r){if(!Buffer.isBuffer(t))return Ua(t,r);var n=t.readUInt32LE(r);return t.toString("utf16le",r+4,r+4+n)},Ha=function(t,r){if(!Buffer.isBuffer(t))return Wa(t,r);var n=t.readUInt32LE(r);return t.toString("utf8",r+4,r+4+n)},an=function(t,r){return Buffer.isBuffer(t)?t.readDoubleLE(r):Va(t,r)},jn=function(t){return Buffer.isBuffer(t)||Array.isArray(t)||typeof Uint8Array<"u"&&t instanceof Uint8Array});var st=function(e,t){return e[t]},Et=function(e,t){return e[t+1]*(1<<8)+e[t]},Tf=function(e,t){var r=e[t+1]*256+e[t];return r<32768?r:(65535-r+1)*-1},sr=function(e,t){return e[t+3]*(1<<24)+(e[t+2]<<16)+(e[t+1]<<8)+e[t]},$r=function(e,t){return e[t+3]<<24|e[t+2]<<16|e[t+1]<<8|e[t]},Ef=function(e,t){return e[t]<<24|e[t+1]<<16|e[t+2]<<8|e[t+3]};function At(e,t){var r="",n,a,i=[],s,f,o,l;switch(t){case"dbcs":if(l=this.l,ve&&Buffer.isBuffer(this))r=this.slice(this.l,this.l+2*e).toString("utf16le");else for(o=0;o<e;++o)r+=String.fromCharCode(Et(this,l)),l+=2;e*=2;break;case"utf8":r=Bt(this,this.l,this.l+e);break;case"utf16le":e*=2,r=Kn(this,this.l,this.l+e);break;case"wstr":return At.call(this,e,"dbcs");case"lpstr-ansi":r=Ia(this,this.l),e=4+sr(this,this.l);break;case"lpstr-cp":r=La(this,this.l),e=4+sr(this,this.l);break;case"lpwstr":r=Ma(this,this.l),e=4+2*sr(this,this.l);break;case"lpp4":e=4+sr(this,this.l),r=ba(this,this.l),e&2&&(e+=2);break;case"8lpp4":e=4+sr(this,this.l),r=Ha(this,this.l),e&3&&(e+=4-(e&3));break;case"cstr":for(e=0,r="";(s=st(this,this.l+e++))!==0;)i.push($t(s));r=i.join("");break;case"_wstr":for(e=0,r="";(s=Et(this,this.l+e))!==0;)i.push($t(s)),e+=2;e+=2,r=i.join("");break;case"dbcs-cont":for(r="",l=this.l,o=0;o<e;++o){if(this.lens&&this.lens.indexOf(l)!==-1)return s=st(this,l),this.l=l+1,f=At.call(this,e-o,s?"dbcs-cont":"sbcs-cont"),i.join("")+f;i.push($t(Et(this,l))),l+=2}r=i.join(""),e*=2;break;case"cpstr":case"sbcs-cont":for(r="",l=this.l,o=0;o!=e;++o){if(this.lens&&this.lens.indexOf(l)!==-1)return s=st(this,l),this.l=l+1,f=At.call(this,e-o,s?"dbcs-cont":"sbcs-cont"),i.join("")+f;i.push($t(st(this,l))),l+=1}r=i.join("");break;default:switch(e){case 1:return n=st(this,this.l),this.l++,n;case 2:return n=(t==="i"?Tf:Et)(this,this.l),this.l+=2,n;case 4:case-4:return t==="i"||(this[this.l+3]&128)===0?(n=(e>0?$r:Ef)(this,this.l),this.l+=4,n):(a=sr(this,this.l),this.l+=4,a);case 8:case-8:if(t==="f")return e==8?a=an(this,this.l):a=an([this[this.l+7],this[this.l+6],this[this.l+5],this[this.l+4],this[this.l+3],this[this.l+2],this[this.l+1],this[this.l+0]],0),this.l+=8,a;e=8;case 16:r=Na(this,this.l,e);break}}return this.l+=e,r}var wf=function(e,t,r){e[r]=t&255,e[r+1]=t>>>8&255,e[r+2]=t>>>16&255,e[r+3]=t>>>24&255},Sf=function(e,t,r){e[r]=t&255,e[r+1]=t>>8&255,e[r+2]=t>>16&255,e[r+3]=t>>24&255},Af=function(e,t,r){e[r]=t&255,e[r+1]=t>>>8&255};function Ff(e,t,r){var n=0,a=0;if(r==="dbcs"){for(a=0;a!=t.length;++a)Af(this,t.charCodeAt(a),this.l+2*a);n=2*t.length}else if(r==="sbcs"){for(t=t.replace(/[^\x00-\x7F]/g,"_"),a=0;a!=t.length;++a)this[this.l+a]=t.charCodeAt(a)&255;n=t.length}else if(r==="hex"){for(;a<e;++a)this[this.l++]=parseInt(t.slice(2*a,2*a+2),16)||0;return this}else if(r==="utf16le"){var i=Math.min(this.l+e,this.length);for(a=0;a<Math.min(t.length,e);++a){var s=t.charCodeAt(a);this[this.l++]=s&255,this[this.l++]=s>>8}for(;this.l<i;)this[this.l++]=0;return this}else switch(e){case 1:n=1,this[this.l]=t&255;break;case 2:n=2,this[this.l]=t&255,t>>>=8,this[this.l+1]=t&255;break;case 3:n=3,this[this.l]=t&255,t>>>=8,this[this.l+1]=t&255,t>>>=8,this[this.l+2]=t&255;break;case 4:n=4,wf(this,t,this.l);break;case 8:if(n=8,r==="f"){_f(this,t,this.l);break}case 16:break;case-4:n=4,Sf(this,t,this.l);break}return this.l+=n,this}function Ga(e,t){var r=Na(this,this.l,e.length>>1);if(r!==e)throw new Error(t+"Expected "+e+" saw "+r);this.l+=e.length>>1}function ar(e,t){e.l=t,e.read_shift=At,e.chk=Ga,e.write_shift=Ff}function Sr(e,t){e.l+=t}function b(e){var t=Kr(e);return ar(t,0),t}function er(){var e=[],t=ve?256:2048,r=function(l){var c=b(l);return ar(c,0),c},n=r(t),a=function(){!n||(n.length>n.l&&(n=n.slice(0,n.l),n.l=n.length),n.length>0&&e.push(n),n=null)},i=function(l){return n&&l<n.length-n.l?n:(a(),n=r(Math.max(l+1,t)))},s=function(){return a(),He(e)},f=function(l){a(),n=l,n.l==null&&(n.l=n.length),i(t)};return{next:i,push:f,end:s,_bufs:e}}function G(e,t,r,n){var a=+t,i;if(!isNaN(a)){n||(n=_u[a].p||(r||[]).length||0),i=1+(a>=128?1:0)+1,n>=128&&++i,n>=16384&&++i,n>=2097152&&++i;var s=e.next(i);a<=127?s.write_shift(1,a):(s.write_shift(1,(a&127)+128),s.write_shift(1,a>>7));for(var f=0;f!=4;++f)if(n>=128)s.write_shift(1,(n&127)+128),n>>=7;else{s.write_shift(1,n);break}n>0&&jn(r)&&e.push(r)}}function Ft(e,t,r){var n=tr(e);if(t.s?(n.cRel&&(n.c+=t.s.c),n.rRel&&(n.r+=t.s.r)):(n.cRel&&(n.c+=t.c),n.rRel&&(n.r+=t.r)),!r||r.biff<12){for(;n.c>=256;)n.c-=256;for(;n.r>=65536;)n.r-=65536}return n}function G0(e,t,r){var n=tr(e);return n.s=Ft(n.s,t.s,r),n.e=Ft(n.e,t.s,r),n}function yt(e,t){if(e.cRel&&e.c<0)for(e=tr(e);e.c<0;)e.c+=t>8?16384:256;if(e.rRel&&e.r<0)for(e=tr(e);e.r<0;)e.r+=t>8?1048576:t>5?65536:16384;var r=Ee(e);return!e.cRel&&e.cRel!=null&&(r=Of(r)),!e.rRel&&e.rRel!=null&&(r=yf(r)),r}function Nn(e,t){return e.s.r==0&&!e.s.rRel&&e.e.r==(t.biff>=12?1048575:t.biff>=8?65536:16384)&&!e.e.rRel?(e.s.cRel?"":"$")+ze(e.s.c)+":"+(e.e.cRel?"":"$")+ze(e.e.c):e.s.c==0&&!e.s.cRel&&e.e.c==(t.biff>=12?16383:255)&&!e.e.cRel?(e.s.rRel?"":"$")+Ge(e.s.r)+":"+(e.e.rRel?"":"$")+Ge(e.e.r):yt(e.s,t.biff)+":"+yt(e.e,t.biff)}function Yn(e){return parseInt(Cf(e),10)-1}function Ge(e){return""+(e+1)}function yf(e){return e.replace(/([A-Z]|^)(\d+)$/,"$1$$$2")}function Cf(e){return e.replace(/\$(\d+)$/,"$1")}function Jn(e){for(var t=Df(e),r=0,n=0;n!==t.length;++n)r=26*r+t.charCodeAt(n)-64;return r-1}function ze(e){if(e<0)throw new Error("invalid column "+e);var t="";for(++e;e;e=Math.floor((e-1)/26))t=String.fromCharCode((e-1)%26+65)+t;return t}function Of(e){return e.replace(/^([A-Z])/,"$$$1")}function Df(e){return e.replace(/^\$([A-Z])/,"$1")}function Rf(e){return e.replace(/(\$?[A-Z]*)(\$?\d*)/,"$1,$2").split(",")}function Be(e){for(var t=0,r=0,n=0;n<e.length;++n){var a=e.charCodeAt(n);a>=48&&a<=57?t=10*t+(a-48):a>=65&&a<=90&&(r=26*r+(a-64))}return{c:r-1,r:t-1}}function Ee(e){for(var t=e.c+1,r="";t;t=(t-1)/26|0)r=String.fromCharCode((t-1)%26+65)+r;return r+(e.r+1)}function fr(e){var t=e.indexOf(":");return t==-1?{s:Be(e),e:Be(e)}:{s:Be(e.slice(0,t)),e:Be(e.slice(t+1))}}function ke(e,t){return typeof t>"u"||typeof t=="number"?ke(e.s,e.e):(typeof e!="string"&&(e=Ee(e)),typeof t!="string"&&(t=Ee(t)),e==t?e:e+":"+t)}function Ae(e){var t={s:{c:0,r:0},e:{c:0,r:0}},r=0,n=0,a=0,i=e.length;for(r=0;n<i&&!((a=e.charCodeAt(n)-64)<1||a>26);++n)r=26*r+a;for(t.s.c=--r,r=0;n<i&&!((a=e.charCodeAt(n)-48)<0||a>9);++n)r=10*r+a;if(t.s.r=--r,n===i||a!=10)return t.e.c=t.s.c,t.e.r=t.s.r,t;for(++n,r=0;n!=i&&!((a=e.charCodeAt(n)-64)<1||a>26);++n)r=26*r+a;for(t.e.c=--r,r=0;n!=i&&!((a=e.charCodeAt(n)-48)<0||a>9);++n)r=10*r+a;return t.e.r=--r,t}function X0(e,t){var r=e.t=="d"&&t instanceof Date;if(e.z!=null)try{return e.w=Mr(e.z,r?rr(t):t)}catch{}try{return e.w=Mr((e.XF||{}).numFmtId||(r?14:0),r?rr(t):t)}catch{return""+t}}function kr(e,t,r){return e==null||e.t==null||e.t=="z"?"":e.w!==void 0?e.w:(e.t=="d"&&!e.z&&r&&r.dateNF&&(e.z=r.dateNF),e.t=="e"?Mt[e.v]||e.v:t==null?X0(e,e.v):X0(e,t))}function Jr(e,t){var r=t&&t.sheet?t.sheet:"Sheet1",n={};return n[r]=e,{SheetNames:[r],Sheets:n}}function Xa(e,t,r){var n=r||{},a=e?Array.isArray(e):n.dense,i=e||(a?[]:{}),s=0,f=0;if(i&&n.origin!=null){if(typeof n.origin=="number")s=n.origin;else{var o=typeof n.origin=="string"?Be(n.origin):n.origin;s=o.r,f=o.c}i["!ref"]||(i["!ref"]="A1:A1")}var l={s:{c:1e7,r:1e7},e:{c:0,r:0}};if(i["!ref"]){var c=Ae(i["!ref"]);l.s.c=c.s.c,l.s.r=c.s.r,l.e.c=Math.max(l.e.c,c.e.c),l.e.r=Math.max(l.e.r,c.e.r),s==-1&&(l.e.r=s=c.e.r+1)}for(var v=0;v!=t.length;++v)if(!!t[v]){if(!Array.isArray(t[v]))throw new Error("aoa_to_sheet expects an array of arrays");for(var x=0;x!=t[v].length;++x)if(!(typeof t[v][x]>"u")){var d={v:t[v][x]},T=s+v,u=f+x;if(l.s.r>T&&(l.s.r=T),l.s.c>u&&(l.s.c=u),l.e.r<T&&(l.e.r=T),l.e.c<u&&(l.e.c=u),t[v][x]&&typeof t[v][x]=="object"&&!Array.isArray(t[v][x])&&!(t[v][x]instanceof Date))d=t[v][x];else if(Array.isArray(d.v)&&(d.f=t[v][x][1],d.v=d.v[0]),d.v===null)if(d.f)d.t="n";else if(n.nullError)d.t="e",d.v=0;else if(n.sheetStubs)d.t="z";else continue;else typeof d.v=="number"?d.t="n":typeof d.v=="boolean"?d.t="b":d.v instanceof Date?(d.z=n.dateNF||Re[14],n.cellDates?(d.t="d",d.w=Mr(d.z,rr(d.v))):(d.t="n",d.v=rr(d.v),d.w=Mr(d.z,d.v))):d.t="s";if(a)i[T]||(i[T]=[]),i[T][u]&&i[T][u].z&&(d.z=i[T][u].z),i[T][u]=d;else{var g=Ee({c:u,r:T});i[g]&&i[g].z&&(d.z=i[g].z),i[g]=d}}}return l.s.c<1e7&&(i["!ref"]=ke(l)),i}function dt(e,t){return Xa(null,e,t)}function Nf(e){return e.read_shift(4,"i")}function gr(e,t){return t||(t=b(4)),t.write_shift(4,e),t}function Ke(e){var t=e.read_shift(4);return t===0?"":e.read_shift(t,"dbcs")}function Me(e,t){var r=!1;return t==null&&(r=!0,t=b(4+2*e.length)),t.write_shift(4,e.length),e.length>0&&t.write_shift(0,e,"dbcs"),r?t.slice(0,t.l):t}function kf(e){return{ich:e.read_shift(2),ifnt:e.read_shift(2)}}function If(e,t){return t||(t=b(4)),t.write_shift(2,e.ich||0),t.write_shift(2,e.ifnt||0),t}function Zn(e,t){var r=e.l,n=e.read_shift(1),a=Ke(e),i=[],s={t:a,h:a};if((n&1)!==0){for(var f=e.read_shift(4),o=0;o!=f;++o)i.push(kf(e));s.r=i}else s.r=[{ich:0,ifnt:0}];return e.l=r+t,s}function Pf(e,t){var r=!1;return t==null&&(r=!0,t=b(15+4*e.t.length)),t.write_shift(1,0),Me(e.t,t),r?t.slice(0,t.l):t}var Lf=Zn;function Bf(e,t){var r=!1;return t==null&&(r=!0,t=b(23+4*e.t.length)),t.write_shift(1,1),Me(e.t,t),t.write_shift(4,1),If({ich:0,ifnt:0},t),r?t.slice(0,t.l):t}function ur(e){var t=e.read_shift(4),r=e.read_shift(2);return r+=e.read_shift(1)<<16,e.l++,{c:t,iStyleRef:r}}function Zr(e,t){return t==null&&(t=b(8)),t.write_shift(-4,e.c),t.write_shift(3,e.iStyleRef||e.s),t.write_shift(1,0),t}function qr(e){var t=e.read_shift(2);return t+=e.read_shift(1)<<16,e.l++,{c:-1,iStyleRef:t}}function Qr(e,t){return t==null&&(t=b(4)),t.write_shift(3,e.iStyleRef||e.s),t.write_shift(1,0),t}var Mf=Ke,$a=Me;function qn(e){var t=e.read_shift(4);return t===0||t===4294967295?"":e.read_shift(t,"dbcs")}function sn(e,t){var r=!1;return t==null&&(r=!0,t=b(127)),t.write_shift(4,e.length>0?e.length:4294967295),e.length>0&&t.write_shift(0,e,"dbcs"),r?t.slice(0,t.l):t}var Uf=Ke,bn=qn,Qn=sn;function za(e){var t=e.slice(e.l,e.l+4),r=t[0]&1,n=t[0]&2;e.l+=4;var a=n===0?an([0,0,0,0,t[0]&252,t[1],t[2],t[3]],0):$r(t,0)>>2;return r?a/100:a}function Ka(e,t){t==null&&(t=b(4));var r=0,n=0,a=e*100;if(e==(e|0)&&e>=-(1<<29)&&e<1<<29?n=1:a==(a|0)&&a>=-(1<<29)&&a<1<<29&&(n=1,r=1),n)t.write_shift(-4,((r?a:e)<<2)+(r+2));else throw new Error("unsupported RkNumber "+e)}function ja(e){var t={s:{},e:{}};return t.s.r=e.read_shift(4),t.e.r=e.read_shift(4),t.s.c=e.read_shift(4),t.e.c=e.read_shift(4),t}function bf(e,t){return t||(t=b(16)),t.write_shift(4,e.s.r),t.write_shift(4,e.e.r),t.write_shift(4,e.s.c),t.write_shift(4,e.e.c),t}var et=ja,vt=bf;function pt(e){if(e.length-e.l<8)throw"XLS Xnum Buffer underflow";return e.read_shift(8,"f")}function jr(e,t){return(t||b(8)).write_shift(8,e,"f")}function Wf(e){var t={},r=e.read_shift(1),n=r>>>1,a=e.read_shift(1),i=e.read_shift(2,"i"),s=e.read_shift(1),f=e.read_shift(1),o=e.read_shift(1);switch(e.l++,n){case 0:t.auto=1;break;case 1:t.index=a;var l=Yf[a];l&&(t.rgb=ra(l));break;case 2:t.rgb=ra([s,f,o]);break;case 3:t.theme=a;break}return i!=0&&(t.tint=i>0?i/32767:i/32768),t}function fn(e,t){if(t||(t=b(8)),!e||e.auto)return t.write_shift(4,0),t.write_shift(4,0),t;e.index!=null?(t.write_shift(1,2),t.write_shift(1,e.index)):e.theme!=null?(t.write_shift(1,6),t.write_shift(1,e.theme)):(t.write_shift(1,5),t.write_shift(1,0));var r=e.tint||0;if(r>0?r*=32767:r<0&&(r*=32768),t.write_shift(2,r),!e.rgb||e.theme!=null)t.write_shift(2,0),t.write_shift(1,0),t.write_shift(1,0);else{var n=e.rgb||"FFFFFF";typeof n=="number"&&(n=("000000"+n.toString(16)).slice(-6)),t.write_shift(1,parseInt(n.slice(0,2),16)),t.write_shift(1,parseInt(n.slice(2,4),16)),t.write_shift(1,parseInt(n.slice(4,6),16)),t.write_shift(1,255)}return t}function Hf(e){var t=e.read_shift(1);e.l++;var r={fBold:t&1,fItalic:t&2,fUnderline:t&4,fStrikeout:t&8,fOutline:t&16,fShadow:t&32,fCondense:t&64,fExtend:t&128};return r}function Vf(e,t){t||(t=b(2));var r=(e.italic?2:0)|(e.strike?8:0)|(e.outline?16:0)|(e.shadow?32:0)|(e.condense?64:0)|(e.extend?128:0);return t.write_shift(1,r),t.write_shift(1,0),t}var Ya=2,nr=3,Yt=11,ln=19,Jt=64,Gf=65,Xf=71,$f=4108,zf=4126,We=80,$0={1:{n:"CodePage",t:Ya},2:{n:"Category",t:We},3:{n:"PresentationFormat",t:We},4:{n:"ByteCount",t:nr},5:{n:"LineCount",t:nr},6:{n:"ParagraphCount",t:nr},7:{n:"SlideCount",t:nr},8:{n:"NoteCount",t:nr},9:{n:"HiddenCount",t:nr},10:{n:"MultimediaClipCount",t:nr},11:{n:"ScaleCrop",t:Yt},12:{n:"HeadingPairs",t:$f},13:{n:"TitlesOfParts",t:zf},14:{n:"Manager",t:We},15:{n:"Company",t:We},16:{n:"LinksUpToDate",t:Yt},17:{n:"CharacterCount",t:nr},19:{n:"SharedDoc",t:Yt},22:{n:"HyperlinksChanged",t:Yt},23:{n:"AppVersion",t:nr,p:"version"},24:{n:"DigSig",t:Gf},26:{n:"ContentType",t:We},27:{n:"ContentStatus",t:We},28:{n:"Language",t:We},29:{n:"Version",t:We},255:{},2147483648:{n:"Locale",t:ln},2147483651:{n:"Behavior",t:ln},1919054434:{}},z0={1:{n:"CodePage",t:Ya},2:{n:"Title",t:We},3:{n:"Subject",t:We},4:{n:"Author",t:We},5:{n:"Keywords",t:We},6:{n:"Comments",t:We},7:{n:"Template",t:We},8:{n:"LastAuthor",t:We},9:{n:"RevNumber",t:We},10:{n:"EditTime",t:Jt},11:{n:"LastPrinted",t:Jt},12:{n:"CreatedDate",t:Jt},13:{n:"ModifiedDate",t:Jt},14:{n:"PageCount",t:nr},15:{n:"WordCount",t:nr},16:{n:"CharCount",t:nr},17:{n:"Thumbnail",t:Xf},18:{n:"Application",t:We},19:{n:"DocSecurity",t:nr},255:{},2147483648:{n:"Locale",t:ln},2147483651:{n:"Behavior",t:ln},1919054434:{}};function Kf(e){return e.map(function(t){return[t>>16&255,t>>8&255,t&255]})}var jf=Kf([0,16777215,16711680,65280,255,16776960,16711935,65535,0,16777215,16711680,65280,255,16776960,16711935,65535,8388608,32768,128,8421376,8388736,32896,12632256,8421504,10066431,10040166,16777164,13434879,6684774,16744576,26316,13421823,128,16711935,16776960,65535,8388736,8388608,32896,255,52479,13434879,13434828,16777113,10079487,16751052,13408767,16764057,3368703,3394764,10079232,16763904,16750848,16737792,6710937,9868950,13158,3381606,13056,3355392,10040064,10040166,3355545,3355443,16777215,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]),Yf=tr(jf),Mt={0:"#NULL!",7:"#DIV/0!",15:"#VALUE!",23:"#REF!",29:"#NAME?",36:"#NUM!",42:"#N/A",43:"#GETTING_DATA",255:"#WTF?"},Jf={"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml":"workbooks","application/vnd.ms-excel.sheet.macroEnabled.main+xml":"workbooks","application/vnd.ms-excel.sheet.binary.macroEnabled.main":"workbooks","application/vnd.ms-excel.addin.macroEnabled.main+xml":"workbooks","application/vnd.openxmlformats-officedocument.spreadsheetml.template.main+xml":"workbooks","application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml":"sheets","application/vnd.ms-excel.worksheet":"sheets","application/vnd.ms-excel.binIndexWs":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.chartsheet+xml":"charts","application/vnd.ms-excel.chartsheet":"charts","application/vnd.ms-excel.macrosheet+xml":"macros","application/vnd.ms-excel.macrosheet":"macros","application/vnd.ms-excel.intlmacrosheet":"TODO","application/vnd.ms-excel.binIndexMs":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.dialogsheet+xml":"dialogs","application/vnd.ms-excel.dialogsheet":"dialogs","application/vnd.openxmlformats-officedocument.spreadsheetml.sharedStrings+xml":"strs","application/vnd.ms-excel.sharedStrings":"strs","application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml":"styles","application/vnd.ms-excel.styles":"styles","application/vnd.openxmlformats-package.core-properties+xml":"coreprops","application/vnd.openxmlformats-officedocument.custom-properties+xml":"custprops","application/vnd.openxmlformats-officedocument.extended-properties+xml":"extprops","application/vnd.openxmlformats-officedocument.customXmlProperties+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.customProperty":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml":"comments","application/vnd.ms-excel.comments":"comments","application/vnd.ms-excel.threadedcomments+xml":"threadedcomments","application/vnd.ms-excel.person+xml":"people","application/vnd.openxmlformats-officedocument.spreadsheetml.sheetMetadata+xml":"metadata","application/vnd.ms-excel.sheetMetadata":"metadata","application/vnd.ms-excel.pivotTable":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotTable+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.chart+xml":"TODO","application/vnd.ms-office.chartcolorstyle+xml":"TODO","application/vnd.ms-office.chartstyle+xml":"TODO","application/vnd.ms-office.chartex+xml":"TODO","application/vnd.ms-excel.calcChain":"calcchains","application/vnd.openxmlformats-officedocument.spreadsheetml.calcChain+xml":"calcchains","application/vnd.openxmlformats-officedocument.spreadsheetml.printerSettings":"TODO","application/vnd.ms-office.activeX":"TODO","application/vnd.ms-office.activeX+xml":"TODO","application/vnd.ms-excel.attachedToolbars":"TODO","application/vnd.ms-excel.connections":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.connections+xml":"TODO","application/vnd.ms-excel.externalLink":"links","application/vnd.openxmlformats-officedocument.spreadsheetml.externalLink+xml":"links","application/vnd.ms-excel.pivotCacheDefinition":"TODO","application/vnd.ms-excel.pivotCacheRecords":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotCacheDefinition+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotCacheRecords+xml":"TODO","application/vnd.ms-excel.queryTable":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.queryTable+xml":"TODO","application/vnd.ms-excel.userNames":"TODO","application/vnd.ms-excel.revisionHeaders":"TODO","application/vnd.ms-excel.revisionLog":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.revisionHeaders+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.revisionLog+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.userNames+xml":"TODO","application/vnd.ms-excel.tableSingleCells":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.tableSingleCells+xml":"TODO","application/vnd.ms-excel.slicer":"TODO","application/vnd.ms-excel.slicerCache":"TODO","application/vnd.ms-excel.slicer+xml":"TODO","application/vnd.ms-excel.slicerCache+xml":"TODO","application/vnd.ms-excel.wsSortMap":"TODO","application/vnd.ms-excel.table":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.table+xml":"TODO","application/vnd.openxmlformats-officedocument.theme+xml":"themes","application/vnd.openxmlformats-officedocument.themeOverride+xml":"TODO","application/vnd.ms-excel.Timeline+xml":"TODO","application/vnd.ms-excel.TimelineCache+xml":"TODO","application/vnd.ms-office.vbaProject":"vba","application/vnd.ms-office.vbaProjectSignature":"TODO","application/vnd.ms-office.volatileDependencies":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.volatileDependencies+xml":"TODO","application/vnd.ms-excel.controlproperties+xml":"TODO","application/vnd.openxmlformats-officedocument.model+data":"TODO","application/vnd.ms-excel.Survey+xml":"TODO","application/vnd.openxmlformats-officedocument.drawing+xml":"drawings","application/vnd.openxmlformats-officedocument.drawingml.chartshapes+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramColors+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramData+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramLayout+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramStyle+xml":"TODO","application/vnd.openxmlformats-officedocument.vmlDrawing":"TODO","application/vnd.openxmlformats-package.relationships+xml":"rels","application/vnd.openxmlformats-officedocument.oleObject":"TODO","image/png":"TODO",sheet:"js"},Zt={workbooks:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml",xlsm:"application/vnd.ms-excel.sheet.macroEnabled.main+xml",xlsb:"application/vnd.ms-excel.sheet.binary.macroEnabled.main",xlam:"application/vnd.ms-excel.addin.macroEnabled.main+xml",xltx:"application/vnd.openxmlformats-officedocument.spreadsheetml.template.main+xml"},strs:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sharedStrings+xml",xlsb:"application/vnd.ms-excel.sharedStrings"},comments:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml",xlsb:"application/vnd.ms-excel.comments"},sheets:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml",xlsb:"application/vnd.ms-excel.worksheet"},charts:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.chartsheet+xml",xlsb:"application/vnd.ms-excel.chartsheet"},dialogs:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.dialogsheet+xml",xlsb:"application/vnd.ms-excel.dialogsheet"},macros:{xlsx:"application/vnd.ms-excel.macrosheet+xml",xlsb:"application/vnd.ms-excel.macrosheet"},metadata:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheetMetadata+xml",xlsb:"application/vnd.ms-excel.sheetMetadata"},styles:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml",xlsb:"application/vnd.ms-excel.styles"}};function Ja(){return{workbooks:[],sheets:[],charts:[],dialogs:[],macros:[],rels:[],strs:[],comments:[],threadedcomments:[],links:[],coreprops:[],extprops:[],custprops:[],themes:[],styles:[],calcchains:[],vba:[],drawings:[],metadata:[],people:[],TODO:[],xmlns:""}}function Za(e,t){var r=sf(Jf),n=[],a;n[n.length]=Ie,n[n.length]=J("Types",null,{xmlns:Le.CT,"xmlns:xsd":Le.xsd,"xmlns:xsi":Le.xsi}),n=n.concat([["xml","application/xml"],["bin","application/vnd.ms-excel.sheet.binary.macroEnabled.main"],["vml","application/vnd.openxmlformats-officedocument.vmlDrawing"],["data","application/vnd.openxmlformats-officedocument.model+data"],["bmp","image/bmp"],["png","image/png"],["gif","image/gif"],["emf","image/x-emf"],["wmf","image/x-wmf"],["jpg","image/jpeg"],["jpeg","image/jpeg"],["tif","image/tiff"],["tiff","image/tiff"],["pdf","application/pdf"],["rels","application/vnd.openxmlformats-package.relationships+xml"]].map(function(o){return J("Default",null,{Extension:o[0],ContentType:o[1]})}));var i=function(o){e[o]&&e[o].length>0&&(a=e[o][0],n[n.length]=J("Override",null,{PartName:(a[0]=="/"?"":"/")+a,ContentType:Zt[o][t.bookType]||Zt[o].xlsx}))},s=function(o){(e[o]||[]).forEach(function(l){n[n.length]=J("Override",null,{PartName:(l[0]=="/"?"":"/")+l,ContentType:Zt[o][t.bookType]||Zt[o].xlsx})})},f=function(o){(e[o]||[]).forEach(function(l){n[n.length]=J("Override",null,{PartName:(l[0]=="/"?"":"/")+l,ContentType:r[o][0]})})};return i("workbooks"),s("sheets"),s("charts"),f("themes"),["strs","styles"].forEach(i),["coreprops","extprops","custprops"].forEach(f),f("vba"),f("comments"),f("threadedcomments"),f("drawings"),s("metadata"),f("people"),n.length>2&&(n[n.length]="</Types>",n[1]=n[1].replace("/>",">")),n.join("")}var de={WB:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument",SHEET:"http://sheetjs.openxmlformats.org/officeDocument/2006/relationships/officeDocument",HLINK:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/hyperlink",VML:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/vmlDrawing",XPATH:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/externalLinkPath",XMISS:"http://schemas.microsoft.com/office/2006/relationships/xlExternalLinkPath/xlPathMissing",XLINK:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/externalLink",CXML:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/customXml",CXMLP:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/customXmlProps",CMNT:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/comments",CORE_PROPS:"http://schemas.openxmlformats.org/package/2006/relationships/metadata/core-properties",EXT_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/extended-properties",CUST_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/custom-properties",SST:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/sharedStrings",STY:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/styles",THEME:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/theme",CHART:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/chart",CHARTEX:"http://schemas.microsoft.com/office/2014/relationships/chartEx",CS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/chartsheet",WS:["http://schemas.openxmlformats.org/officeDocument/2006/relationships/worksheet","http://purl.oclc.org/ooxml/officeDocument/relationships/worksheet"],DS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/dialogsheet",MS:"http://schemas.microsoft.com/office/2006/relationships/xlMacrosheet",IMG:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/image",DRAW:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/drawing",XLMETA:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/sheetMetadata",TCMNT:"http://schemas.microsoft.com/office/2017/10/relationships/threadedComment",PEOPLE:"http://schemas.microsoft.com/office/2017/10/relationships/person",VBA:"http://schemas.microsoft.com/office/2006/relationships/vbaProject"};function qa(e){var t=e.lastIndexOf("/");return e.slice(0,t+1)+"_rels/"+e.slice(t+1)+".rels"}function ot(e){var t=[Ie,J("Relationships",null,{xmlns:Le.RELS})];return Xe(e["!id"]).forEach(function(r){t[t.length]=J("Relationship",null,e["!id"][r])}),t.length>2&&(t[t.length]="</Relationships>",t[1]=t[1].replace("/>",">")),t.join("")}function _e(e,t,r,n,a,i){if(a||(a={}),e["!id"]||(e["!id"]={}),e["!idx"]||(e["!idx"]=1),t<0)for(t=e["!idx"];e["!id"]["rId"+t];++t);if(e["!idx"]=t+1,a.Id="rId"+t,a.Type=n,a.Target=r,i?a.TargetMode=i:[de.HLINK,de.XPATH,de.XMISS].indexOf(a.Type)>-1&&(a.TargetMode="External"),e["!id"][a.Id])throw new Error("Cannot rewrite rId "+t);return e["!id"][a.Id]=a,e[("/"+a.Target).replace("//","/")]=a,t}function Zf(e){var t=[Ie];t.push(`<manifest:manifest xmlns:manifest="urn:oasis:names:tc:opendocument:xmlns:manifest:1.0" manifest:version="1.2">
`),t.push(`  <manifest:file-entry manifest:full-path="/" manifest:version="1.2" manifest:media-type="application/vnd.oasis.opendocument.spreadsheet"/>
`);for(var r=0;r<e.length;++r)t.push('  <manifest:file-entry manifest:full-path="'+e[r][0]+'" manifest:media-type="'+e[r][1]+`"/>
`);return t.push("</manifest:manifest>"),t.join("")}function K0(e,t,r){return['  <rdf:Description rdf:about="'+e+`">
`,'    <rdf:type rdf:resource="http://docs.oasis-open.org/ns/office/1.2/meta/'+(r||"odf")+"#"+t+`"/>
`,`  </rdf:Description>
`].join("")}function qf(e,t){return['  <rdf:Description rdf:about="'+e+`">
`,'    <ns0:hasPart xmlns:ns0="http://docs.oasis-open.org/ns/office/1.2/meta/pkg#" rdf:resource="'+t+`"/>
`,`  </rdf:Description>
`].join("")}function Qf(e){var t=[Ie];t.push(`<rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
`);for(var r=0;r!=e.length;++r)t.push(K0(e[r][0],e[r][1])),t.push(qf("",e[r][0]));return t.push(K0("","Document","pkg")),t.push("</rdf:RDF>"),t.join("")}function Qa(){return'<office:document-meta xmlns:office="urn:oasis:names:tc:opendocument:xmlns:office:1.0" xmlns:meta="urn:oasis:names:tc:opendocument:xmlns:meta:1.0" xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:xlink="http://www.w3.org/1999/xlink" office:version="1.2"><office:meta><meta:generator>SheetJS '+Qt.version+"</meta:generator></office:meta></office:document-meta>"}var zr=[["cp:category","Category"],["cp:contentStatus","ContentStatus"],["cp:keywords","Keywords"],["cp:lastModifiedBy","LastAuthor"],["cp:lastPrinted","LastPrinted"],["cp:revision","RevNumber"],["cp:version","Version"],["dc:creator","Author"],["dc:description","Comments"],["dc:identifier","Identifier"],["dc:language","Language"],["dc:subject","Subject"],["dc:title","Title"],["dcterms:created","CreatedDate","date"],["dcterms:modified","ModifiedDate","date"]];function kn(e,t,r,n,a){a[e]!=null||t==null||t===""||(a[e]=t,t=Te(t),n[n.length]=r?J(e,t,r):Ve(e,t))}function ei(e,t){var r=t||{},n=[Ie,J("cp:coreProperties",null,{"xmlns:cp":Le.CORE_PROPS,"xmlns:dc":Le.dc,"xmlns:dcterms":Le.dcterms,"xmlns:dcmitype":Le.dcmitype,"xmlns:xsi":Le.xsi})],a={};if(!e&&!r.Props)return n.join("");e&&(e.CreatedDate!=null&&kn("dcterms:created",typeof e.CreatedDate=="string"?e.CreatedDate:Un(e.CreatedDate,r.WTF),{"xsi:type":"dcterms:W3CDTF"},n,a),e.ModifiedDate!=null&&kn("dcterms:modified",typeof e.ModifiedDate=="string"?e.ModifiedDate:Un(e.ModifiedDate,r.WTF),{"xsi:type":"dcterms:W3CDTF"},n,a));for(var i=0;i!=zr.length;++i){var s=zr[i],f=r.Props&&r.Props[s[1]]!=null?r.Props[s[1]]:e?e[s[1]]:null;f===!0?f="1":f===!1?f="0":typeof f=="number"&&(f=String(f)),f!=null&&kn(s[0],f,null,n,a)}return n.length>2&&(n[n.length]="</cp:coreProperties>",n[1]=n[1].replace("/>",">")),n.join("")}var ct=[["Application","Application","string"],["AppVersion","AppVersion","string"],["Company","Company","string"],["DocSecurity","DocSecurity","string"],["Manager","Manager","string"],["HyperlinksChanged","HyperlinksChanged","bool"],["SharedDoc","SharedDoc","bool"],["LinksUpToDate","LinksUpToDate","bool"],["ScaleCrop","ScaleCrop","bool"],["HeadingPairs","HeadingPairs","raw"],["TitlesOfParts","TitlesOfParts","raw"]],ri=["Worksheets","SheetNames","NamedRanges","DefinedNames","Chartsheets","ChartNames"];function ti(e){var t=[],r=J;return e||(e={}),e.Application="SheetJS",t[t.length]=Ie,t[t.length]=J("Properties",null,{xmlns:Le.EXT_PROPS,"xmlns:vt":Le.vt}),ct.forEach(function(n){if(e[n[1]]!==void 0){var a;switch(n[2]){case"string":a=Te(String(e[n[1]]));break;case"bool":a=e[n[1]]?"true":"false";break}a!==void 0&&(t[t.length]=r(n[0],a))}}),t[t.length]=r("HeadingPairs",r("vt:vector",r("vt:variant","<vt:lpstr>Worksheets</vt:lpstr>")+r("vt:variant",r("vt:i4",String(e.Worksheets))),{size:2,baseType:"variant"})),t[t.length]=r("TitlesOfParts",r("vt:vector",e.SheetNames.map(function(n){return"<vt:lpstr>"+Te(n)+"</vt:lpstr>"}).join(""),{size:e.Worksheets,baseType:"lpstr"})),t.length>2&&(t[t.length]="</Properties>",t[1]=t[1].replace("/>",">")),t.join("")}function ni(e){var t=[Ie,J("Properties",null,{xmlns:Le.CUST_PROPS,"xmlns:vt":Le.vt})];if(!e)return t.join("");var r=1;return Xe(e).forEach(function(a){++r,t[t.length]=J("property",mf(e[a],!0),{fmtid:"{D5CDD505-2E9C-101B-9397-08002B2CF9AE}",pid:r,name:Te(a)})}),t.length>2&&(t[t.length]="</Properties>",t[1]=t[1].replace("/>",">")),t.join("")}var j0={Title:"Title",Subject:"Subject",Author:"Author",Keywords:"Keywords",Comments:"Description",LastAuthor:"LastAuthor",RevNumber:"Revision",Application:"AppName",LastPrinted:"LastPrinted",CreatedDate:"Created",ModifiedDate:"LastSaved",Category:"Category",Manager:"Manager",Company:"Company",AppVersion:"Version",ContentStatus:"ContentStatus",Identifier:"Identifier",Language:"Language"};function el(e,t){var r=[];return Xe(j0).map(function(n){for(var a=0;a<zr.length;++a)if(zr[a][1]==n)return zr[a];for(a=0;a<ct.length;++a)if(ct[a][1]==n)return ct[a];throw n}).forEach(function(n){if(e[n[1]]!=null){var a=t&&t.Props&&t.Props[n[1]]!=null?t.Props[n[1]]:e[n[1]];switch(n[2]){case"date":a=new Date(a).toISOString().replace(/\.\d*Z/,"Z");break}typeof a=="number"?a=String(a):a===!0||a===!1?a=a?"1":"0":a instanceof Date&&(a=new Date(a).toISOString().replace(/\.\d*Z/,"")),r.push(Ve(j0[n[1]]||n[1],a))}}),J("DocumentProperties",r.join(""),{xmlns:ir.o})}function rl(e,t){var r=["Worksheets","SheetNames"],n="CustomDocumentProperties",a=[];return e&&Xe(e).forEach(function(i){if(!!Object.prototype.hasOwnProperty.call(e,i)){for(var s=0;s<zr.length;++s)if(i==zr[s][1])return;for(s=0;s<ct.length;++s)if(i==ct[s][1])return;for(s=0;s<r.length;++s)if(i==r[s])return;var f=e[i],o="string";typeof f=="number"?(o="float",f=String(f)):f===!0||f===!1?(o="boolean",f=f?"1":"0"):f=String(f),a.push(J(L0(i),f,{"dt:dt":o}))}}),t&&Xe(t).forEach(function(i){if(!!Object.prototype.hasOwnProperty.call(t,i)&&!(e&&Object.prototype.hasOwnProperty.call(e,i))){var s=t[i],f="string";typeof s=="number"?(f="float",s=String(s)):s===!0||s===!1?(f="boolean",s=s?"1":"0"):s instanceof Date?(f="dateTime.tz",s=s.toISOString()):s=String(s),a.push(J(L0(i),s,{"dt:dt":f}))}}),"<"+n+' xmlns="'+ir.o+'">'+a.join("")+"</"+n+">"}function tl(e){var t=typeof e=="string"?new Date(Date.parse(e)):e,r=t.getTime()/1e3+11644473600,n=r%Math.pow(2,32),a=(r-n)/Math.pow(2,32);n*=1e7,a*=1e7;var i=n/Math.pow(2,32)|0;i>0&&(n=n%Math.pow(2,32),a+=i);var s=b(8);return s.write_shift(4,n),s.write_shift(4,a),s}function Y0(e,t){var r=b(4),n=b(4);switch(r.write_shift(4,e==80?31:e),e){case 3:n.write_shift(-4,t);break;case 5:n=b(8),n.write_shift(8,t,"f");break;case 11:n.write_shift(4,t?1:0);break;case 64:n=tl(t);break;case 31:case 80:for(n=b(4+2*(t.length+1)+(t.length%2?0:2)),n.write_shift(4,t.length+1),n.write_shift(0,t,"dbcs");n.l!=n.length;)n.write_shift(1,0);break;default:throw new Error("TypedPropertyValue unrecognized type "+e+" "+t)}return He([r,n])}var ai=["CodePage","Thumbnail","_PID_LINKBASE","_PID_HLINKS","SystemIdentifier","FMTID"];function nl(e){switch(typeof e){case"boolean":return 11;case"number":return(e|0)==e?3:5;case"string":return 31;case"object":if(e instanceof Date)return 64;break}return-1}function J0(e,t,r){var n=b(8),a=[],i=[],s=8,f=0,o=b(8),l=b(8);if(o.write_shift(4,2),o.write_shift(4,1200),l.write_shift(4,1),i.push(o),a.push(l),s+=8+o.length,!t){l=b(8),l.write_shift(4,0),a.unshift(l);var c=[b(4)];for(c[0].write_shift(4,e.length),f=0;f<e.length;++f){var v=e[f][0];for(o=b(4+4+2*(v.length+1)+(v.length%2?0:2)),o.write_shift(4,f+2),o.write_shift(4,v.length+1),o.write_shift(0,v,"dbcs");o.l!=o.length;)o.write_shift(1,0);c.push(o)}o=He(c),i.unshift(o),s+=8+o.length}for(f=0;f<e.length;++f)if(!(t&&!t[e[f][0]])&&!(ai.indexOf(e[f][0])>-1||ri.indexOf(e[f][0])>-1)&&e[f][1]!=null){var x=e[f][1],d=0;if(t){d=+t[e[f][0]];var T=r[d];if(T.p=="version"&&typeof x=="string"){var u=x.split(".");x=(+u[0]<<16)+(+u[1]||0)}o=Y0(T.t,x)}else{var g=nl(x);g==-1&&(g=31,x=String(x)),o=Y0(g,x)}i.push(o),l=b(8),l.write_shift(4,t?d:2+f),a.push(l),s+=8+o.length}var R=8*(i.length+1);for(f=0;f<i.length;++f)a[f].write_shift(4,R),R+=i[f].length;return n.write_shift(4,s),n.write_shift(4,i.length),He([n].concat(a).concat(i))}function Z0(e,t,r,n,a,i){var s=b(a?68:48),f=[s];s.write_shift(2,65534),s.write_shift(2,0),s.write_shift(4,842412599),s.write_shift(16,we.utils.consts.HEADER_CLSID,"hex"),s.write_shift(4,a?2:1),s.write_shift(16,t,"hex"),s.write_shift(4,a?68:48);var o=J0(e,r,n);if(f.push(o),a){var l=J0(a,null,null);s.write_shift(16,i,"hex"),s.write_shift(4,68+o.length),f.push(l)}return He(f)}function al(e,t){t||(t=b(e));for(var r=0;r<e;++r)t.write_shift(1,0);return t}function il(e,t){return e.read_shift(t)===1}function Ze(e,t){return t||(t=b(2)),t.write_shift(2,+!!e),t}function ii(e){return e.read_shift(2,"u")}function hr(e,t){return t||(t=b(2)),t.write_shift(2,e),t}function si(e,t,r){return r||(r=b(2)),r.write_shift(1,t=="e"?+e:+!!e),r.write_shift(1,t=="e"?1:0),r}function fi(e,t,r){var n=e.read_shift(r&&r.biff>=12?2:1),a="sbcs-cont";if(r&&r.biff>=8,!r||r.biff==8){var i=e.read_shift(1);i&&(a="dbcs-cont")}else r.biff==12&&(a="wstr");r.biff>=2&&r.biff<=5&&(a="cpstr");var s=n?e.read_shift(n,a):"";return s}function sl(e){var t=e.t||"",r=b(3+0);r.write_shift(2,t.length),r.write_shift(1,1);var n=b(2*t.length);n.write_shift(2*t.length,t,"utf16le");var a=[r,n];return He(a)}function fl(e,t,r){var n;if(r){if(r.biff>=2&&r.biff<=5)return e.read_shift(t,"cpstr");if(r.biff>=12)return e.read_shift(t,"dbcs-cont")}var a=e.read_shift(1);return a===0?n=e.read_shift(t,"sbcs-cont"):n=e.read_shift(t,"dbcs-cont"),n}function ll(e,t,r){var n=e.read_shift(r&&r.biff==2?1:2);return n===0?(e.l++,""):fl(e,n,r)}function ol(e,t,r){if(r.biff>5)return ll(e,t,r);var n=e.read_shift(1);return n===0?(e.l++,""):e.read_shift(n,r.biff<=4||!e.lens?"cpstr":"sbcs-cont")}function li(e,t,r){return r||(r=b(3+2*e.length)),r.write_shift(2,e.length),r.write_shift(1,1),r.write_shift(31,e,"utf16le"),r}function q0(e,t){t||(t=b(6+e.length*2)),t.write_shift(4,1+e.length);for(var r=0;r<e.length;++r)t.write_shift(2,e.charCodeAt(r));return t.write_shift(2,0),t}function cl(e){var t=b(512),r=0,n=e.Target;n.slice(0,7)=="file://"&&(n=n.slice(7));var a=n.indexOf("#"),i=a>-1?31:23;switch(n.charAt(0)){case"#":i=28;break;case".":i&=-3;break}t.write_shift(4,2),t.write_shift(4,i);var s=[8,6815827,6619237,4849780,83];for(r=0;r<s.length;++r)t.write_shift(4,s[r]);if(i==28)n=n.slice(1),q0(n,t);else if(i&2){for(s="e0 c9 ea 79 f9 ba ce 11 8c 82 00 aa 00 4b a9 0b".split(" "),r=0;r<s.length;++r)t.write_shift(1,parseInt(s[r],16));var f=a>-1?n.slice(0,a):n;for(t.write_shift(4,2*(f.length+1)),r=0;r<f.length;++r)t.write_shift(2,f.charCodeAt(r));t.write_shift(2,0),i&8&&q0(a>-1?n.slice(a+1):"",t)}else{for(s="03 03 00 00 00 00 00 00 c0 00 00 00 00 00 00 46".split(" "),r=0;r<s.length;++r)t.write_shift(1,parseInt(s[r],16));for(var o=0;n.slice(o*3,o*3+3)=="../"||n.slice(o*3,o*3+3)=="..\\";)++o;for(t.write_shift(2,o),t.write_shift(4,n.length-3*o+1),r=0;r<n.length-3*o;++r)t.write_shift(1,n.charCodeAt(r+3*o)&255);for(t.write_shift(1,0),t.write_shift(2,65535),t.write_shift(2,57005),r=0;r<6;++r)t.write_shift(4,0)}return t.slice(0,t.l)}function Yr(e,t,r,n){return n||(n=b(6)),n.write_shift(2,e),n.write_shift(2,t),n.write_shift(2,r||0),n}function hl(e,t,r){var n=r.biff>8?4:2,a=e.read_shift(n),i=e.read_shift(n,"i"),s=e.read_shift(n,"i");return[a,i,s]}function ul(e){var t=e.read_shift(2),r=e.read_shift(2),n=e.read_shift(2),a=e.read_shift(2);return{s:{c:n,r:t},e:{c:a,r}}}function oi(e,t){return t||(t=b(8)),t.write_shift(2,e.s.r),t.write_shift(2,e.e.r),t.write_shift(2,e.s.c),t.write_shift(2,e.e.c),t}function e0(e,t,r){var n=1536,a=16;switch(r.bookType){case"biff8":break;case"biff5":n=1280,a=8;break;case"biff4":n=4,a=6;break;case"biff3":n=3,a=6;break;case"biff2":n=2,a=4;break;case"xla":break;default:throw new Error("unsupported BIFF version")}var i=b(a);return i.write_shift(2,n),i.write_shift(2,t),a>4&&i.write_shift(2,29282),a>6&&i.write_shift(2,1997),a>8&&(i.write_shift(2,49161),i.write_shift(2,1),i.write_shift(2,1798),i.write_shift(2,0)),i}function xl(e,t){var r=!t||t.biff==8,n=b(r?112:54);for(n.write_shift(t.biff==8?2:1,7),r&&n.write_shift(1,0),n.write_shift(4,859007059),n.write_shift(4,5458548|(r?0:536870912));n.l<n.length;)n.write_shift(1,r?0:32);return n}function dl(e,t){var r=!t||t.biff>=8?2:1,n=b(8+r*e.name.length);n.write_shift(4,e.pos),n.write_shift(1,e.hs||0),n.write_shift(1,e.dt),n.write_shift(1,e.name.length),t.biff>=8&&n.write_shift(1,1),n.write_shift(r*e.name.length,e.name,t.biff<8?"sbcs":"utf16le");var a=n.slice(0,n.l);return a.l=n.l,a}function vl(e,t){var r=b(8);r.write_shift(4,e.Count),r.write_shift(4,e.Unique);for(var n=[],a=0;a<e.length;++a)n[a]=sl(e[a]);var i=He([r].concat(n));return i.parts=[r.length].concat(n.map(function(s){return s.length})),i}function pl(){var e=b(18);return e.write_shift(2,0),e.write_shift(2,0),e.write_shift(2,29280),e.write_shift(2,17600),e.write_shift(2,56),e.write_shift(2,0),e.write_shift(2,0),e.write_shift(2,1),e.write_shift(2,500),e}function ml(e){var t=b(18),r=1718;return e&&e.RTL&&(r|=64),t.write_shift(2,r),t.write_shift(4,0),t.write_shift(4,64),t.write_shift(4,0),t.write_shift(4,0),t}function gl(e,t){var r=e.name||"Arial",n=t&&t.biff==5,a=n?15+r.length:16+2*r.length,i=b(a);return i.write_shift(2,(e.sz||12)*20),i.write_shift(4,0),i.write_shift(2,400),i.write_shift(4,0),i.write_shift(2,0),i.write_shift(1,r.length),n||i.write_shift(1,1),i.write_shift((n?1:2)*r.length,r,n?"sbcs":"utf16le"),i}function _l(e,t,r,n){var a=b(10);return Yr(e,t,n,a),a.write_shift(4,r),a}function Tl(e,t,r,n,a){var i=!a||a.biff==8,s=b(6+2+ +i+(1+i)*r.length);return Yr(e,t,n,s),s.write_shift(2,r.length),i&&s.write_shift(1,1),s.write_shift((1+i)*r.length,r,i?"utf16le":"sbcs"),s}function El(e,t,r,n){var a=r&&r.biff==5;n||(n=b(a?3+t.length:5+2*t.length)),n.write_shift(2,e),n.write_shift(a?1:2,t.length),a||n.write_shift(1,1),n.write_shift((a?1:2)*t.length,t,a?"sbcs":"utf16le");var i=n.length>n.l?n.slice(0,n.l):n;return i.l==null&&(i.l=i.length),i}function wl(e,t){var r=t.biff==8||!t.biff?4:2,n=b(2*r+6);return n.write_shift(r,e.s.r),n.write_shift(r,e.e.r+1),n.write_shift(2,e.s.c),n.write_shift(2,e.e.c+1),n.write_shift(2,0),n}function Q0(e,t,r,n){var a=r&&r.biff==5;n||(n=b(a?16:20)),n.write_shift(2,0),e.style?(n.write_shift(2,e.numFmtId||0),n.write_shift(2,65524)):(n.write_shift(2,e.numFmtId||0),n.write_shift(2,t<<4));var i=0;return e.numFmtId>0&&a&&(i|=1024),n.write_shift(4,i),n.write_shift(4,0),a||n.write_shift(4,0),n.write_shift(2,0),n}function Sl(e){var t=b(8);return t.write_shift(4,0),t.write_shift(2,e[0]?e[0]+1:0),t.write_shift(2,e[1]?e[1]+1:0),t}function Al(e,t,r,n,a,i){var s=b(8);return Yr(e,t,n,s),si(r,i,s),s}function Fl(e,t,r,n){var a=b(14);return Yr(e,t,n,a),jr(r,a),a}function yl(e,t,r){if(r.biff<8)return Cl(e,t,r);for(var n=[],a=e.l+t,i=e.read_shift(r.biff>8?4:2);i--!==0;)n.push(hl(e,r.biff>8?12:6,r));if(e.l!=a)throw new Error("Bad ExternSheet: "+e.l+" != "+a);return n}function Cl(e,t,r){e[e.l+1]==3&&e[e.l]++;var n=fi(e,t,r);return n.charCodeAt(0)==3?n.slice(1):n}function Ol(e){var t=b(2+e.length*8);t.write_shift(2,e.length);for(var r=0;r<e.length;++r)oi(e[r],t);return t}function Dl(e){var t=b(24),r=Be(e[0]);t.write_shift(2,r.r),t.write_shift(2,r.r),t.write_shift(2,r.c),t.write_shift(2,r.c);for(var n="d0 c9 ea 79 f9 ba ce 11 8c 82 00 aa 00 4b a9 0b".split(" "),a=0;a<16;++a)t.write_shift(1,parseInt(n[a],16));return He([t,cl(e[1])])}function Rl(e){var t=e[1].Tooltip,r=b(10+2*(t.length+1));r.write_shift(2,2048);var n=Be(e[0]);r.write_shift(2,n.r),r.write_shift(2,n.r),r.write_shift(2,n.c),r.write_shift(2,n.c);for(var a=0;a<t.length;++a)r.write_shift(2,t.charCodeAt(a));return r.write_shift(2,0),r}function Nl(e){return e||(e=b(4)),e.write_shift(2,1),e.write_shift(2,1),e}function kl(e,t,r){if(!r.cellStyles)return Sr(e,t);var n=r&&r.biff>=12?4:2,a=e.read_shift(n),i=e.read_shift(n),s=e.read_shift(n),f=e.read_shift(n),o=e.read_shift(2);n==2&&(e.l+=2);var l={s:a,e:i,w:s,ixfe:f,flags:o};return(r.biff>=5||!r.biff)&&(l.level=o>>8&7),l}function Il(e,t){var r=b(12);r.write_shift(2,t),r.write_shift(2,t),r.write_shift(2,e.width*256),r.write_shift(2,0);var n=0;return e.hidden&&(n|=1),r.write_shift(1,n),n=e.level||0,r.write_shift(1,n),r.write_shift(2,0),r}function Pl(e){for(var t=b(2*e),r=0;r<e;++r)t.write_shift(2,r+1);return t}function Ll(e,t,r){var n=b(15);return bt(n,e,t),n.write_shift(8,r,"f"),n}function Bl(e,t,r){var n=b(9);return bt(n,e,t),n.write_shift(2,r),n}var Ml=function(){var e={1:437,2:850,3:1252,4:1e4,100:852,101:866,102:865,103:861,104:895,105:620,106:737,107:857,120:950,121:949,122:936,123:932,124:874,125:1255,126:1256,150:10007,151:10029,152:10006,200:1250,201:1251,202:1254,203:1253,0:20127,8:865,9:437,10:850,11:437,13:437,14:850,15:437,16:850,17:437,18:850,19:932,20:850,21:437,22:850,23:865,24:437,25:437,26:850,27:437,28:863,29:850,31:852,34:852,35:852,36:860,37:850,38:866,55:850,64:852,77:936,78:949,79:950,80:874,87:1252,88:1252,89:1252,108:863,134:737,135:852,136:857,204:1257,255:16969},t=Gn({1:437,2:850,3:1252,4:1e4,100:852,101:866,102:865,103:861,104:895,105:620,106:737,107:857,120:950,121:949,122:936,123:932,124:874,125:1255,126:1256,150:10007,151:10029,152:10006,200:1250,201:1251,202:1254,203:1253,0:20127});function r(f,o){var l=[],c=Kr(1);switch(o.type){case"base64":c=pr(Nr(f));break;case"binary":c=pr(f);break;case"buffer":case"array":c=f;break}ar(c,0);var v=c.read_shift(1),x=!!(v&136),d=!1,T=!1;switch(v){case 2:break;case 3:break;case 48:d=!0,x=!0;break;case 49:d=!0,x=!0;break;case 131:break;case 139:break;case 140:T=!0;break;case 245:break;default:throw new Error("DBF Unsupported Version: "+v.toString(16))}var u=0,g=521;v==2&&(u=c.read_shift(2)),c.l+=3,v!=2&&(u=c.read_shift(4)),u>1048576&&(u=1e6),v!=2&&(g=c.read_shift(2));var R=c.read_shift(2),C=o.codepage||1252;v!=2&&(c.l+=16,c.read_shift(1),c[c.l]!==0&&(C=e[c[c.l]]),c.l+=1,c.l+=2),T&&(c.l+=36);for(var y=[],U={},Y=Math.min(c.length,v==2?521:g-10-(d?264:0)),Q=T?32:11;c.l<Y&&c[c.l]!=13;)switch(U={},U.name=Xr.utils.decode(C,c.slice(c.l,c.l+Q)).replace(/[\u0000\r\n].*$/g,""),c.l+=Q,U.type=String.fromCharCode(c.read_shift(1)),v!=2&&!T&&(U.offset=c.read_shift(4)),U.len=c.read_shift(1),v==2&&(U.offset=c.read_shift(2)),U.dec=c.read_shift(1),U.name.length&&y.push(U),v!=2&&(c.l+=T?13:14),U.type){case"B":(!d||U.len!=8)&&o.WTF&&""+U.name+U.type;break;case"G":case"P":o.WTF&&""+U.name+U.type;break;case"+":case"0":case"@":case"C":case"D":case"F":case"I":case"L":case"M":case"N":case"O":case"T":case"Y":break;default:throw new Error("Unknown Field Type: "+U.type)}if(c[c.l]!==13&&(c.l=g-1),c.read_shift(1)!==13)throw new Error("DBF Terminator not found "+c.l+" "+c[c.l]);c.l=g;var O=0,V=0;for(l[0]=[],V=0;V!=y.length;++V)l[0][V]=y[V].name;for(;u-- >0;){if(c[c.l]===42){c.l+=R;continue}for(++c.l,l[++O]=[],V=0,V=0;V!=y.length;++V){var B=c.slice(c.l,c.l+y[V].len);c.l+=y[V].len,ar(B,0);var I=Xr.utils.decode(C,B);switch(y[V].type){case"C":I.trim().length&&(l[O][V]=I.replace(/\s+$/,""));break;case"D":I.length===8?l[O][V]=new Date(+I.slice(0,4),+I.slice(4,6)-1,+I.slice(6,8)):l[O][V]=I;break;case"F":l[O][V]=parseFloat(I.trim());break;case"+":case"I":l[O][V]=T?B.read_shift(-4,"i")^2147483648:B.read_shift(4,"i");break;case"L":switch(I.trim().toUpperCase()){case"Y":case"T":l[O][V]=!0;break;case"N":case"F":l[O][V]=!1;break;case"":case"?":break;default:throw new Error("DBF Unrecognized L:|"+I+"|")}break;case"M":if(!x)throw new Error("DBF Unexpected MEMO for type "+v.toString(16));l[O][V]="##MEMO##"+(T?parseInt(I.trim(),10):B.read_shift(4));break;case"N":I=I.replace(/\u0000/g,"").trim(),I&&I!="."&&(l[O][V]=+I||0);break;case"@":l[O][V]=new Date(B.read_shift(-8,"f")-621356832e5);break;case"T":l[O][V]=new Date((B.read_shift(4)-2440588)*864e5+B.read_shift(4));break;case"Y":l[O][V]=B.read_shift(4,"i")/1e4+B.read_shift(4,"i")/1e4*Math.pow(2,32);break;case"O":l[O][V]=-B.read_shift(-8,"f");break;case"B":if(d&&y[V].len==8){l[O][V]=B.read_shift(8,"f");break}case"G":case"P":B.l+=y[V].len;break;case"0":if(y[V].name==="_NullFlags")break;default:throw new Error("DBF Unsupported data type "+y[V].type)}}}if(v!=2&&c.l<c.length&&c[c.l++]!=26)throw new Error("DBF EOF Marker missing "+(c.l-1)+" of "+c.length+" "+c[c.l-1].toString(16));return o&&o.sheetRows&&(l=l.slice(0,o.sheetRows)),o.DBF=y,l}function n(f,o){var l=o||{};l.dateNF||(l.dateNF="yyyymmdd");var c=dt(r(f,l),l);return c["!cols"]=l.DBF.map(function(v){return{wch:v.len,DBF:v}}),delete l.DBF,c}function a(f,o){try{return Jr(n(f,o),o)}catch(l){if(o&&o.WTF)throw l}return{SheetNames:[],Sheets:{}}}var i={B:8,C:250,L:1,D:8,"?":0,"":0};function s(f,o){var l=o||{};if(+l.codepage>=0&&Ot(+l.codepage),l.type=="string")throw new Error("Cannot write DBF to JS string");var c=er(),v=xn(f,{header:1,raw:!0,cellDates:!0}),x=v[0],d=v.slice(1),T=f["!cols"]||[],u=0,g=0,R=0,C=1;for(u=0;u<x.length;++u){if(((T[u]||{}).DBF||{}).name){x[u]=T[u].DBF.name,++R;continue}if(x[u]!=null){if(++R,typeof x[u]=="number"&&(x[u]=x[u].toString(10)),typeof x[u]!="string")throw new Error("DBF Invalid column name "+x[u]+" |"+typeof x[u]+"|");if(x.indexOf(x[u])!==u){for(g=0;g<1024;++g)if(x.indexOf(x[u]+"_"+g)==-1){x[u]+="_"+g;break}}}}var y=Ae(f["!ref"]),U=[],Y=[],Q=[];for(u=0;u<=y.e.c-y.s.c;++u){var O="",V="",B=0,I=[];for(g=0;g<d.length;++g)d[g][u]!=null&&I.push(d[g][u]);if(I.length==0||x[u]==null){U[u]="?";continue}for(g=0;g<I.length;++g){switch(typeof I[g]){case"number":V="B";break;case"string":V="C";break;case"boolean":V="L";break;case"object":V=I[g]instanceof Date?"D":"C";break;default:V="C"}B=Math.max(B,String(I[g]).length),O=O&&O!=V?"C":V}B>250&&(B=250),V=((T[u]||{}).DBF||{}).type,V=="C"&&T[u].DBF.len>B&&(B=T[u].DBF.len),O=="B"&&V=="N"&&(O="N",Q[u]=T[u].DBF.dec,B=T[u].DBF.len),Y[u]=O=="C"||V=="N"?B:i[O]||0,C+=Y[u],U[u]=O}var M=c.next(32);for(M.write_shift(4,318902576),M.write_shift(4,d.length),M.write_shift(2,296+32*R),M.write_shift(2,C),u=0;u<4;++u)M.write_shift(4,0);for(M.write_shift(4,0|(+t[ha]||3)<<8),u=0,g=0;u<x.length;++u)if(x[u]!=null){var H=c.next(32),K=(x[u].slice(-10)+"\0\0\0\0\0\0\0\0\0\0\0").slice(0,11);H.write_shift(1,K,"sbcs"),H.write_shift(1,U[u]=="?"?"C":U[u],"sbcs"),H.write_shift(4,g),H.write_shift(1,Y[u]||i[U[u]]||0),H.write_shift(1,Q[u]||0),H.write_shift(1,2),H.write_shift(4,0),H.write_shift(1,0),H.write_shift(4,0),H.write_shift(4,0),g+=Y[u]||i[U[u]]||0}var ae=c.next(264);for(ae.write_shift(4,13),u=0;u<65;++u)ae.write_shift(4,0);for(u=0;u<d.length;++u){var ne=c.next(C);for(ne.write_shift(1,0),g=0;g<x.length;++g)if(x[g]!=null)switch(U[g]){case"L":ne.write_shift(1,d[u][g]==null?63:d[u][g]?84:70);break;case"B":ne.write_shift(8,d[u][g]||0,"f");break;case"N":var he="0";for(typeof d[u][g]=="number"&&(he=d[u][g].toFixed(Q[g]||0)),R=0;R<Y[g]-he.length;++R)ne.write_shift(1,32);ne.write_shift(1,he,"sbcs");break;case"D":d[u][g]?(ne.write_shift(4,("0000"+d[u][g].getFullYear()).slice(-4),"sbcs"),ne.write_shift(2,("00"+(d[u][g].getMonth()+1)).slice(-2),"sbcs"),ne.write_shift(2,("00"+d[u][g].getDate()).slice(-2),"sbcs")):ne.write_shift(8,"00000000","sbcs");break;case"C":var fe=String(d[u][g]!=null?d[u][g]:"").slice(0,Y[g]);for(ne.write_shift(1,fe,"sbcs"),R=0;R<Y[g]-fe.length;++R)ne.write_shift(1,32);break}}return c.next(1).write_shift(1,26),c.end()}return{to_workbook:a,to_sheet:n,from_sheet:s}}(),Ul=function(){var e={AA:"\xC0",BA:"\xC1",CA:"\xC2",DA:195,HA:"\xC4",JA:197,AE:"\xC8",BE:"\xC9",CE:"\xCA",HE:"\xCB",AI:"\xCC",BI:"\xCD",CI:"\xCE",HI:"\xCF",AO:"\xD2",BO:"\xD3",CO:"\xD4",DO:213,HO:"\xD6",AU:"\xD9",BU:"\xDA",CU:"\xDB",HU:"\xDC",Aa:"\xE0",Ba:"\xE1",Ca:"\xE2",Da:227,Ha:"\xE4",Ja:229,Ae:"\xE8",Be:"\xE9",Ce:"\xEA",He:"\xEB",Ai:"\xEC",Bi:"\xED",Ci:"\xEE",Hi:"\xEF",Ao:"\xF2",Bo:"\xF3",Co:"\xF4",Do:245,Ho:"\xF6",Au:"\xF9",Bu:"\xFA",Cu:"\xFB",Hu:"\xFC",KC:"\xC7",Kc:"\xE7",q:"\xE6",z:"\u0153",a:"\xC6",j:"\u0152",DN:209,Dn:241,Hy:255,S:169,c:170,R:174,"B ":180,0:176,1:177,2:178,3:179,5:181,6:182,7:183,Q:185,k:186,b:208,i:216,l:222,s:240,y:248,"!":161,'"':162,"#":163,"(":164,"%":165,"'":167,"H ":168,"+":171,";":187,"<":188,"=":189,">":190,"?":191,"{":223},t=new RegExp("\x1BN("+Xe(e).join("|").replace(/\|\|\|/,"|\\||").replace(/([?()+])/g,"\\$1")+"|\\|)","gm"),r=function(x,d){var T=e[d];return typeof T=="number"?S0(T):T},n=function(x,d,T){var u=d.charCodeAt(0)-32<<4|T.charCodeAt(0)-48;return u==59?x:S0(u)};e["|"]=254;function a(x,d){switch(d.type){case"base64":return i(Nr(x),d);case"binary":return i(x,d);case"buffer":return i(ve&&Buffer.isBuffer(x)?x.toString("binary"):Pt(x),d);case"array":return i(gn(x),d)}throw new Error("Unrecognized type "+d.type)}function i(x,d){var T=x.split(/[\n\r]+/),u=-1,g=-1,R=0,C=0,y=[],U=[],Y=null,Q={},O=[],V=[],B=[],I=0,M;for(+d.codepage>=0&&Ot(+d.codepage);R!==T.length;++R){I=0;var H=T[R].trim().replace(/\x1B([\x20-\x2F])([\x30-\x3F])/g,n).replace(t,r),K=H.replace(/;;/g,"\0").split(";").map(function(A){return A.replace(/\u0000/g,";")}),ae=K[0],ne;if(H.length>0)switch(ae){case"ID":break;case"E":break;case"B":break;case"O":break;case"W":break;case"P":K[1].charAt(0)=="P"&&U.push(H.slice(3).replace(/;;/g,";"));break;case"C":var he=!1,fe=!1,Fe=!1,pe=!1,Ue=-1,je=-1;for(C=1;C<K.length;++C)switch(K[C].charAt(0)){case"A":break;case"X":g=parseInt(K[C].slice(1))-1,fe=!0;break;case"Y":for(u=parseInt(K[C].slice(1))-1,fe||(g=0),M=y.length;M<=u;++M)y[M]=[];break;case"K":ne=K[C].slice(1),ne.charAt(0)==='"'?ne=ne.slice(1,ne.length-1):ne==="TRUE"?ne=!0:ne==="FALSE"?ne=!1:isNaN(Dr(ne))?isNaN(Rt(ne).getDate())||(ne=qe(ne)):(ne=Dr(ne),Y!==null&&Sa(Y)&&(ne=Ca(ne))),he=!0;break;case"E":pe=!0;var S=Mo(K[C].slice(1),{r:u,c:g});y[u][g]=[y[u][g],S];break;case"S":Fe=!0,y[u][g]=[y[u][g],"S5S"];break;case"G":break;case"R":Ue=parseInt(K[C].slice(1))-1;break;case"C":je=parseInt(K[C].slice(1))-1;break;default:if(d&&d.WTF)throw new Error("SYLK bad record "+H)}if(he&&(y[u][g]&&y[u][g].length==2?y[u][g][0]=ne:y[u][g]=ne,Y=null),Fe){if(pe)throw new Error("SYLK shared formula cannot have own formula");var D=Ue>-1&&y[Ue][je];if(!D||!D[1])throw new Error("SYLK shared formula cannot find base");y[u][g][1]=Uo(D[1],{r:u-Ue,c:g-je})}break;case"F":var F=0;for(C=1;C<K.length;++C)switch(K[C].charAt(0)){case"X":g=parseInt(K[C].slice(1))-1,++F;break;case"Y":for(u=parseInt(K[C].slice(1))-1,M=y.length;M<=u;++M)y[M]=[];break;case"M":I=parseInt(K[C].slice(1))/20;break;case"F":break;case"G":break;case"P":Y=U[parseInt(K[C].slice(1))];break;case"S":break;case"D":break;case"N":break;case"W":for(B=K[C].slice(1).split(" "),M=parseInt(B[0],10);M<=parseInt(B[1],10);++M)I=parseInt(B[2],10),V[M-1]=I===0?{hidden:!0}:{wch:I},r0(V[M-1]);break;case"C":g=parseInt(K[C].slice(1))-1,V[g]||(V[g]={});break;case"R":u=parseInt(K[C].slice(1))-1,O[u]||(O[u]={}),I>0?(O[u].hpt=I,O[u].hpx=di(I)):I===0&&(O[u].hidden=!0);break;default:if(d&&d.WTF)throw new Error("SYLK bad record "+H)}F<1&&(Y=null);break;default:if(d&&d.WTF)throw new Error("SYLK bad record "+H)}}return O.length>0&&(Q["!rows"]=O),V.length>0&&(Q["!cols"]=V),d&&d.sheetRows&&(y=y.slice(0,d.sheetRows)),[y,Q]}function s(x,d){var T=a(x,d),u=T[0],g=T[1],R=dt(u,d);return Xe(g).forEach(function(C){R[C]=g[C]}),R}function f(x,d){return Jr(s(x,d),d)}function o(x,d,T,u){var g="C;Y"+(T+1)+";X"+(u+1)+";K";switch(x.t){case"n":g+=x.v||0,x.f&&!x.F&&(g+=";E"+n0(x.f,{r:T,c:u}));break;case"b":g+=x.v?"TRUE":"FALSE";break;case"e":g+=x.w||x.v;break;case"d":g+='"'+(x.w||x.v)+'"';break;case"s":g+='"'+x.v.replace(/"/g,"").replace(/;/g,";;")+'"';break}return g}function l(x,d){d.forEach(function(T,u){var g="F;W"+(u+1)+" "+(u+1)+" ";T.hidden?g+="0":(typeof T.width=="number"&&!T.wpx&&(T.wpx=on(T.width)),typeof T.wpx=="number"&&!T.wch&&(T.wch=cn(T.wpx)),typeof T.wch=="number"&&(g+=Math.round(T.wch))),g.charAt(g.length-1)!=" "&&x.push(g)})}function c(x,d){d.forEach(function(T,u){var g="F;";T.hidden?g+="M0;":T.hpt?g+="M"+20*T.hpt+";":T.hpx&&(g+="M"+20*hn(T.hpx)+";"),g.length>2&&x.push(g+"R"+(u+1))})}function v(x,d){var T=["ID;PWXL;N;E"],u=[],g=Ae(x["!ref"]),R,C=Array.isArray(x),y=`\r
`;T.push("P;PGeneral"),T.push("F;P0;DG0G8;M255"),x["!cols"]&&l(T,x["!cols"]),x["!rows"]&&c(T,x["!rows"]),T.push("B;Y"+(g.e.r-g.s.r+1)+";X"+(g.e.c-g.s.c+1)+";D"+[g.s.c,g.s.r,g.e.c,g.e.r].join(" "));for(var U=g.s.r;U<=g.e.r;++U)for(var Y=g.s.c;Y<=g.e.c;++Y){var Q=Ee({r:U,c:Y});R=C?(x[U]||[])[Y]:x[Q],!(!R||R.v==null&&(!R.f||R.F))&&u.push(o(R,x,U,Y))}return T.join(y)+y+u.join(y)+y+"E"+y}return{to_workbook:f,to_sheet:s,from_sheet:v}}(),bl=function(){function e(i,s){switch(s.type){case"base64":return t(Nr(i),s);case"binary":return t(i,s);case"buffer":return t(ve&&Buffer.isBuffer(i)?i.toString("binary"):Pt(i),s);case"array":return t(gn(i),s)}throw new Error("Unrecognized type "+s.type)}function t(i,s){for(var f=i.split(`
`),o=-1,l=-1,c=0,v=[];c!==f.length;++c){if(f[c].trim()==="BOT"){v[++o]=[],l=0;continue}if(!(o<0)){var x=f[c].trim().split(","),d=x[0],T=x[1];++c;for(var u=f[c]||"";(u.match(/["]/g)||[]).length&1&&c<f.length-1;)u+=`
`+f[++c];switch(u=u.trim(),+d){case-1:if(u==="BOT"){v[++o]=[],l=0;continue}else if(u!=="EOD")throw new Error("Unrecognized DIF special command "+u);break;case 0:u==="TRUE"?v[o][l]=!0:u==="FALSE"?v[o][l]=!1:isNaN(Dr(T))?isNaN(Rt(T).getDate())?v[o][l]=T:v[o][l]=qe(T):v[o][l]=Dr(T),++l;break;case 1:u=u.slice(1,u.length-1),u=u.replace(/""/g,'"'),u&&u.match(/^=".*"$/)&&(u=u.slice(2,-1)),v[o][l++]=u!==""?u:null;break}if(u==="EOD")break}}return s&&s.sheetRows&&(v=v.slice(0,s.sheetRows)),v}function r(i,s){return dt(e(i,s),s)}function n(i,s){return Jr(r(i,s),s)}var a=function(){var i=function(o,l,c,v,x){o.push(l),o.push(c+","+v),o.push('"'+x.replace(/"/g,'""')+'"')},s=function(o,l,c,v){o.push(l+","+c),o.push(l==1?'"'+v.replace(/"/g,'""')+'"':v)};return function(o){var l=[],c=Ae(o["!ref"]),v,x=Array.isArray(o);i(l,"TABLE",0,1,"sheetjs"),i(l,"VECTORS",0,c.e.r-c.s.r+1,""),i(l,"TUPLES",0,c.e.c-c.s.c+1,""),i(l,"DATA",0,0,"");for(var d=c.s.r;d<=c.e.r;++d){s(l,-1,0,"BOT");for(var T=c.s.c;T<=c.e.c;++T){var u=Ee({r:d,c:T});if(v=x?(o[d]||[])[T]:o[u],!v){s(l,1,0,"");continue}switch(v.t){case"n":var g=v.w;!g&&v.v!=null&&(g=v.v),g==null?v.f&&!v.F?s(l,1,0,"="+v.f):s(l,1,0,""):s(l,0,g,"V");break;case"b":s(l,0,v.v?1:0,v.v?"TRUE":"FALSE");break;case"s":s(l,1,0,isNaN(v.v)?v.v:'="'+v.v+'"');break;case"d":v.w||(v.w=Mr(v.z||Re[14],rr(qe(v.v)))),s(l,0,v.w,"V");break;default:s(l,1,0,"")}}}s(l,-1,0,"EOD");var R=`\r
`,C=l.join(R);return C}}();return{to_workbook:n,to_sheet:r,from_sheet:a}}(),ci=function(){function e(v){return v.replace(/\\b/g,"\\").replace(/\\c/g,":").replace(/\\n/g,`
`)}function t(v){return v.replace(/\\/g,"\\b").replace(/:/g,"\\c").replace(/\n/g,"\\n")}function r(v,x){for(var d=v.split(`
`),T=-1,u=-1,g=0,R=[];g!==d.length;++g){var C=d[g].trim().split(":");if(C[0]==="cell"){var y=Be(C[1]);if(R.length<=y.r)for(T=R.length;T<=y.r;++T)R[T]||(R[T]=[]);switch(T=y.r,u=y.c,C[2]){case"t":R[T][u]=e(C[3]);break;case"v":R[T][u]=+C[3];break;case"vtf":var U=C[C.length-1];case"vtc":switch(C[3]){case"nl":R[T][u]=!!+C[4];break;default:R[T][u]=+C[4];break}C[2]=="vtf"&&(R[T][u]=[R[T][u],U])}}}return x&&x.sheetRows&&(R=R.slice(0,x.sheetRows)),R}function n(v,x){return dt(r(v,x),x)}function a(v,x){return Jr(n(v,x),x)}var i=["socialcalc:version:1.5","MIME-Version: 1.0","Content-Type: multipart/mixed; boundary=SocialCalcSpreadsheetControlSave"].join(`
`),s=["--SocialCalcSpreadsheetControlSave","Content-type: text/plain; charset=UTF-8"].join(`
`)+`
`,f=["# SocialCalc Spreadsheet Control Save","part:sheet"].join(`
`),o="--SocialCalcSpreadsheetControlSave--";function l(v){if(!v||!v["!ref"])return"";for(var x=[],d=[],T,u="",g=fr(v["!ref"]),R=Array.isArray(v),C=g.s.r;C<=g.e.r;++C)for(var y=g.s.c;y<=g.e.c;++y)if(u=Ee({r:C,c:y}),T=R?(v[C]||[])[y]:v[u],!(!T||T.v==null||T.t==="z")){switch(d=["cell",u,"t"],T.t){case"s":case"str":d.push(t(T.v));break;case"n":T.f?(d[2]="vtf",d[3]="n",d[4]=T.v,d[5]=t(T.f)):(d[2]="v",d[3]=T.v);break;case"b":d[2]="vt"+(T.f?"f":"c"),d[3]="nl",d[4]=T.v?"1":"0",d[5]=t(T.f||(T.v?"TRUE":"FALSE"));break;case"d":var U=rr(qe(T.v));d[2]="vtc",d[3]="nd",d[4]=""+U,d[5]=T.w||Mr(T.z||Re[14],U);break;case"e":continue}x.push(d.join(":"))}return x.push("sheet:c:"+(g.e.c-g.s.c+1)+":r:"+(g.e.r-g.s.r+1)+":tvf:1"),x.push("valueformat:1:text-wiki"),x.join(`
`)}function c(v){return[i,s,f,s,l(v),o].join(`
`)}return{to_workbook:a,to_sheet:n,from_sheet:c}}(),Wl=function(){function e(c,v,x,d,T){T.raw?v[x][d]=c:c===""||(c==="TRUE"?v[x][d]=!0:c==="FALSE"?v[x][d]=!1:isNaN(Dr(c))?isNaN(Rt(c).getDate())?v[x][d]=c:v[x][d]=qe(c):v[x][d]=Dr(c))}function t(c,v){var x=v||{},d=[];if(!c||c.length===0)return d;for(var T=c.split(/[\r\n]/),u=T.length-1;u>=0&&T[u].length===0;)--u;for(var g=10,R=0,C=0;C<=u;++C)R=T[C].indexOf(" "),R==-1?R=T[C].length:R++,g=Math.max(g,R);for(C=0;C<=u;++C){d[C]=[];var y=0;for(e(T[C].slice(0,g).trim(),d,C,y,x),y=1;y<=(T[C].length-g)/10+1;++y)e(T[C].slice(g+(y-1)*10,g+y*10).trim(),d,C,y,x)}return x.sheetRows&&(d=d.slice(0,x.sheetRows)),d}var r={44:",",9:"	",59:";",124:"|"},n={44:3,9:2,59:1,124:0};function a(c){for(var v={},x=!1,d=0,T=0;d<c.length;++d)(T=c.charCodeAt(d))==34?x=!x:!x&&T in r&&(v[T]=(v[T]||0)+1);T=[];for(d in v)Object.prototype.hasOwnProperty.call(v,d)&&T.push([v[d],d]);if(!T.length){v=n;for(d in v)Object.prototype.hasOwnProperty.call(v,d)&&T.push([v[d],d])}return T.sort(function(u,g){return u[0]-g[0]||n[u[1]]-n[g[1]]}),r[T.pop()[1]]||44}function i(c,v){var x=v||{},d="",T=x.dense?[]:{},u={s:{c:0,r:0},e:{c:0,r:0}};c.slice(0,4)=="sep="?c.charCodeAt(5)==13&&c.charCodeAt(6)==10?(d=c.charAt(4),c=c.slice(7)):c.charCodeAt(5)==13||c.charCodeAt(5)==10?(d=c.charAt(4),c=c.slice(6)):d=a(c.slice(0,1024)):x&&x.FS?d=x.FS:d=a(c.slice(0,1024));var g=0,R=0,C=0,y=0,U=0,Y=d.charCodeAt(0),Q=!1,O=0,V=c.charCodeAt(0);c=c.replace(/\r\n/mg,`
`);var B=x.dateNF!=null?rf(x.dateNF):null;function I(){var M=c.slice(y,U),H={};if(M.charAt(0)=='"'&&M.charAt(M.length-1)=='"'&&(M=M.slice(1,-1).replace(/""/g,'"')),M.length===0)H.t="z";else if(x.raw)H.t="s",H.v=M;else if(M.trim().length===0)H.t="s",H.v=M;else if(M.charCodeAt(0)==61)M.charCodeAt(1)==34&&M.charCodeAt(M.length-1)==34?(H.t="s",H.v=M.slice(2,-1).replace(/""/g,'"')):bo(M)?(H.t="n",H.f=M.slice(1)):(H.t="s",H.v=M);else if(M=="TRUE")H.t="b",H.v=!0;else if(M=="FALSE")H.t="b",H.v=!1;else if(!isNaN(C=Dr(M)))H.t="n",x.cellText!==!1&&(H.w=M),H.v=C;else if(!isNaN(Rt(M).getDate())||B&&M.match(B)){H.z=x.dateNF||Re[14];var K=0;B&&M.match(B)&&(M=tf(M,x.dateNF,M.match(B)||[]),K=1),x.cellDates?(H.t="d",H.v=qe(M,K)):(H.t="n",H.v=rr(qe(M,K))),x.cellText!==!1&&(H.w=Mr(H.z,H.v instanceof Date?rr(H.v):H.v)),x.cellNF||delete H.z}else H.t="s",H.v=M;if(H.t=="z"||(x.dense?(T[g]||(T[g]=[]),T[g][R]=H):T[Ee({c:R,r:g})]=H),y=U+1,V=c.charCodeAt(y),u.e.c<R&&(u.e.c=R),u.e.r<g&&(u.e.r=g),O==Y)++R;else if(R=0,++g,x.sheetRows&&x.sheetRows<=g)return!0}e:for(;U<c.length;++U)switch(O=c.charCodeAt(U)){case 34:V===34&&(Q=!Q);break;case Y:case 10:case 13:if(!Q&&I())break e;break}return U-y>0&&I(),T["!ref"]=ke(u),T}function s(c,v){return!(v&&v.PRN)||v.FS||c.slice(0,4)=="sep="||c.indexOf("	")>=0||c.indexOf(",")>=0||c.indexOf(";")>=0?i(c,v):dt(t(c,v),v)}function f(c,v){var x="",d=v.type=="string"?[0,0,0,0]:Qu(c,v);switch(v.type){case"base64":x=Nr(c);break;case"binary":x=c;break;case"buffer":v.codepage==65001?x=c.toString("utf8"):v.codepage&&typeof Xr<"u"?x=Xr.utils.decode(v.codepage,c):x=ve&&Buffer.isBuffer(c)?c.toString("binary"):Pt(c);break;case"array":x=gn(c);break;case"string":x=c;break;default:throw new Error("Unrecognized type "+v.type)}return d[0]==239&&d[1]==187&&d[2]==191?x=St(x.slice(3)):v.type!="string"&&v.type!="buffer"&&v.codepage==65001?x=St(x):v.type=="binary"&&typeof Xr<"u"&&v.codepage&&(x=Xr.utils.decode(v.codepage,Xr.utils.encode(28591,x))),x.slice(0,19)=="socialcalc:version:"?ci.to_sheet(v.type=="string"?x:St(x),v):s(x,v)}function o(c,v){return Jr(f(c,v),v)}function l(c){for(var v=[],x=Ae(c["!ref"]),d,T=Array.isArray(c),u=x.s.r;u<=x.e.r;++u){for(var g=[],R=x.s.c;R<=x.e.c;++R){var C=Ee({r:u,c:R});if(d=T?(c[u]||[])[R]:c[C],!d||d.v==null){g.push("          ");continue}for(var y=(d.w||(kr(d),d.w)||"").slice(0,10);y.length<10;)y+=" ";g.push(y+(R===0?" ":""))}v.push(g.join(""))}return v.join(`
`)}return{to_workbook:o,to_sheet:f,from_sheet:l}}(),ea=function(){function e(S,D,F){if(!!S){ar(S,S.l||0);for(var A=F.Enum||Ue;S.l<S.length;){var X=S.read_shift(2),le=A[X]||A[65535],oe=S.read_shift(2),se=S.l+oe,ee=le.f&&le.f(S,oe,F);if(S.l=se,D(ee,le,X))return}}}function t(S,D){switch(D.type){case"base64":return r(pr(Nr(S)),D);case"binary":return r(pr(S),D);case"buffer":case"array":return r(S,D)}throw"Unsupported type "+D.type}function r(S,D){if(!S)return S;var F=D||{},A=F.dense?[]:{},X="Sheet1",le="",oe=0,se={},ee=[],Se=[],xe={s:{r:0,c:0},e:{r:0,c:0}},Ye=F.sheetRows||0;if(S[2]==0&&(S[3]==8||S[3]==9)&&S.length>=16&&S[14]==5&&S[15]===108)throw new Error("Unsupported Works 3 for Mac file");if(S[2]==2)F.Enum=Ue,e(S,function(ie,lr,Fr){switch(Fr){case 0:F.vers=ie,ie>=4096&&(F.qpro=!0);break;case 6:xe=ie;break;case 204:ie&&(le=ie);break;case 222:le=ie;break;case 15:case 51:F.qpro||(ie[1].v=ie[1].v.slice(1));case 13:case 14:case 16:Fr==14&&(ie[2]&112)==112&&(ie[2]&15)>1&&(ie[2]&15)<15&&(ie[1].z=F.dateNF||Re[14],F.cellDates&&(ie[1].t="d",ie[1].v=Ca(ie[1].v))),F.qpro&&ie[3]>oe&&(A["!ref"]=ke(xe),se[X]=A,ee.push(X),A=F.dense?[]:{},xe={s:{r:0,c:0},e:{r:0,c:0}},oe=ie[3],X=le||"Sheet"+(oe+1),le="");var Hr=F.dense?(A[ie[0].r]||[])[ie[0].c]:A[Ee(ie[0])];if(Hr){Hr.t=ie[1].t,Hr.v=ie[1].v,ie[1].z!=null&&(Hr.z=ie[1].z),ie[1].f!=null&&(Hr.f=ie[1].f);break}F.dense?(A[ie[0].r]||(A[ie[0].r]=[]),A[ie[0].r][ie[0].c]=ie[1]):A[Ee(ie[0])]=ie[1];break}},F);else if(S[2]==26||S[2]==14)F.Enum=je,S[2]==14&&(F.qpro=!0,S.l=0),e(S,function(ie,lr,Fr){switch(Fr){case 204:X=ie;break;case 22:ie[1].v=ie[1].v.slice(1);case 23:case 24:case 25:case 37:case 39:case 40:if(ie[3]>oe&&(A["!ref"]=ke(xe),se[X]=A,ee.push(X),A=F.dense?[]:{},xe={s:{r:0,c:0},e:{r:0,c:0}},oe=ie[3],X="Sheet"+(oe+1)),Ye>0&&ie[0].r>=Ye)break;F.dense?(A[ie[0].r]||(A[ie[0].r]=[]),A[ie[0].r][ie[0].c]=ie[1]):A[Ee(ie[0])]=ie[1],xe.e.c<ie[0].c&&(xe.e.c=ie[0].c),xe.e.r<ie[0].r&&(xe.e.r=ie[0].r);break;case 27:ie[14e3]&&(Se[ie[14e3][0]]=ie[14e3][1]);break;case 1537:Se[ie[0]]=ie[1],ie[0]==oe&&(X=ie[1]);break}},F);else throw new Error("Unrecognized LOTUS BOF "+S[2]);if(A["!ref"]=ke(xe),se[le||X]=A,ee.push(le||X),!Se.length)return{SheetNames:ee,Sheets:se};for(var me={},Ar=[],Oe=0;Oe<Se.length;++Oe)se[ee[Oe]]?(Ar.push(Se[Oe]||ee[Oe]),me[Se[Oe]]=se[Se[Oe]]||se[ee[Oe]]):(Ar.push(Se[Oe]),me[Se[Oe]]={"!ref":"A1"});return{SheetNames:Ar,Sheets:me}}function n(S,D){var F=D||{};if(+F.codepage>=0&&Ot(+F.codepage),F.type=="string")throw new Error("Cannot write WK1 to JS string");var A=er(),X=Ae(S["!ref"]),le=Array.isArray(S),oe=[];Z(A,0,i(1030)),Z(A,6,o(X));for(var se=Math.min(X.e.r,8191),ee=X.s.r;ee<=se;++ee)for(var Se=Ge(ee),xe=X.s.c;xe<=X.e.c;++xe){ee===X.s.r&&(oe[xe]=ze(xe));var Ye=oe[xe]+Se,me=le?(S[ee]||[])[xe]:S[Ye];if(!(!me||me.t=="z"))if(me.t=="n")(me.v|0)==me.v&&me.v>=-32768&&me.v<=32767?Z(A,13,d(ee,xe,me.v)):Z(A,14,u(ee,xe,me.v));else{var Ar=kr(me);Z(A,15,v(ee,xe,Ar.slice(0,239)))}}return Z(A,1),A.end()}function a(S,D){var F=D||{};if(+F.codepage>=0&&Ot(+F.codepage),F.type=="string")throw new Error("Cannot write WK3 to JS string");var A=er();Z(A,0,s(S));for(var X=0,le=0;X<S.SheetNames.length;++X)(S.Sheets[S.SheetNames[X]]||{})["!ref"]&&Z(A,27,pe(S.SheetNames[X],le++));var oe=0;for(X=0;X<S.SheetNames.length;++X){var se=S.Sheets[S.SheetNames[X]];if(!(!se||!se["!ref"])){for(var ee=Ae(se["!ref"]),Se=Array.isArray(se),xe=[],Ye=Math.min(ee.e.r,8191),me=ee.s.r;me<=Ye;++me)for(var Ar=Ge(me),Oe=ee.s.c;Oe<=ee.e.c;++Oe){me===ee.s.r&&(xe[Oe]=ze(Oe));var ie=xe[Oe]+Ar,lr=Se?(se[me]||[])[Oe]:se[ie];if(!(!lr||lr.t=="z"))if(lr.t=="n")Z(A,23,I(me,Oe,oe,lr.v));else{var Fr=kr(lr);Z(A,22,O(me,Oe,oe,Fr.slice(0,239)))}}++oe}}return Z(A,1),A.end()}function i(S){var D=b(2);return D.write_shift(2,S),D}function s(S){var D=b(26);D.write_shift(2,4096),D.write_shift(2,4),D.write_shift(4,0);for(var F=0,A=0,X=0,le=0;le<S.SheetNames.length;++le){var oe=S.SheetNames[le],se=S.Sheets[oe];if(!(!se||!se["!ref"])){++X;var ee=fr(se["!ref"]);F<ee.e.r&&(F=ee.e.r),A<ee.e.c&&(A=ee.e.c)}}return F>8191&&(F=8191),D.write_shift(2,F),D.write_shift(1,X),D.write_shift(1,A),D.write_shift(2,0),D.write_shift(2,0),D.write_shift(1,1),D.write_shift(1,2),D.write_shift(4,0),D.write_shift(4,0),D}function f(S,D,F){var A={s:{c:0,r:0},e:{c:0,r:0}};return D==8&&F.qpro?(A.s.c=S.read_shift(1),S.l++,A.s.r=S.read_shift(2),A.e.c=S.read_shift(1),S.l++,A.e.r=S.read_shift(2),A):(A.s.c=S.read_shift(2),A.s.r=S.read_shift(2),D==12&&F.qpro&&(S.l+=2),A.e.c=S.read_shift(2),A.e.r=S.read_shift(2),D==12&&F.qpro&&(S.l+=2),A.s.c==65535&&(A.s.c=A.e.c=A.s.r=A.e.r=0),A)}function o(S){var D=b(8);return D.write_shift(2,S.s.c),D.write_shift(2,S.s.r),D.write_shift(2,S.e.c),D.write_shift(2,S.e.r),D}function l(S,D,F){var A=[{c:0,r:0},{t:"n",v:0},0,0];return F.qpro&&F.vers!=20768?(A[0].c=S.read_shift(1),A[3]=S.read_shift(1),A[0].r=S.read_shift(2),S.l+=2):(A[2]=S.read_shift(1),A[0].c=S.read_shift(2),A[0].r=S.read_shift(2)),A}function c(S,D,F){var A=S.l+D,X=l(S,D,F);if(X[1].t="s",F.vers==20768){S.l++;var le=S.read_shift(1);return X[1].v=S.read_shift(le,"utf8"),X}return F.qpro&&S.l++,X[1].v=S.read_shift(A-S.l,"cstr"),X}function v(S,D,F){var A=b(7+F.length);A.write_shift(1,255),A.write_shift(2,D),A.write_shift(2,S),A.write_shift(1,39);for(var X=0;X<A.length;++X){var le=F.charCodeAt(X);A.write_shift(1,le>=128?95:le)}return A.write_shift(1,0),A}function x(S,D,F){var A=l(S,D,F);return A[1].v=S.read_shift(2,"i"),A}function d(S,D,F){var A=b(7);return A.write_shift(1,255),A.write_shift(2,D),A.write_shift(2,S),A.write_shift(2,F,"i"),A}function T(S,D,F){var A=l(S,D,F);return A[1].v=S.read_shift(8,"f"),A}function u(S,D,F){var A=b(13);return A.write_shift(1,255),A.write_shift(2,D),A.write_shift(2,S),A.write_shift(8,F,"f"),A}function g(S,D,F){var A=S.l+D,X=l(S,D,F);if(X[1].v=S.read_shift(8,"f"),F.qpro)S.l=A;else{var le=S.read_shift(2);U(S.slice(S.l,S.l+le),X),S.l+=le}return X}function R(S,D,F){var A=D&32768;return D&=-32769,D=(A?S:0)+(D>=8192?D-16384:D),(A?"":"$")+(F?ze(D):Ge(D))}var C={51:["FALSE",0],52:["TRUE",0],70:["LEN",1],80:["SUM",69],81:["AVERAGEA",69],82:["COUNTA",69],83:["MINA",69],84:["MAXA",69],111:["T",1]},y=["","","","","","","","","","+","-","*","/","^","=","<>","<=",">=","<",">","","","","","&","","","","","","",""];function U(S,D){ar(S,0);for(var F=[],A=0,X="",le="",oe="",se="";S.l<S.length;){var ee=S[S.l++];switch(ee){case 0:F.push(S.read_shift(8,"f"));break;case 1:le=R(D[0].c,S.read_shift(2),!0),X=R(D[0].r,S.read_shift(2),!1),F.push(le+X);break;case 2:{var Se=R(D[0].c,S.read_shift(2),!0),xe=R(D[0].r,S.read_shift(2),!1);le=R(D[0].c,S.read_shift(2),!0),X=R(D[0].r,S.read_shift(2),!1),F.push(Se+xe+":"+le+X)}break;case 3:if(S.l<S.length){console.error("WK1 premature formula end");return}break;case 4:F.push("("+F.pop()+")");break;case 5:F.push(S.read_shift(2));break;case 6:{for(var Ye="";ee=S[S.l++];)Ye+=String.fromCharCode(ee);F.push('"'+Ye.replace(/"/g,'""')+'"')}break;case 8:F.push("-"+F.pop());break;case 23:F.push("+"+F.pop());break;case 22:F.push("NOT("+F.pop()+")");break;case 20:case 21:se=F.pop(),oe=F.pop(),F.push(["AND","OR"][ee-20]+"("+oe+","+se+")");break;default:if(ee<32&&y[ee])se=F.pop(),oe=F.pop(),F.push(oe+y[ee]+se);else if(C[ee]){if(A=C[ee][1],A==69&&(A=S[S.l++]),A>F.length){console.error("WK1 bad formula parse 0x"+ee.toString(16)+":|"+F.join("|")+"|");return}var me=F.slice(-A);F.length-=A,F.push(C[ee][0]+"("+me.join(",")+")")}else return ee<=7?console.error("WK1 invalid opcode "+ee.toString(16)):ee<=24?console.error("WK1 unsupported op "+ee.toString(16)):ee<=30?console.error("WK1 invalid opcode "+ee.toString(16)):ee<=115?console.error("WK1 unsupported function opcode "+ee.toString(16)):console.error("WK1 unrecognized opcode "+ee.toString(16))}}F.length==1?D[1].f=""+F[0]:console.error("WK1 bad formula parse |"+F.join("|")+"|")}function Y(S){var D=[{c:0,r:0},{t:"n",v:0},0];return D[0].r=S.read_shift(2),D[3]=S[S.l++],D[0].c=S[S.l++],D}function Q(S,D){var F=Y(S);return F[1].t="s",F[1].v=S.read_shift(D-4,"cstr"),F}function O(S,D,F,A){var X=b(6+A.length);X.write_shift(2,S),X.write_shift(1,F),X.write_shift(1,D),X.write_shift(1,39);for(var le=0;le<A.length;++le){var oe=A.charCodeAt(le);X.write_shift(1,oe>=128?95:oe)}return X.write_shift(1,0),X}function V(S,D){var F=Y(S);F[1].v=S.read_shift(2);var A=F[1].v>>1;if(F[1].v&1)switch(A&7){case 0:A=(A>>3)*5e3;break;case 1:A=(A>>3)*500;break;case 2:A=(A>>3)/20;break;case 3:A=(A>>3)/200;break;case 4:A=(A>>3)/2e3;break;case 5:A=(A>>3)/2e4;break;case 6:A=(A>>3)/16;break;case 7:A=(A>>3)/64;break}return F[1].v=A,F}function B(S,D){var F=Y(S),A=S.read_shift(4),X=S.read_shift(4),le=S.read_shift(2);if(le==65535)return A===0&&X===3221225472?(F[1].t="e",F[1].v=15):A===0&&X===3489660928?(F[1].t="e",F[1].v=42):F[1].v=0,F;var oe=le&32768;return le=(le&32767)-16446,F[1].v=(1-oe*2)*(X*Math.pow(2,le+32)+A*Math.pow(2,le)),F}function I(S,D,F,A){var X=b(14);if(X.write_shift(2,S),X.write_shift(1,F),X.write_shift(1,D),A==0)return X.write_shift(4,0),X.write_shift(4,0),X.write_shift(2,65535),X;var le=0,oe=0,se=0,ee=0;return A<0&&(le=1,A=-A),oe=Math.log2(A)|0,A/=Math.pow(2,oe-31),ee=A>>>0,(ee&2147483648)==0&&(A/=2,++oe,ee=A>>>0),A-=ee,ee|=2147483648,ee>>>=0,A*=Math.pow(2,32),se=A>>>0,X.write_shift(4,se),X.write_shift(4,ee),oe+=16383+(le?32768:0),X.write_shift(2,oe),X}function M(S,D){var F=B(S);return S.l+=D-14,F}function H(S,D){var F=Y(S),A=S.read_shift(4);return F[1].v=A>>6,F}function K(S,D){var F=Y(S),A=S.read_shift(8,"f");return F[1].v=A,F}function ae(S,D){var F=K(S);return S.l+=D-10,F}function ne(S,D){return S[S.l+D-1]==0?S.read_shift(D,"cstr"):""}function he(S,D){var F=S[S.l++];F>D-1&&(F=D-1);for(var A="";A.length<F;)A+=String.fromCharCode(S[S.l++]);return A}function fe(S,D,F){if(!(!F.qpro||D<21)){var A=S.read_shift(1);S.l+=17,S.l+=1,S.l+=2;var X=S.read_shift(D-21,"cstr");return[A,X]}}function Fe(S,D){for(var F={},A=S.l+D;S.l<A;){var X=S.read_shift(2);if(X==14e3){for(F[X]=[0,""],F[X][0]=S.read_shift(2);S[S.l];)F[X][1]+=String.fromCharCode(S[S.l]),S.l++;S.l++}}return F}function pe(S,D){var F=b(5+S.length);F.write_shift(2,14e3),F.write_shift(2,D);for(var A=0;A<S.length;++A){var X=S.charCodeAt(A);F[F.l++]=X>127?95:X}return F[F.l++]=0,F}var Ue={0:{n:"BOF",f:ii},1:{n:"EOF"},2:{n:"CALCMODE"},3:{n:"CALCORDER"},4:{n:"SPLIT"},5:{n:"SYNC"},6:{n:"RANGE",f},7:{n:"WINDOW1"},8:{n:"COLW1"},9:{n:"WINTWO"},10:{n:"COLW2"},11:{n:"NAME"},12:{n:"BLANK"},13:{n:"INTEGER",f:x},14:{n:"NUMBER",f:T},15:{n:"LABEL",f:c},16:{n:"FORMULA",f:g},24:{n:"TABLE"},25:{n:"ORANGE"},26:{n:"PRANGE"},27:{n:"SRANGE"},28:{n:"FRANGE"},29:{n:"KRANGE1"},32:{n:"HRANGE"},35:{n:"KRANGE2"},36:{n:"PROTEC"},37:{n:"FOOTER"},38:{n:"HEADER"},39:{n:"SETUP"},40:{n:"MARGINS"},41:{n:"LABELFMT"},42:{n:"TITLES"},43:{n:"SHEETJS"},45:{n:"GRAPH"},46:{n:"NGRAPH"},47:{n:"CALCCOUNT"},48:{n:"UNFORMATTED"},49:{n:"CURSORW12"},50:{n:"WINDOW"},51:{n:"STRING",f:c},55:{n:"PASSWORD"},56:{n:"LOCKED"},60:{n:"QUERY"},61:{n:"QUERYNAME"},62:{n:"PRINT"},63:{n:"PRINTNAME"},64:{n:"GRAPH2"},65:{n:"GRAPHNAME"},66:{n:"ZOOM"},67:{n:"SYMSPLIT"},68:{n:"NSROWS"},69:{n:"NSCOLS"},70:{n:"RULER"},71:{n:"NNAME"},72:{n:"ACOMM"},73:{n:"AMACRO"},74:{n:"PARSE"},102:{n:"PRANGES??"},103:{n:"RRANGES??"},104:{n:"FNAME??"},105:{n:"MRANGES??"},204:{n:"SHEETNAMECS",f:ne},222:{n:"SHEETNAMELP",f:he},65535:{n:""}},je={0:{n:"BOF"},1:{n:"EOF"},2:{n:"PASSWORD"},3:{n:"CALCSET"},4:{n:"WINDOWSET"},5:{n:"SHEETCELLPTR"},6:{n:"SHEETLAYOUT"},7:{n:"COLUMNWIDTH"},8:{n:"HIDDENCOLUMN"},9:{n:"USERRANGE"},10:{n:"SYSTEMRANGE"},11:{n:"ZEROFORCE"},12:{n:"SORTKEYDIR"},13:{n:"FILESEAL"},14:{n:"DATAFILLNUMS"},15:{n:"PRINTMAIN"},16:{n:"PRINTSTRING"},17:{n:"GRAPHMAIN"},18:{n:"GRAPHSTRING"},19:{n:"??"},20:{n:"ERRCELL"},21:{n:"NACELL"},22:{n:"LABEL16",f:Q},23:{n:"NUMBER17",f:B},24:{n:"NUMBER18",f:V},25:{n:"FORMULA19",f:M},26:{n:"FORMULA1A"},27:{n:"XFORMAT",f:Fe},28:{n:"DTLABELMISC"},29:{n:"DTLABELCELL"},30:{n:"GRAPHWINDOW"},31:{n:"CPA"},32:{n:"LPLAUTO"},33:{n:"QUERY"},34:{n:"HIDDENSHEET"},35:{n:"??"},37:{n:"NUMBER25",f:H},38:{n:"??"},39:{n:"NUMBER27",f:K},40:{n:"FORMULA28",f:ae},142:{n:"??"},147:{n:"??"},150:{n:"??"},151:{n:"??"},152:{n:"??"},153:{n:"??"},154:{n:"??"},155:{n:"??"},156:{n:"??"},163:{n:"??"},174:{n:"??"},175:{n:"??"},176:{n:"??"},177:{n:"??"},184:{n:"??"},185:{n:"??"},186:{n:"??"},187:{n:"??"},188:{n:"??"},195:{n:"??"},201:{n:"??"},204:{n:"SHEETNAMECS",f:ne},205:{n:"??"},206:{n:"??"},207:{n:"??"},208:{n:"??"},256:{n:"??"},259:{n:"??"},260:{n:"??"},261:{n:"??"},262:{n:"??"},263:{n:"??"},265:{n:"??"},266:{n:"??"},267:{n:"??"},268:{n:"??"},270:{n:"??"},271:{n:"??"},384:{n:"??"},389:{n:"??"},390:{n:"??"},393:{n:"??"},396:{n:"??"},512:{n:"??"},514:{n:"??"},513:{n:"??"},516:{n:"??"},517:{n:"??"},640:{n:"??"},641:{n:"??"},642:{n:"??"},643:{n:"??"},644:{n:"??"},645:{n:"??"},646:{n:"??"},647:{n:"??"},648:{n:"??"},658:{n:"??"},659:{n:"??"},660:{n:"??"},661:{n:"??"},662:{n:"??"},665:{n:"??"},666:{n:"??"},768:{n:"??"},772:{n:"??"},1537:{n:"SHEETINFOQP",f:fe},1600:{n:"??"},1602:{n:"??"},1793:{n:"??"},1794:{n:"??"},1795:{n:"??"},1796:{n:"??"},1920:{n:"??"},2048:{n:"??"},2049:{n:"??"},2052:{n:"??"},2688:{n:"??"},10998:{n:"??"},12849:{n:"??"},28233:{n:"??"},28484:{n:"??"},65535:{n:""}};return{sheet_to_wk1:n,book_to_wk3:a,to_workbook:t}}(),Hl=/^\s|\s$|[\t\n\r]/;function hi(e,t){if(!t.bookSST)return"";var r=[Ie];r[r.length]=J("sst",null,{xmlns:xt[0],count:e.Count,uniqueCount:e.Unique});for(var n=0;n!=e.length;++n)if(e[n]!=null){var a=e[n],i="<si>";a.r?i+=a.r:(i+="<t",a.t||(a.t=""),a.t.match(Hl)&&(i+=' xml:space="preserve"'),i+=">"+Te(a.t)+"</t>"),i+="</si>",r[r.length]=i}return r.length>2&&(r[r.length]="</sst>",r[1]=r[1].replace("/>",">")),r.join("")}function Vl(e){return[e.read_shift(4),e.read_shift(4)]}function Gl(e,t){return t||(t=b(8)),t.write_shift(4,e.Count),t.write_shift(4,e.Unique),t}var Xl=Pf;function $l(e){var t=er();G(t,159,Gl(e));for(var r=0;r<e.length;++r)G(t,19,Xl(e[r]));return G(t,160),t.end()}function zl(e){for(var t=[],r=e.split(""),n=0;n<r.length;++n)t[n]=r[n].charCodeAt(0);return t}function ui(e){var t=0,r,n=zl(e),a=n.length+1,i,s,f,o,l;for(r=Kr(a),r[0]=n.length,i=1;i!=a;++i)r[i]=n[i-1];for(i=a-1;i>=0;--i)s=r[i],f=(t&16384)===0?0:1,o=t<<1&32767,l=f|o,t=l^s;return t^52811}var Kl=function(){function e(a,i){switch(i.type){case"base64":return t(Nr(a),i);case"binary":return t(a,i);case"buffer":return t(ve&&Buffer.isBuffer(a)?a.toString("binary"):Pt(a),i);case"array":return t(gn(a),i)}throw new Error("Unrecognized type "+i.type)}function t(a,i){var s=i||{},f=s.dense?[]:{},o=a.match(/\\trowd.*?\\row\b/g);if(!o.length)throw new Error("RTF missing table");var l={s:{c:0,r:0},e:{c:0,r:o.length-1}};return o.forEach(function(c,v){Array.isArray(f)&&(f[v]=[]);for(var x=/\\\w+\b/g,d=0,T,u=-1;T=x.exec(c);){switch(T[0]){case"\\cell":var g=c.slice(d,x.lastIndex-T[0].length);if(g[0]==" "&&(g=g.slice(1)),++u,g.length){var R={v:g,t:"s"};Array.isArray(f)?f[v][u]=R:f[Ee({r:v,c:u})]=R}break}d=x.lastIndex}u>l.e.c&&(l.e.c=u)}),f["!ref"]=ke(l),f}function r(a,i){return Jr(e(a,i),i)}function n(a){for(var i=["{\\rtf1\\ansi"],s=Ae(a["!ref"]),f,o=Array.isArray(a),l=s.s.r;l<=s.e.r;++l){i.push("\\trowd\\trautofit1");for(var c=s.s.c;c<=s.e.c;++c)i.push("\\cellx"+(c+1));for(i.push("\\pard\\intbl"),c=s.s.c;c<=s.e.c;++c){var v=Ee({r:l,c});f=o?(a[l]||[])[c]:a[v],!(!f||f.v==null&&(!f.f||f.F))&&(i.push(" "+(f.w||(kr(f),f.w))),i.push("\\cell"))}i.push("\\pard\\intbl\\row")}return i.join("")+"}"}return{to_workbook:r,to_sheet:e,from_sheet:n}}();function ra(e){for(var t=0,r=1;t!=3;++t)r=r*256+(e[t]>255?255:e[t]<0?0:e[t]);return r.toString(16).toUpperCase().slice(1)}var jl=6,Rr=jl;function on(e){return Math.floor((e+Math.round(128/Rr)/256)*Rr)}function cn(e){return Math.floor((e-5)/Rr*100+.5)/100}function Wn(e){return Math.round((e*Rr+5)/Rr*256)/256}function r0(e){e.width?(e.wpx=on(e.width),e.wch=cn(e.wpx),e.MDW=Rr):e.wpx?(e.wch=cn(e.wpx),e.width=Wn(e.wch),e.MDW=Rr):typeof e.wch=="number"&&(e.width=Wn(e.wch),e.wpx=on(e.width),e.MDW=Rr),e.customWidth&&delete e.customWidth}var Yl=96,xi=Yl;function hn(e){return e*96/xi}function di(e){return e*xi/96}function Jl(e){var t=["<numFmts>"];return[[5,8],[23,26],[41,44],[50,392]].forEach(function(r){for(var n=r[0];n<=r[1];++n)e[n]!=null&&(t[t.length]=J("numFmt",null,{numFmtId:n,formatCode:Te(e[n])}))}),t.length===1?"":(t[t.length]="</numFmts>",t[0]=J("numFmts",null,{count:t.length-2}).replace("/>",">"),t.join(""))}function Zl(e){var t=[];return t[t.length]=J("cellXfs",null),e.forEach(function(r){t[t.length]=J("xf",null,r)}),t[t.length]="</cellXfs>",t.length===2?"":(t[0]=J("cellXfs",null,{count:t.length-2}).replace("/>",">"),t.join(""))}function vi(e,t){var r=[Ie,J("styleSheet",null,{xmlns:xt[0],"xmlns:vt":Le.vt})],n;return e.SSF&&(n=Jl(e.SSF))!=null&&(r[r.length]=n),r[r.length]='<fonts count="1"><font><sz val="12"/><color theme="1"/><name val="Calibri"/><family val="2"/><scheme val="minor"/></font></fonts>',r[r.length]='<fills count="2"><fill><patternFill patternType="none"/></fill><fill><patternFill patternType="gray125"/></fill></fills>',r[r.length]='<borders count="1"><border><left/><right/><top/><bottom/><diagonal/></border></borders>',r[r.length]='<cellStyleXfs count="1"><xf numFmtId="0" fontId="0" fillId="0" borderId="0"/></cellStyleXfs>',(n=Zl(t.cellXfs))&&(r[r.length]=n),r[r.length]='<cellStyles count="1"><cellStyle name="Normal" xfId="0" builtinId="0"/></cellStyles>',r[r.length]='<dxfs count="0"/>',r[r.length]='<tableStyles count="0" defaultTableStyle="TableStyleMedium9" defaultPivotStyle="PivotStyleMedium4"/>',r.length>2&&(r[r.length]="</styleSheet>",r[1]=r[1].replace("/>",">")),r.join("")}function ql(e,t){var r=e.read_shift(2),n=Ke(e);return[r,n]}function Ql(e,t,r){r||(r=b(6+4*t.length)),r.write_shift(2,e),Me(t,r);var n=r.length>r.l?r.slice(0,r.l):r;return r.l==null&&(r.l=r.length),n}function eo(e,t,r){var n={};n.sz=e.read_shift(2)/20;var a=Hf(e);a.fItalic&&(n.italic=1),a.fCondense&&(n.condense=1),a.fExtend&&(n.extend=1),a.fShadow&&(n.shadow=1),a.fOutline&&(n.outline=1),a.fStrikeout&&(n.strike=1);var i=e.read_shift(2);switch(i===700&&(n.bold=1),e.read_shift(2)){case 1:n.vertAlign="superscript";break;case 2:n.vertAlign="subscript";break}var s=e.read_shift(1);s!=0&&(n.underline=s);var f=e.read_shift(1);f>0&&(n.family=f);var o=e.read_shift(1);switch(o>0&&(n.charset=o),e.l++,n.color=Wf(e),e.read_shift(1)){case 1:n.scheme="major";break;case 2:n.scheme="minor";break}return n.name=Ke(e),n}function ro(e,t){t||(t=b(25+4*32)),t.write_shift(2,e.sz*20),Vf(e,t),t.write_shift(2,e.bold?700:400);var r=0;e.vertAlign=="superscript"?r=1:e.vertAlign=="subscript"&&(r=2),t.write_shift(2,r),t.write_shift(1,e.underline||0),t.write_shift(1,e.family||0),t.write_shift(1,e.charset||0),t.write_shift(1,0),fn(e.color,t);var n=0;return e.scheme=="major"&&(n=1),e.scheme=="minor"&&(n=2),t.write_shift(1,n),Me(e.name,t),t.length>t.l?t.slice(0,t.l):t}var to=["none","solid","mediumGray","darkGray","lightGray","darkHorizontal","darkVertical","darkDown","darkUp","darkGrid","darkTrellis","lightHorizontal","lightVertical","lightDown","lightUp","lightGrid","lightTrellis","gray125","gray0625"],In,no=Sr;function ta(e,t){t||(t=b(4*3+8*7+16*1)),In||(In=Gn(to));var r=In[e.patternType];r==null&&(r=40),t.write_shift(4,r);var n=0;if(r!=40)for(fn({auto:1},t),fn({auto:1},t);n<12;++n)t.write_shift(4,0);else{for(;n<4;++n)t.write_shift(4,0);for(;n<12;++n)t.write_shift(4,0)}return t.length>t.l?t.slice(0,t.l):t}function ao(e,t){var r=e.l+t,n=e.read_shift(2),a=e.read_shift(2);return e.l=r,{ixfe:n,numFmtId:a}}function pi(e,t,r){r||(r=b(16)),r.write_shift(2,t||0),r.write_shift(2,e.numFmtId||0),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(1,0),r.write_shift(1,0);var n=0;return r.write_shift(1,n),r.write_shift(1,0),r.write_shift(1,0),r.write_shift(1,0),r}function Tt(e,t){return t||(t=b(10)),t.write_shift(1,0),t.write_shift(1,0),t.write_shift(4,0),t.write_shift(4,0),t}var io=Sr;function so(e,t){return t||(t=b(51)),t.write_shift(1,0),Tt(null,t),Tt(null,t),Tt(null,t),Tt(null,t),Tt(null,t),t.length>t.l?t.slice(0,t.l):t}function fo(e,t){return t||(t=b(12+4*10)),t.write_shift(4,e.xfId),t.write_shift(2,1),t.write_shift(1,+e.builtinId),t.write_shift(1,0),sn(e.name||"",t),t.length>t.l?t.slice(0,t.l):t}function lo(e,t,r){var n=b(2052);return n.write_shift(4,e),sn(t,n),sn(r,n),n.length>n.l?n.slice(0,n.l):n}function oo(e,t){if(!!t){var r=0;[[5,8],[23,26],[41,44],[50,392]].forEach(function(n){for(var a=n[0];a<=n[1];++a)t[a]!=null&&++r}),r!=0&&(G(e,615,gr(r)),[[5,8],[23,26],[41,44],[50,392]].forEach(function(n){for(var a=n[0];a<=n[1];++a)t[a]!=null&&G(e,44,Ql(a,t[a]))}),G(e,616))}}function co(e){var t=1;G(e,611,gr(t)),G(e,43,ro({sz:12,color:{theme:1},name:"Calibri",family:2,scheme:"minor"})),G(e,612)}function ho(e){var t=2;G(e,603,gr(t)),G(e,45,ta({patternType:"none"})),G(e,45,ta({patternType:"gray125"})),G(e,604)}function uo(e){var t=1;G(e,613,gr(t)),G(e,46,so()),G(e,614)}function xo(e){var t=1;G(e,626,gr(t)),G(e,47,pi({numFmtId:0,fontId:0,fillId:0,borderId:0},65535)),G(e,627)}function vo(e,t){G(e,617,gr(t.length)),t.forEach(function(r){G(e,47,pi(r,0))}),G(e,618)}function po(e){var t=1;G(e,619,gr(t)),G(e,48,fo({xfId:0,builtinId:0,name:"Normal"})),G(e,620)}function mo(e){var t=0;G(e,505,gr(t)),G(e,506)}function go(e){var t=0;G(e,508,lo(t,"TableStyleMedium9","PivotStyleMedium4")),G(e,509)}function _o(e,t){var r=er();return G(r,278),oo(r,e.SSF),co(r),ho(r),uo(r),xo(r),vo(r,t.cellXfs),po(r),mo(r),go(r),G(r,279),r.end()}function mi(e,t){if(t&&t.themeXLSX)return t.themeXLSX;if(e&&typeof e.raw=="string")return e.raw;var r=[Ie];return r[r.length]='<a:theme xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" name="Office Theme">',r[r.length]="<a:themeElements>",r[r.length]='<a:clrScheme name="Office">',r[r.length]='<a:dk1><a:sysClr val="windowText" lastClr="000000"/></a:dk1>',r[r.length]='<a:lt1><a:sysClr val="window" lastClr="FFFFFF"/></a:lt1>',r[r.length]='<a:dk2><a:srgbClr val="1F497D"/></a:dk2>',r[r.length]='<a:lt2><a:srgbClr val="EEECE1"/></a:lt2>',r[r.length]='<a:accent1><a:srgbClr val="4F81BD"/></a:accent1>',r[r.length]='<a:accent2><a:srgbClr val="C0504D"/></a:accent2>',r[r.length]='<a:accent3><a:srgbClr val="9BBB59"/></a:accent3>',r[r.length]='<a:accent4><a:srgbClr val="8064A2"/></a:accent4>',r[r.length]='<a:accent5><a:srgbClr val="4BACC6"/></a:accent5>',r[r.length]='<a:accent6><a:srgbClr val="F79646"/></a:accent6>',r[r.length]='<a:hlink><a:srgbClr val="0000FF"/></a:hlink>',r[r.length]='<a:folHlink><a:srgbClr val="800080"/></a:folHlink>',r[r.length]="</a:clrScheme>",r[r.length]='<a:fontScheme name="Office">',r[r.length]="<a:majorFont>",r[r.length]='<a:latin typeface="Cambria"/>',r[r.length]='<a:ea typeface=""/>',r[r.length]='<a:cs typeface=""/>',r[r.length]='<a:font script="Jpan" typeface="\uFF2D\uFF33 \uFF30\u30B4\u30B7\u30C3\u30AF"/>',r[r.length]='<a:font script="Hang" typeface="\uB9D1\uC740 \uACE0\uB515"/>',r[r.length]='<a:font script="Hans" typeface="\u5B8B\u4F53"/>',r[r.length]='<a:font script="Hant" typeface="\u65B0\u7D30\u660E\u9AD4"/>',r[r.length]='<a:font script="Arab" typeface="Times New Roman"/>',r[r.length]='<a:font script="Hebr" typeface="Times New Roman"/>',r[r.length]='<a:font script="Thai" typeface="Tahoma"/>',r[r.length]='<a:font script="Ethi" typeface="Nyala"/>',r[r.length]='<a:font script="Beng" typeface="Vrinda"/>',r[r.length]='<a:font script="Gujr" typeface="Shruti"/>',r[r.length]='<a:font script="Khmr" typeface="MoolBoran"/>',r[r.length]='<a:font script="Knda" typeface="Tunga"/>',r[r.length]='<a:font script="Guru" typeface="Raavi"/>',r[r.length]='<a:font script="Cans" typeface="Euphemia"/>',r[r.length]='<a:font script="Cher" typeface="Plantagenet Cherokee"/>',r[r.length]='<a:font script="Yiii" typeface="Microsoft Yi Baiti"/>',r[r.length]='<a:font script="Tibt" typeface="Microsoft Himalaya"/>',r[r.length]='<a:font script="Thaa" typeface="MV Boli"/>',r[r.length]='<a:font script="Deva" typeface="Mangal"/>',r[r.length]='<a:font script="Telu" typeface="Gautami"/>',r[r.length]='<a:font script="Taml" typeface="Latha"/>',r[r.length]='<a:font script="Syrc" typeface="Estrangelo Edessa"/>',r[r.length]='<a:font script="Orya" typeface="Kalinga"/>',r[r.length]='<a:font script="Mlym" typeface="Kartika"/>',r[r.length]='<a:font script="Laoo" typeface="DokChampa"/>',r[r.length]='<a:font script="Sinh" typeface="Iskoola Pota"/>',r[r.length]='<a:font script="Mong" typeface="Mongolian Baiti"/>',r[r.length]='<a:font script="Viet" typeface="Times New Roman"/>',r[r.length]='<a:font script="Uigh" typeface="Microsoft Uighur"/>',r[r.length]='<a:font script="Geor" typeface="Sylfaen"/>',r[r.length]="</a:majorFont>",r[r.length]="<a:minorFont>",r[r.length]='<a:latin typeface="Calibri"/>',r[r.length]='<a:ea typeface=""/>',r[r.length]='<a:cs typeface=""/>',r[r.length]='<a:font script="Jpan" typeface="\uFF2D\uFF33 \uFF30\u30B4\u30B7\u30C3\u30AF"/>',r[r.length]='<a:font script="Hang" typeface="\uB9D1\uC740 \uACE0\uB515"/>',r[r.length]='<a:font script="Hans" typeface="\u5B8B\u4F53"/>',r[r.length]='<a:font script="Hant" typeface="\u65B0\u7D30\u660E\u9AD4"/>',r[r.length]='<a:font script="Arab" typeface="Arial"/>',r[r.length]='<a:font script="Hebr" typeface="Arial"/>',r[r.length]='<a:font script="Thai" typeface="Tahoma"/>',r[r.length]='<a:font script="Ethi" typeface="Nyala"/>',r[r.length]='<a:font script="Beng" typeface="Vrinda"/>',r[r.length]='<a:font script="Gujr" typeface="Shruti"/>',r[r.length]='<a:font script="Khmr" typeface="DaunPenh"/>',r[r.length]='<a:font script="Knda" typeface="Tunga"/>',r[r.length]='<a:font script="Guru" typeface="Raavi"/>',r[r.length]='<a:font script="Cans" typeface="Euphemia"/>',r[r.length]='<a:font script="Cher" typeface="Plantagenet Cherokee"/>',r[r.length]='<a:font script="Yiii" typeface="Microsoft Yi Baiti"/>',r[r.length]='<a:font script="Tibt" typeface="Microsoft Himalaya"/>',r[r.length]='<a:font script="Thaa" typeface="MV Boli"/>',r[r.length]='<a:font script="Deva" typeface="Mangal"/>',r[r.length]='<a:font script="Telu" typeface="Gautami"/>',r[r.length]='<a:font script="Taml" typeface="Latha"/>',r[r.length]='<a:font script="Syrc" typeface="Estrangelo Edessa"/>',r[r.length]='<a:font script="Orya" typeface="Kalinga"/>',r[r.length]='<a:font script="Mlym" typeface="Kartika"/>',r[r.length]='<a:font script="Laoo" typeface="DokChampa"/>',r[r.length]='<a:font script="Sinh" typeface="Iskoola Pota"/>',r[r.length]='<a:font script="Mong" typeface="Mongolian Baiti"/>',r[r.length]='<a:font script="Viet" typeface="Arial"/>',r[r.length]='<a:font script="Uigh" typeface="Microsoft Uighur"/>',r[r.length]='<a:font script="Geor" typeface="Sylfaen"/>',r[r.length]="</a:minorFont>",r[r.length]="</a:fontScheme>",r[r.length]='<a:fmtScheme name="Office">',r[r.length]="<a:fillStyleLst>",r[r.length]='<a:solidFill><a:schemeClr val="phClr"/></a:solidFill>',r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="50000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="35000"><a:schemeClr val="phClr"><a:tint val="37000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:tint val="15000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:lin ang="16200000" scaled="1"/>',r[r.length]="</a:gradFill>",r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="100000"/><a:shade val="100000"/><a:satMod val="130000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:tint val="50000"/><a:shade val="100000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:lin ang="16200000" scaled="0"/>',r[r.length]="</a:gradFill>",r[r.length]="</a:fillStyleLst>",r[r.length]="<a:lnStyleLst>",r[r.length]='<a:ln w="9525" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"><a:shade val="95000"/><a:satMod val="105000"/></a:schemeClr></a:solidFill><a:prstDash val="solid"/></a:ln>',r[r.length]='<a:ln w="25400" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:prstDash val="solid"/></a:ln>',r[r.length]='<a:ln w="38100" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:prstDash val="solid"/></a:ln>',r[r.length]="</a:lnStyleLst>",r[r.length]="<a:effectStyleLst>",r[r.length]="<a:effectStyle>",r[r.length]="<a:effectLst>",r[r.length]='<a:outerShdw blurRad="40000" dist="20000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="38000"/></a:srgbClr></a:outerShdw>',r[r.length]="</a:effectLst>",r[r.length]="</a:effectStyle>",r[r.length]="<a:effectStyle>",r[r.length]="<a:effectLst>",r[r.length]='<a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="35000"/></a:srgbClr></a:outerShdw>',r[r.length]="</a:effectLst>",r[r.length]="</a:effectStyle>",r[r.length]="<a:effectStyle>",r[r.length]="<a:effectLst>",r[r.length]='<a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="35000"/></a:srgbClr></a:outerShdw>',r[r.length]="</a:effectLst>",r[r.length]='<a:scene3d><a:camera prst="orthographicFront"><a:rot lat="0" lon="0" rev="0"/></a:camera><a:lightRig rig="threePt" dir="t"><a:rot lat="0" lon="0" rev="1200000"/></a:lightRig></a:scene3d>',r[r.length]='<a:sp3d><a:bevelT w="63500" h="25400"/></a:sp3d>',r[r.length]="</a:effectStyle>",r[r.length]="</a:effectStyleLst>",r[r.length]="<a:bgFillStyleLst>",r[r.length]='<a:solidFill><a:schemeClr val="phClr"/></a:solidFill>',r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="40000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="40000"><a:schemeClr val="phClr"><a:tint val="45000"/><a:shade val="99000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:shade val="20000"/><a:satMod val="255000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:path path="circle"><a:fillToRect l="50000" t="-80000" r="50000" b="180000"/></a:path>',r[r.length]="</a:gradFill>",r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="80000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:shade val="30000"/><a:satMod val="200000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:path path="circle"><a:fillToRect l="50000" t="50000" r="50000" b="50000"/></a:path>',r[r.length]="</a:gradFill>",r[r.length]="</a:bgFillStyleLst>",r[r.length]="</a:fmtScheme>",r[r.length]="</a:themeElements>",r[r.length]="<a:objectDefaults>",r[r.length]="<a:spDef>",r[r.length]='<a:spPr/><a:bodyPr/><a:lstStyle/><a:style><a:lnRef idx="1"><a:schemeClr val="accent1"/></a:lnRef><a:fillRef idx="3"><a:schemeClr val="accent1"/></a:fillRef><a:effectRef idx="2"><a:schemeClr val="accent1"/></a:effectRef><a:fontRef idx="minor"><a:schemeClr val="lt1"/></a:fontRef></a:style>',r[r.length]="</a:spDef>",r[r.length]="<a:lnDef>",r[r.length]='<a:spPr/><a:bodyPr/><a:lstStyle/><a:style><a:lnRef idx="2"><a:schemeClr val="accent1"/></a:lnRef><a:fillRef idx="0"><a:schemeClr val="accent1"/></a:fillRef><a:effectRef idx="1"><a:schemeClr val="accent1"/></a:effectRef><a:fontRef idx="minor"><a:schemeClr val="tx1"/></a:fontRef></a:style>',r[r.length]="</a:lnDef>",r[r.length]="</a:objectDefaults>",r[r.length]="<a:extraClrSchemeLst/>",r[r.length]="</a:theme>",r.join("")}function To(e,t){return{flags:e.read_shift(4),version:e.read_shift(4),name:Ke(e)}}function Eo(e){var t=b(12+2*e.name.length);return t.write_shift(4,e.flags),t.write_shift(4,e.version),Me(e.name,t),t.slice(0,t.l)}function wo(e){for(var t=[],r=e.read_shift(4);r-- >0;)t.push([e.read_shift(4),e.read_shift(4)]);return t}function So(e){var t=b(4+8*e.length);t.write_shift(4,e.length);for(var r=0;r<e.length;++r)t.write_shift(4,e[r][0]),t.write_shift(4,e[r][1]);return t}function Ao(e,t){var r=b(8+2*t.length);return r.write_shift(4,e),Me(t,r),r.slice(0,r.l)}function Fo(e){return e.l+=4,e.read_shift(4)!=0}function yo(e,t){var r=b(8);return r.write_shift(4,e),r.write_shift(4,t?1:0),r}function Co(){var e=er();return G(e,332),G(e,334,gr(1)),G(e,335,Eo({name:"XLDAPR",version:12e4,flags:3496657072})),G(e,336),G(e,339,Ao(1,"XLDAPR")),G(e,52),G(e,35,gr(514)),G(e,4096,gr(0)),G(e,4097,hr(1)),G(e,36),G(e,53),G(e,340),G(e,337,yo(1,!0)),G(e,51,So([[1,0]])),G(e,338),G(e,333),e.end()}function gi(){var e=[Ie];return e.push(`<metadata xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main" xmlns:xlrd="http://schemas.microsoft.com/office/spreadsheetml/2017/richdata" xmlns:xda="http://schemas.microsoft.com/office/spreadsheetml/2017/dynamicarray">
  <metadataTypes count="1">
    <metadataType name="XLDAPR" minSupportedVersion="120000" copy="1" pasteAll="1" pasteValues="1" merge="1" splitFirst="1" rowColShift="1" clearFormats="1" clearComments="1" assign="1" coerce="1" cellMeta="1"/>
  </metadataTypes>
  <futureMetadata name="XLDAPR" count="1">
    <bk>
      <extLst>
        <ext uri="{bdbb8cdc-fa1e-496e-a857-3c3f30c029c3}">
          <xda:dynamicArrayProperties fDynamic="1" fCollapsed="0"/>
        </ext>
      </extLst>
    </bk>
  </futureMetadata>
  <cellMetadata count="1">
    <bk>
      <rc t="1" v="0"/>
    </bk>
  </cellMetadata>
</metadata>`),e.join("")}function Oo(e){var t={};t.i=e.read_shift(4);var r={};r.r=e.read_shift(4),r.c=e.read_shift(4),t.r=Ee(r);var n=e.read_shift(1);return n&2&&(t.l="1"),n&8&&(t.a="1"),t}var ft=1024;function _i(e,t){for(var r=[21600,21600],n=["m0,0l0",r[1],r[0],r[1],r[0],"0xe"].join(","),a=[J("xml",null,{"xmlns:v":ir.v,"xmlns:o":ir.o,"xmlns:x":ir.x,"xmlns:mv":ir.mv}).replace(/\/>/,">"),J("o:shapelayout",J("o:idmap",null,{"v:ext":"edit",data:e}),{"v:ext":"edit"}),J("v:shapetype",[J("v:stroke",null,{joinstyle:"miter"}),J("v:path",null,{gradientshapeok:"t","o:connecttype":"rect"})].join(""),{id:"_x0000_t202","o:spt":202,coordsize:r.join(","),path:n})];ft<e*1e3;)ft+=1e3;return t.forEach(function(i){var s=Be(i[0]),f={color2:"#BEFF82",type:"gradient"};f.type=="gradient"&&(f.angle="-180");var o=f.type=="gradient"?J("o:fill",null,{type:"gradientUnscaled","v:ext":"view"}):null,l=J("v:fill",o,f),c={on:"t",obscured:"t"};++ft,a=a.concat(["<v:shape"+Nt({id:"_x0000_s"+ft,type:"#_x0000_t202",style:"position:absolute; margin-left:80pt;margin-top:5pt;width:104pt;height:64pt;z-index:10"+(i[1].hidden?";visibility:hidden":""),fillcolor:"#ECFAD4",strokecolor:"#edeaa1"})+">",l,J("v:shadow",null,c),J("v:path",null,{"o:connecttype":"none"}),'<v:textbox><div style="text-align:left"></div></v:textbox>','<x:ClientData ObjectType="Note">',"<x:MoveWithCells/>","<x:SizeWithCells/>",Ve("x:Anchor",[s.c+1,0,s.r+1,0,s.c+3,20,s.r+5,20].join(",")),Ve("x:AutoFill","False"),Ve("x:Row",String(s.r)),Ve("x:Column",String(s.c)),i[1].hidden?"":"<x:Visible/>","</x:ClientData>","</v:shape>"])}),a.push("</xml>"),a.join("")}function Ti(e){var t=[Ie,J("comments",null,{xmlns:xt[0]})],r=[];return t.push("<authors>"),e.forEach(function(n){n[1].forEach(function(a){var i=Te(a.a);r.indexOf(i)==-1&&(r.push(i),t.push("<author>"+i+"</author>")),a.T&&a.ID&&r.indexOf("tc="+a.ID)==-1&&(r.push("tc="+a.ID),t.push("<author>tc="+a.ID+"</author>"))})}),r.length==0&&(r.push("SheetJ5"),t.push("<author>SheetJ5</author>")),t.push("</authors>"),t.push("<commentList>"),e.forEach(function(n){var a=0,i=[];if(n[1][0]&&n[1][0].T&&n[1][0].ID?a=r.indexOf("tc="+n[1][0].ID):n[1].forEach(function(o){o.a&&(a=r.indexOf(Te(o.a))),i.push(o.t||"")}),t.push('<comment ref="'+n[0]+'" authorId="'+a+'"><text>'),i.length<=1)t.push(Ve("t",Te(i[0]||"")));else{for(var s=`Comment:
    `+i[0]+`
`,f=1;f<i.length;++f)s+=`Reply:
    `+i[f]+`
`;t.push(Ve("t",Te(s)))}t.push("</text></comment>")}),t.push("</commentList>"),t.length>2&&(t[t.length]="</comments>",t[1]=t[1].replace("/>",">")),t.join("")}function Do(e,t,r){var n=[Ie,J("ThreadedComments",null,{xmlns:Le.TCMNT}).replace(/[\/]>/,">")];return e.forEach(function(a){var i="";(a[1]||[]).forEach(function(s,f){if(!s.T){delete s.ID;return}s.a&&t.indexOf(s.a)==-1&&t.push(s.a);var o={ref:a[0],id:"{54EE7951-7262-4200-6969-"+("000000000000"+r.tcid++).slice(-12)+"}"};f==0?i=o.id:o.parentId=i,s.ID=o.id,s.a&&(o.personId="{54EE7950-7262-4200-6969-"+("000000000000"+t.indexOf(s.a)).slice(-12)+"}"),n.push(J("threadedComment",Ve("text",s.t||""),o))})}),n.push("</ThreadedComments>"),n.join("")}function Ro(e){var t=[Ie,J("personList",null,{xmlns:Le.TCMNT,"xmlns:x":xt[0]}).replace(/[\/]>/,">")];return e.forEach(function(r,n){t.push(J("person",null,{displayName:r,id:"{54EE7950-7262-4200-6969-"+("000000000000"+n).slice(-12)+"}",userId:r,providerId:"None"}))}),t.push("</personList>"),t.join("")}function No(e){var t={};t.iauthor=e.read_shift(4);var r=et(e);return t.rfx=r.s,t.ref=Ee(r.s),e.l+=16,t}function ko(e,t){return t==null&&(t=b(36)),t.write_shift(4,e[1].iauthor),vt(e[0],t),t.write_shift(4,0),t.write_shift(4,0),t.write_shift(4,0),t.write_shift(4,0),t}var Io=Ke;function Po(e){return Me(e.slice(0,54))}function Lo(e){var t=er(),r=[];return G(t,628),G(t,630),e.forEach(function(n){n[1].forEach(function(a){r.indexOf(a.a)>-1||(r.push(a.a.slice(0,54)),G(t,632,Po(a.a)))})}),G(t,631),G(t,633),e.forEach(function(n){n[1].forEach(function(a){a.iauthor=r.indexOf(a.a);var i={s:Be(n[0]),e:Be(n[0])};G(t,635,ko([i,a])),a.t&&a.t.length>0&&G(t,637,Bf(a)),G(t,636),delete a.iauthor})}),G(t,634),G(t,629),t.end()}function Bo(e,t){t.FullPaths.forEach(function(r,n){if(n!=0){var a=r.replace(/[^\/]*[\/]/,"/_VBA_PROJECT_CUR/");a.slice(-1)!=="/"&&we.utils.cfb_add(e,a,t.FileIndex[n].content)}})}var Ei=["xlsb","xlsm","xlam","biff8","xla"],Mo=function(){var e=/(^|[^A-Za-z_])R(\[?-?\d+\]|[1-9]\d*|)C(\[?-?\d+\]|[1-9]\d*|)(?![A-Za-z0-9_])/g,t={r:0,c:0};function r(n,a,i,s){var f=!1,o=!1;i.length==0?o=!0:i.charAt(0)=="["&&(o=!0,i=i.slice(1,-1)),s.length==0?f=!0:s.charAt(0)=="["&&(f=!0,s=s.slice(1,-1));var l=i.length>0?parseInt(i,10)|0:0,c=s.length>0?parseInt(s,10)|0:0;return f?c+=t.c:--c,o?l+=t.r:--l,a+(f?"":"$")+ze(c)+(o?"":"$")+Ge(l)}return function(a,i){return t=i,a.replace(e,r)}}(),t0=/(^|[^._A-Z0-9])([$]?)([A-Z]{1,2}|[A-W][A-Z]{2}|X[A-E][A-Z]|XF[A-D])([$]?)(10[0-3]\d{4}|104[0-7]\d{3}|1048[0-4]\d{2}|10485[0-6]\d|104857[0-6]|[1-9]\d{0,5})(?![_.\(A-Za-z0-9])/g,n0=function(){return function(t,r){return t.replace(t0,function(n,a,i,s,f,o){var l=Jn(s)-(i?0:r.c),c=Yn(o)-(f?0:r.r),v=c==0?"":f?c+1:"["+c+"]",x=l==0?"":i?l+1:"["+l+"]";return a+"R"+v+"C"+x})}}();function Uo(e,t){return e.replace(t0,function(r,n,a,i,s,f){return n+(a=="$"?a+i:ze(Jn(i)+t.c))+(s=="$"?s+f:Ge(Yn(f)+t.r))})}function bo(e){return e.length!=1}function Ne(e){e.l+=1}function Ur(e,t){var r=e.read_shift(t==1?1:2);return[r&16383,r>>14&1,r>>15&1]}function wi(e,t,r){var n=2;if(r){if(r.biff>=2&&r.biff<=5)return Si(e);r.biff==12&&(n=4)}var a=e.read_shift(n),i=e.read_shift(n),s=Ur(e,2),f=Ur(e,2);return{s:{r:a,c:s[0],cRel:s[1],rRel:s[2]},e:{r:i,c:f[0],cRel:f[1],rRel:f[2]}}}function Si(e){var t=Ur(e,2),r=Ur(e,2),n=e.read_shift(1),a=e.read_shift(1);return{s:{r:t[0],c:n,cRel:t[1],rRel:t[2]},e:{r:r[0],c:a,cRel:r[1],rRel:r[2]}}}function Wo(e,t,r){if(r.biff<8)return Si(e);var n=e.read_shift(r.biff==12?4:2),a=e.read_shift(r.biff==12?4:2),i=Ur(e,2),s=Ur(e,2);return{s:{r:n,c:i[0],cRel:i[1],rRel:i[2]},e:{r:a,c:s[0],cRel:s[1],rRel:s[2]}}}function Ai(e,t,r){if(r&&r.biff>=2&&r.biff<=5)return Ho(e);var n=e.read_shift(r&&r.biff==12?4:2),a=Ur(e,2);return{r:n,c:a[0],cRel:a[1],rRel:a[2]}}function Ho(e){var t=Ur(e,2),r=e.read_shift(1);return{r:t[0],c:r,cRel:t[1],rRel:t[2]}}function Vo(e){var t=e.read_shift(2),r=e.read_shift(2);return{r:t,c:r&255,fQuoted:!!(r&16384),cRel:r>>15,rRel:r>>15}}function Go(e,t,r){var n=r&&r.biff?r.biff:8;if(n>=2&&n<=5)return Xo(e);var a=e.read_shift(n>=12?4:2),i=e.read_shift(2),s=(i&16384)>>14,f=(i&32768)>>15;if(i&=16383,f==1)for(;a>524287;)a-=1048576;if(s==1)for(;i>8191;)i=i-16384;return{r:a,c:i,cRel:s,rRel:f}}function Xo(e){var t=e.read_shift(2),r=e.read_shift(1),n=(t&32768)>>15,a=(t&16384)>>14;return t&=16383,n==1&&t>=8192&&(t=t-16384),a==1&&r>=128&&(r=r-256),{r:t,c:r,cRel:a,rRel:n}}function $o(e,t,r){var n=(e[e.l++]&96)>>5,a=wi(e,r.biff>=2&&r.biff<=5?6:8,r);return[n,a]}function zo(e,t,r){var n=(e[e.l++]&96)>>5,a=e.read_shift(2,"i"),i=8;if(r)switch(r.biff){case 5:e.l+=12,i=6;break;case 12:i=12;break}var s=wi(e,i,r);return[n,a,s]}function Ko(e,t,r){var n=(e[e.l++]&96)>>5;return e.l+=r&&r.biff>8?12:r.biff<8?6:8,[n]}function jo(e,t,r){var n=(e[e.l++]&96)>>5,a=e.read_shift(2),i=8;if(r)switch(r.biff){case 5:e.l+=12,i=6;break;case 12:i=12;break}return e.l+=i,[n,a]}function Yo(e,t,r){var n=(e[e.l++]&96)>>5,a=Wo(e,t-1,r);return[n,a]}function Jo(e,t,r){var n=(e[e.l++]&96)>>5;return e.l+=r.biff==2?6:r.biff==12?14:7,[n]}function na(e){var t=e[e.l+1]&1,r=1;return e.l+=4,[t,r]}function Zo(e,t,r){e.l+=2;for(var n=e.read_shift(r&&r.biff==2?1:2),a=[],i=0;i<=n;++i)a.push(e.read_shift(r&&r.biff==2?1:2));return a}function qo(e,t,r){var n=e[e.l+1]&255?1:0;return e.l+=2,[n,e.read_shift(r&&r.biff==2?1:2)]}function Qo(e,t,r){var n=e[e.l+1]&255?1:0;return e.l+=2,[n,e.read_shift(r&&r.biff==2?1:2)]}function ec(e){var t=e[e.l+1]&255?1:0;return e.l+=2,[t,e.read_shift(2)]}function rc(e,t,r){var n=e[e.l+1]&255?1:0;return e.l+=r&&r.biff==2?3:4,[n]}function Fi(e){var t=e.read_shift(1),r=e.read_shift(1);return[t,r]}function tc(e){return e.read_shift(2),Fi(e)}function nc(e){return e.read_shift(2),Fi(e)}function ac(e,t,r){var n=(e[e.l]&96)>>5;e.l+=1;var a=Ai(e,0,r);return[n,a]}function ic(e,t,r){var n=(e[e.l]&96)>>5;e.l+=1;var a=Go(e,0,r);return[n,a]}function sc(e,t,r){var n=(e[e.l]&96)>>5;e.l+=1;var a=e.read_shift(2);r&&r.biff==5&&(e.l+=12);var i=Ai(e,0,r);return[n,a,i]}function fc(e,t,r){var n=(e[e.l]&96)>>5;e.l+=1;var a=e.read_shift(r&&r.biff<=3?1:2);return[f1[a],Oi[a],n]}function lc(e,t,r){var n=e[e.l++],a=e.read_shift(1),i=r&&r.biff<=3?[n==88?-1:0,e.read_shift(1)]:oc(e);return[a,(i[0]===0?Oi:s1)[i[1]]]}function oc(e){return[e[e.l+1]>>7,e.read_shift(2)&32767]}function cc(e,t,r){e.l+=r&&r.biff==2?3:4}function hc(e,t,r){if(e.l++,r&&r.biff==12)return[e.read_shift(4,"i"),0];var n=e.read_shift(2),a=e.read_shift(r&&r.biff==2?1:2);return[n,a]}function uc(e){return e.l++,Mt[e.read_shift(1)]}function xc(e){return e.l++,e.read_shift(2)}function dc(e){return e.l++,e.read_shift(1)!==0}function vc(e){return e.l++,pt(e)}function pc(e,t,r){return e.l++,fi(e,t-1,r)}function mc(e,t){var r=[e.read_shift(1)];if(t==12)switch(r[0]){case 2:r[0]=4;break;case 4:r[0]=16;break;case 0:r[0]=1;break;case 1:r[0]=2;break}switch(r[0]){case 4:r[1]=il(e,1)?"TRUE":"FALSE",t!=12&&(e.l+=7);break;case 37:case 16:r[1]=Mt[e[e.l]],e.l+=t==12?4:8;break;case 0:e.l+=8;break;case 1:r[1]=pt(e);break;case 2:r[1]=ol(e,0,{biff:t>0&&t<8?2:t});break;default:throw new Error("Bad SerAr: "+r[0])}return r}function gc(e,t,r){for(var n=e.read_shift(r.biff==12?4:2),a=[],i=0;i!=n;++i)a.push((r.biff==12?et:ul)(e));return a}function _c(e,t,r){var n=0,a=0;r.biff==12?(n=e.read_shift(4),a=e.read_shift(4)):(a=1+e.read_shift(1),n=1+e.read_shift(2)),r.biff>=2&&r.biff<8&&(--n,--a==0&&(a=256));for(var i=0,s=[];i!=n&&(s[i]=[]);++i)for(var f=0;f!=a;++f)s[i][f]=mc(e,r.biff);return s}function Tc(e,t,r){var n=e.read_shift(1)>>>5&3,a=!r||r.biff>=8?4:2,i=e.read_shift(a);switch(r.biff){case 2:e.l+=5;break;case 3:case 4:e.l+=8;break;case 5:e.l+=12;break}return[n,0,i]}function Ec(e,t,r){if(r.biff==5)return wc(e);var n=e.read_shift(1)>>>5&3,a=e.read_shift(2),i=e.read_shift(4);return[n,a,i]}function wc(e){var t=e.read_shift(1)>>>5&3,r=e.read_shift(2,"i");e.l+=8;var n=e.read_shift(2);return e.l+=12,[t,r,n]}function Sc(e,t,r){var n=e.read_shift(1)>>>5&3;e.l+=r&&r.biff==2?3:4;var a=e.read_shift(r&&r.biff==2?1:2);return[n,a]}function Ac(e,t,r){var n=e.read_shift(1)>>>5&3,a=e.read_shift(r&&r.biff==2?1:2);return[n,a]}function Fc(e,t,r){var n=e.read_shift(1)>>>5&3;return e.l+=4,r.biff<8&&e.l--,r.biff==12&&(e.l+=2),[n]}function yc(e,t,r){var n=(e[e.l++]&96)>>5,a=e.read_shift(2),i=4;if(r)switch(r.biff){case 5:i=15;break;case 12:i=6;break}return e.l+=i,[n,a]}var Cc=Sr,Oc=Sr,Dc=Sr;function Ut(e,t,r){return e.l+=2,[Vo(e)]}function a0(e){return e.l+=6,[]}var Rc=Ut,Nc=a0,kc=a0,Ic=Ut;function yi(e){return e.l+=2,[ii(e),e.read_shift(2)&1]}var Pc=Ut,Lc=yi,Bc=a0,Mc=Ut,Uc=Ut,bc=["Data","All","Headers","??","?Data2","??","?DataHeaders","??","Totals","??","??","??","?DataTotals","??","??","??","?Current"];function Wc(e){e.l+=2;var t=e.read_shift(2),r=e.read_shift(2),n=e.read_shift(4),a=e.read_shift(2),i=e.read_shift(2),s=bc[r>>2&31];return{ixti:t,coltype:r&3,rt:s,idx:n,c:a,C:i}}function Hc(e){return e.l+=2,[e.read_shift(4)]}function Vc(e,t,r){return e.l+=5,e.l+=2,e.l+=r.biff==2?1:4,["PTGSHEET"]}function Gc(e,t,r){return e.l+=r.biff==2?4:5,["PTGENDSHEET"]}function Xc(e){var t=e.read_shift(1)>>>5&3,r=e.read_shift(2);return[t,r]}function $c(e){var t=e.read_shift(1)>>>5&3,r=e.read_shift(2);return[t,r]}function zc(e){return e.l+=4,[0,0]}var aa={1:{n:"PtgExp",f:hc},2:{n:"PtgTbl",f:Dc},3:{n:"PtgAdd",f:Ne},4:{n:"PtgSub",f:Ne},5:{n:"PtgMul",f:Ne},6:{n:"PtgDiv",f:Ne},7:{n:"PtgPower",f:Ne},8:{n:"PtgConcat",f:Ne},9:{n:"PtgLt",f:Ne},10:{n:"PtgLe",f:Ne},11:{n:"PtgEq",f:Ne},12:{n:"PtgGe",f:Ne},13:{n:"PtgGt",f:Ne},14:{n:"PtgNe",f:Ne},15:{n:"PtgIsect",f:Ne},16:{n:"PtgUnion",f:Ne},17:{n:"PtgRange",f:Ne},18:{n:"PtgUplus",f:Ne},19:{n:"PtgUminus",f:Ne},20:{n:"PtgPercent",f:Ne},21:{n:"PtgParen",f:Ne},22:{n:"PtgMissArg",f:Ne},23:{n:"PtgStr",f:pc},26:{n:"PtgSheet",f:Vc},27:{n:"PtgEndSheet",f:Gc},28:{n:"PtgErr",f:uc},29:{n:"PtgBool",f:dc},30:{n:"PtgInt",f:xc},31:{n:"PtgNum",f:vc},32:{n:"PtgArray",f:Jo},33:{n:"PtgFunc",f:fc},34:{n:"PtgFuncVar",f:lc},35:{n:"PtgName",f:Tc},36:{n:"PtgRef",f:ac},37:{n:"PtgArea",f:$o},38:{n:"PtgMemArea",f:Sc},39:{n:"PtgMemErr",f:Cc},40:{n:"PtgMemNoMem",f:Oc},41:{n:"PtgMemFunc",f:Ac},42:{n:"PtgRefErr",f:Fc},43:{n:"PtgAreaErr",f:Ko},44:{n:"PtgRefN",f:ic},45:{n:"PtgAreaN",f:Yo},46:{n:"PtgMemAreaN",f:Xc},47:{n:"PtgMemNoMemN",f:$c},57:{n:"PtgNameX",f:Ec},58:{n:"PtgRef3d",f:sc},59:{n:"PtgArea3d",f:zo},60:{n:"PtgRefErr3d",f:yc},61:{n:"PtgAreaErr3d",f:jo},255:{}},Kc={64:32,96:32,65:33,97:33,66:34,98:34,67:35,99:35,68:36,100:36,69:37,101:37,70:38,102:38,71:39,103:39,72:40,104:40,73:41,105:41,74:42,106:42,75:43,107:43,76:44,108:44,77:45,109:45,78:46,110:46,79:47,111:47,88:34,120:34,89:57,121:57,90:58,122:58,91:59,123:59,92:60,124:60,93:61,125:61},jc={1:{n:"PtgElfLel",f:yi},2:{n:"PtgElfRw",f:Mc},3:{n:"PtgElfCol",f:Rc},6:{n:"PtgElfRwV",f:Uc},7:{n:"PtgElfColV",f:Ic},10:{n:"PtgElfRadical",f:Pc},11:{n:"PtgElfRadicalS",f:Bc},13:{n:"PtgElfColS",f:Nc},15:{n:"PtgElfColSV",f:kc},16:{n:"PtgElfRadicalLel",f:Lc},25:{n:"PtgList",f:Wc},29:{n:"PtgSxName",f:Hc},255:{}},Yc={0:{n:"PtgAttrNoop",f:zc},1:{n:"PtgAttrSemi",f:rc},2:{n:"PtgAttrIf",f:Qo},4:{n:"PtgAttrChoose",f:Zo},8:{n:"PtgAttrGoto",f:qo},16:{n:"PtgAttrSum",f:cc},32:{n:"PtgAttrBaxcel",f:na},33:{n:"PtgAttrBaxcel",f:na},64:{n:"PtgAttrSpace",f:tc},65:{n:"PtgAttrSpaceSemi",f:nc},128:{n:"PtgAttrIfError",f:ec},255:{}};function Jc(e,t,r,n){if(n.biff<8)return Sr(e,t);for(var a=e.l+t,i=[],s=0;s!==r.length;++s)switch(r[s][0]){case"PtgArray":r[s][1]=_c(e,0,n),i.push(r[s][1]);break;case"PtgMemArea":r[s][2]=gc(e,r[s][1],n),i.push(r[s][2]);break;case"PtgExp":n&&n.biff==12&&(r[s][1][1]=e.read_shift(4),i.push(r[s][1]));break;case"PtgList":case"PtgElfRadicalS":case"PtgElfColS":case"PtgElfColSV":throw"Unsupported "+r[s][0]}return t=a-e.l,t!==0&&i.push(Sr(e,t)),i}function Zc(e,t,r){for(var n=e.l+t,a,i,s=[];n!=e.l;)t=n-e.l,i=e[e.l],a=aa[i]||aa[Kc[i]],(i===24||i===25)&&(a=(i===24?jc:Yc)[e[e.l+1]]),!a||!a.f?Sr(e,t):s.push([a.n,a.f(e,t,r)]);return s}function qc(e){for(var t=[],r=0;r<e.length;++r){for(var n=e[r],a=[],i=0;i<n.length;++i){var s=n[i];if(s)switch(s[0]){case 2:a.push('"'+s[1].replace(/"/g,'""')+'"');break;default:a.push(s[1])}else a.push("")}t.push(a.join(","))}return t.join(";")}var Qc={PtgAdd:"+",PtgConcat:"&",PtgDiv:"/",PtgEq:"=",PtgGe:">=",PtgGt:">",PtgLe:"<=",PtgLt:"<",PtgMul:"*",PtgNe:"<>",PtgPower:"^",PtgSub:"-"};function e1(e,t){if(!e&&!(t&&t.biff<=5&&t.biff>=2))throw new Error("empty sheet name");return/[^\w\u4E00-\u9FFF\u3040-\u30FF]/.test(e)?"'"+e+"'":e}function Ci(e,t,r){if(!e)return"SH33TJSERR0";if(r.biff>8&&(!e.XTI||!e.XTI[t]))return e.SheetNames[t];if(!e.XTI)return"SH33TJSERR6";var n=e.XTI[t];if(r.biff<8)return t>1e4&&(t-=65536),t<0&&(t=-t),t==0?"":e.XTI[t-1];if(!n)return"SH33TJSERR1";var a="";if(r.biff>8)switch(e[n[0]][0]){case 357:return a=n[1]==-1?"#REF":e.SheetNames[n[1]],n[1]==n[2]?a:a+":"+e.SheetNames[n[2]];case 358:return r.SID!=null?e.SheetNames[r.SID]:"SH33TJSSAME"+e[n[0]][0];case 355:default:return"SH33TJSSRC"+e[n[0]][0]}switch(e[n[0]][0][0]){case 1025:return a=n[1]==-1?"#REF":e.SheetNames[n[1]]||"SH33TJSERR3",n[1]==n[2]?a:a+":"+e.SheetNames[n[2]];case 14849:return e[n[0]].slice(1).map(function(i){return i.Name}).join(";;");default:return e[n[0]][0][3]?(a=n[1]==-1?"#REF":e[n[0]][0][3][n[1]]||"SH33TJSERR4",n[1]==n[2]?a:a+":"+e[n[0]][0][3][n[2]]):"SH33TJSERR2"}}function ia(e,t,r){var n=Ci(e,t,r);return n=="#REF"?n:e1(n,r)}function ut(e,t,r,n,a){var i=a&&a.biff||8,s={s:{c:0,r:0},e:{c:0,r:0}},f=[],o,l,c,v=0,x=0,d,T="";if(!e[0]||!e[0][0])return"";for(var u=-1,g="",R=0,C=e[0].length;R<C;++R){var y=e[0][R];switch(y[0]){case"PtgUminus":f.push("-"+f.pop());break;case"PtgUplus":f.push("+"+f.pop());break;case"PtgPercent":f.push(f.pop()+"%");break;case"PtgAdd":case"PtgConcat":case"PtgDiv":case"PtgEq":case"PtgGe":case"PtgGt":case"PtgLe":case"PtgLt":case"PtgMul":case"PtgNe":case"PtgPower":case"PtgSub":if(o=f.pop(),l=f.pop(),u>=0){switch(e[0][u][1][0]){case 0:g=De(" ",e[0][u][1][1]);break;case 1:g=De("\r",e[0][u][1][1]);break;default:if(g="",a.WTF)throw new Error("Unexpected PtgAttrSpaceType "+e[0][u][1][0])}l=l+g,u=-1}f.push(l+Qc[y[0]]+o);break;case"PtgIsect":o=f.pop(),l=f.pop(),f.push(l+" "+o);break;case"PtgUnion":o=f.pop(),l=f.pop(),f.push(l+","+o);break;case"PtgRange":o=f.pop(),l=f.pop(),f.push(l+":"+o);break;case"PtgAttrChoose":break;case"PtgAttrGoto":break;case"PtgAttrIf":break;case"PtgAttrIfError":break;case"PtgRef":c=Ft(y[1][1],s,a),f.push(yt(c,i));break;case"PtgRefN":c=r?Ft(y[1][1],r,a):y[1][1],f.push(yt(c,i));break;case"PtgRef3d":v=y[1][1],c=Ft(y[1][2],s,a),T=ia(n,v,a),f.push(T+"!"+yt(c,i));break;case"PtgFunc":case"PtgFuncVar":var U=y[1][0],Y=y[1][1];U||(U=0),U&=127;var Q=U==0?[]:f.slice(-U);f.length-=U,Y==="User"&&(Y=Q.shift()),f.push(Y+"("+Q.join(",")+")");break;case"PtgBool":f.push(y[1]?"TRUE":"FALSE");break;case"PtgInt":f.push(y[1]);break;case"PtgNum":f.push(String(y[1]));break;case"PtgStr":f.push('"'+y[1].replace(/"/g,'""')+'"');break;case"PtgErr":f.push(y[1]);break;case"PtgAreaN":d=G0(y[1][1],r?{s:r}:s,a),f.push(Nn(d,a));break;case"PtgArea":d=G0(y[1][1],s,a),f.push(Nn(d,a));break;case"PtgArea3d":v=y[1][1],d=y[1][2],T=ia(n,v,a),f.push(T+"!"+Nn(d,a));break;case"PtgAttrSum":f.push("SUM("+f.pop()+")");break;case"PtgAttrBaxcel":case"PtgAttrSemi":break;case"PtgName":x=y[1][2];var O=(n.names||[])[x-1]||(n[0]||[])[x],V=O?O.Name:"SH33TJSNAME"+String(x);V&&V.slice(0,6)=="_xlfn."&&!a.xlfn&&(V=V.slice(6)),f.push(V);break;case"PtgNameX":var B=y[1][1];x=y[1][2];var I;if(a.biff<=5)B<0&&(B=-B),n[B]&&(I=n[B][x]);else{var M="";if(((n[B]||[])[0]||[])[0]==14849||(((n[B]||[])[0]||[])[0]==1025?n[B][x]&&n[B][x].itab>0&&(M=n.SheetNames[n[B][x].itab-1]+"!"):M=n.SheetNames[x-1]+"!"),n[B]&&n[B][x])M+=n[B][x].Name;else if(n[0]&&n[0][x])M+=n[0][x].Name;else{var H=(Ci(n,B,a)||"").split(";;");H[x-1]?M=H[x-1]:M+="SH33TJSERRX"}f.push(M);break}I||(I={Name:"SH33TJSERRY"}),f.push(I.Name);break;case"PtgParen":var K="(",ae=")";if(u>=0){switch(g="",e[0][u][1][0]){case 2:K=De(" ",e[0][u][1][1])+K;break;case 3:K=De("\r",e[0][u][1][1])+K;break;case 4:ae=De(" ",e[0][u][1][1])+ae;break;case 5:ae=De("\r",e[0][u][1][1])+ae;break;default:if(a.WTF)throw new Error("Unexpected PtgAttrSpaceType "+e[0][u][1][0])}u=-1}f.push(K+f.pop()+ae);break;case"PtgRefErr":f.push("#REF!");break;case"PtgRefErr3d":f.push("#REF!");break;case"PtgExp":c={c:y[1][1],r:y[1][0]};var ne={c:r.c,r:r.r};if(n.sharedf[Ee(c)]){var he=n.sharedf[Ee(c)];f.push(ut(he,s,ne,n,a))}else{var fe=!1;for(o=0;o!=n.arrayf.length;++o)if(l=n.arrayf[o],!(c.c<l[0].s.c||c.c>l[0].e.c)&&!(c.r<l[0].s.r||c.r>l[0].e.r)){f.push(ut(l[1],s,ne,n,a)),fe=!0;break}fe||f.push(y[1])}break;case"PtgArray":f.push("{"+qc(y[1])+"}");break;case"PtgMemArea":break;case"PtgAttrSpace":case"PtgAttrSpaceSemi":u=R;break;case"PtgTbl":break;case"PtgMemErr":break;case"PtgMissArg":f.push("");break;case"PtgAreaErr":f.push("#REF!");break;case"PtgAreaErr3d":f.push("#REF!");break;case"PtgList":f.push("Table"+y[1].idx+"[#"+y[1].rt+"]");break;case"PtgMemAreaN":case"PtgMemNoMemN":case"PtgAttrNoop":case"PtgSheet":case"PtgEndSheet":break;case"PtgMemFunc":break;case"PtgMemNoMem":break;case"PtgElfCol":case"PtgElfColS":case"PtgElfColSV":case"PtgElfColV":case"PtgElfLel":case"PtgElfRadical":case"PtgElfRadicalLel":case"PtgElfRadicalS":case"PtgElfRw":case"PtgElfRwV":throw new Error("Unsupported ELFs");case"PtgSxName":throw new Error("Unrecognized Formula Token: "+String(y));default:throw new Error("Unrecognized Formula Token: "+String(y))}var Fe=["PtgAttrSpace","PtgAttrSpaceSemi","PtgAttrGoto"];if(a.biff!=3&&u>=0&&Fe.indexOf(e[0][R][0])==-1){y=e[0][u];var pe=!0;switch(y[1][0]){case 4:pe=!1;case 0:g=De(" ",y[1][1]);break;case 5:pe=!1;case 1:g=De("\r",y[1][1]);break;default:if(g="",a.WTF)throw new Error("Unexpected PtgAttrSpaceType "+y[1][0])}f.push((pe?g:"")+f.pop()+(pe?"":g)),u=-1}}if(f.length>1&&a.WTF)throw new Error("bad formula stack");return f[0]}function r1(e){if(e==null){var t=b(8);return t.write_shift(1,3),t.write_shift(1,0),t.write_shift(2,0),t.write_shift(2,0),t.write_shift(2,65535),t}else if(typeof e=="number")return jr(e);return jr(0)}function t1(e,t,r,n,a){var i=Yr(t,r,a),s=r1(e.v),f=b(6),o=33;f.write_shift(2,o),f.write_shift(4,0);for(var l=b(e.bf.length),c=0;c<e.bf.length;++c)l[c]=e.bf[c];var v=He([i,s,f,l]);return v}function _n(e,t,r){var n=e.read_shift(4),a=Zc(e,n,r),i=e.read_shift(4),s=i>0?Jc(e,i,a,r):null;return[a,s]}var n1=_n,Tn=_n,a1=_n,i1=_n,s1={0:"BEEP",1:"OPEN",2:"OPEN.LINKS",3:"CLOSE.ALL",4:"SAVE",5:"SAVE.AS",6:"FILE.DELETE",7:"PAGE.SETUP",8:"PRINT",9:"PRINTER.SETUP",10:"QUIT",11:"NEW.WINDOW",12:"ARRANGE.ALL",13:"WINDOW.SIZE",14:"WINDOW.MOVE",15:"FULL",16:"CLOSE",17:"RUN",22:"SET.PRINT.AREA",23:"SET.PRINT.TITLES",24:"SET.PAGE.BREAK",25:"REMOVE.PAGE.BREAK",26:"FONT",27:"DISPLAY",28:"PROTECT.DOCUMENT",29:"PRECISION",30:"A1.R1C1",31:"CALCULATE.NOW",32:"CALCULATION",34:"DATA.FIND",35:"EXTRACT",36:"DATA.DELETE",37:"SET.DATABASE",38:"SET.CRITERIA",39:"SORT",40:"DATA.SERIES",41:"TABLE",42:"FORMAT.NUMBER",43:"ALIGNMENT",44:"STYLE",45:"BORDER",46:"CELL.PROTECTION",47:"COLUMN.WIDTH",48:"UNDO",49:"CUT",50:"COPY",51:"PASTE",52:"CLEAR",53:"PASTE.SPECIAL",54:"EDIT.DELETE",55:"INSERT",56:"FILL.RIGHT",57:"FILL.DOWN",61:"DEFINE.NAME",62:"CREATE.NAMES",63:"FORMULA.GOTO",64:"FORMULA.FIND",65:"SELECT.LAST.CELL",66:"SHOW.ACTIVE.CELL",67:"GALLERY.AREA",68:"GALLERY.BAR",69:"GALLERY.COLUMN",70:"GALLERY.LINE",71:"GALLERY.PIE",72:"GALLERY.SCATTER",73:"COMBINATION",74:"PREFERRED",75:"ADD.OVERLAY",76:"GRIDLINES",77:"SET.PREFERRED",78:"AXES",79:"LEGEND",80:"ATTACH.TEXT",81:"ADD.ARROW",82:"SELECT.CHART",83:"SELECT.PLOT.AREA",84:"PATTERNS",85:"MAIN.CHART",86:"OVERLAY",87:"SCALE",88:"FORMAT.LEGEND",89:"FORMAT.TEXT",90:"EDIT.REPEAT",91:"PARSE",92:"JUSTIFY",93:"HIDE",94:"UNHIDE",95:"WORKSPACE",96:"FORMULA",97:"FORMULA.FILL",98:"FORMULA.ARRAY",99:"DATA.FIND.NEXT",100:"DATA.FIND.PREV",101:"FORMULA.FIND.NEXT",102:"FORMULA.FIND.PREV",103:"ACTIVATE",104:"ACTIVATE.NEXT",105:"ACTIVATE.PREV",106:"UNLOCKED.NEXT",107:"UNLOCKED.PREV",108:"COPY.PICTURE",109:"SELECT",110:"DELETE.NAME",111:"DELETE.FORMAT",112:"VLINE",113:"HLINE",114:"VPAGE",115:"HPAGE",116:"VSCROLL",117:"HSCROLL",118:"ALERT",119:"NEW",120:"CANCEL.COPY",121:"SHOW.CLIPBOARD",122:"MESSAGE",124:"PASTE.LINK",125:"APP.ACTIVATE",126:"DELETE.ARROW",127:"ROW.HEIGHT",128:"FORMAT.MOVE",129:"FORMAT.SIZE",130:"FORMULA.REPLACE",131:"SEND.KEYS",132:"SELECT.SPECIAL",133:"APPLY.NAMES",134:"REPLACE.FONT",135:"FREEZE.PANES",136:"SHOW.INFO",137:"SPLIT",138:"ON.WINDOW",139:"ON.DATA",140:"DISABLE.INPUT",142:"OUTLINE",143:"LIST.NAMES",144:"FILE.CLOSE",145:"SAVE.WORKBOOK",146:"DATA.FORM",147:"COPY.CHART",148:"ON.TIME",149:"WAIT",150:"FORMAT.FONT",151:"FILL.UP",152:"FILL.LEFT",153:"DELETE.OVERLAY",155:"SHORT.MENUS",159:"SET.UPDATE.STATUS",161:"COLOR.PALETTE",162:"DELETE.STYLE",163:"WINDOW.RESTORE",164:"WINDOW.MAXIMIZE",166:"CHANGE.LINK",167:"CALCULATE.DOCUMENT",168:"ON.KEY",169:"APP.RESTORE",170:"APP.MOVE",171:"APP.SIZE",172:"APP.MINIMIZE",173:"APP.MAXIMIZE",174:"BRING.TO.FRONT",175:"SEND.TO.BACK",185:"MAIN.CHART.TYPE",186:"OVERLAY.CHART.TYPE",187:"SELECT.END",188:"OPEN.MAIL",189:"SEND.MAIL",190:"STANDARD.FONT",191:"CONSOLIDATE",192:"SORT.SPECIAL",193:"GALLERY.3D.AREA",194:"GALLERY.3D.COLUMN",195:"GALLERY.3D.LINE",196:"GALLERY.3D.PIE",197:"VIEW.3D",198:"GOAL.SEEK",199:"WORKGROUP",200:"FILL.GROUP",201:"UPDATE.LINK",202:"PROMOTE",203:"DEMOTE",204:"SHOW.DETAIL",206:"UNGROUP",207:"OBJECT.PROPERTIES",208:"SAVE.NEW.OBJECT",209:"SHARE",210:"SHARE.NAME",211:"DUPLICATE",212:"APPLY.STYLE",213:"ASSIGN.TO.OBJECT",214:"OBJECT.PROTECTION",215:"HIDE.OBJECT",216:"SET.EXTRACT",217:"CREATE.PUBLISHER",218:"SUBSCRIBE.TO",219:"ATTRIBUTES",220:"SHOW.TOOLBAR",222:"PRINT.PREVIEW",223:"EDIT.COLOR",224:"SHOW.LEVELS",225:"FORMAT.MAIN",226:"FORMAT.OVERLAY",227:"ON.RECALC",228:"EDIT.SERIES",229:"DEFINE.STYLE",240:"LINE.PRINT",243:"ENTER.DATA",249:"GALLERY.RADAR",250:"MERGE.STYLES",251:"EDITION.OPTIONS",252:"PASTE.PICTURE",253:"PASTE.PICTURE.LINK",254:"SPELLING",256:"ZOOM",259:"INSERT.OBJECT",260:"WINDOW.MINIMIZE",265:"SOUND.NOTE",266:"SOUND.PLAY",267:"FORMAT.SHAPE",268:"EXTEND.POLYGON",269:"FORMAT.AUTO",272:"GALLERY.3D.BAR",273:"GALLERY.3D.SURFACE",274:"FILL.AUTO",276:"CUSTOMIZE.TOOLBAR",277:"ADD.TOOL",278:"EDIT.OBJECT",279:"ON.DOUBLECLICK",280:"ON.ENTRY",281:"WORKBOOK.ADD",282:"WORKBOOK.MOVE",283:"WORKBOOK.COPY",284:"WORKBOOK.OPTIONS",285:"SAVE.WORKSPACE",288:"CHART.WIZARD",289:"DELETE.TOOL",290:"MOVE.TOOL",291:"WORKBOOK.SELECT",292:"WORKBOOK.ACTIVATE",293:"ASSIGN.TO.TOOL",295:"COPY.TOOL",296:"RESET.TOOL",297:"CONSTRAIN.NUMERIC",298:"PASTE.TOOL",302:"WORKBOOK.NEW",305:"SCENARIO.CELLS",306:"SCENARIO.DELETE",307:"SCENARIO.ADD",308:"SCENARIO.EDIT",309:"SCENARIO.SHOW",310:"SCENARIO.SHOW.NEXT",311:"SCENARIO.SUMMARY",312:"PIVOT.TABLE.WIZARD",313:"PIVOT.FIELD.PROPERTIES",314:"PIVOT.FIELD",315:"PIVOT.ITEM",316:"PIVOT.ADD.FIELDS",318:"OPTIONS.CALCULATION",319:"OPTIONS.EDIT",320:"OPTIONS.VIEW",321:"ADDIN.MANAGER",322:"MENU.EDITOR",323:"ATTACH.TOOLBARS",324:"VBAActivate",325:"OPTIONS.CHART",328:"VBA.INSERT.FILE",330:"VBA.PROCEDURE.DEFINITION",336:"ROUTING.SLIP",338:"ROUTE.DOCUMENT",339:"MAIL.LOGON",342:"INSERT.PICTURE",343:"EDIT.TOOL",344:"GALLERY.DOUGHNUT",350:"CHART.TREND",352:"PIVOT.ITEM.PROPERTIES",354:"WORKBOOK.INSERT",355:"OPTIONS.TRANSITION",356:"OPTIONS.GENERAL",370:"FILTER.ADVANCED",373:"MAIL.ADD.MAILER",374:"MAIL.DELETE.MAILER",375:"MAIL.REPLY",376:"MAIL.REPLY.ALL",377:"MAIL.FORWARD",378:"MAIL.NEXT.LETTER",379:"DATA.LABEL",380:"INSERT.TITLE",381:"FONT.PROPERTIES",382:"MACRO.OPTIONS",383:"WORKBOOK.HIDE",384:"WORKBOOK.UNHIDE",385:"WORKBOOK.DELETE",386:"WORKBOOK.NAME",388:"GALLERY.CUSTOM",390:"ADD.CHART.AUTOFORMAT",391:"DELETE.CHART.AUTOFORMAT",392:"CHART.ADD.DATA",393:"AUTO.OUTLINE",394:"TAB.ORDER",395:"SHOW.DIALOG",396:"SELECT.ALL",397:"UNGROUP.SHEETS",398:"SUBTOTAL.CREATE",399:"SUBTOTAL.REMOVE",400:"RENAME.OBJECT",412:"WORKBOOK.SCROLL",413:"WORKBOOK.NEXT",414:"WORKBOOK.PREV",415:"WORKBOOK.TAB.SPLIT",416:"FULL.SCREEN",417:"WORKBOOK.PROTECT",420:"SCROLLBAR.PROPERTIES",421:"PIVOT.SHOW.PAGES",422:"TEXT.TO.COLUMNS",423:"FORMAT.CHARTTYPE",424:"LINK.FORMAT",425:"TRACER.DISPLAY",430:"TRACER.NAVIGATE",431:"TRACER.CLEAR",432:"TRACER.ERROR",433:"PIVOT.FIELD.GROUP",434:"PIVOT.FIELD.UNGROUP",435:"CHECKBOX.PROPERTIES",436:"LABEL.PROPERTIES",437:"LISTBOX.PROPERTIES",438:"EDITBOX.PROPERTIES",439:"PIVOT.REFRESH",440:"LINK.COMBO",441:"OPEN.TEXT",442:"HIDE.DIALOG",443:"SET.DIALOG.FOCUS",444:"ENABLE.OBJECT",445:"PUSHBUTTON.PROPERTIES",446:"SET.DIALOG.DEFAULT",447:"FILTER",448:"FILTER.SHOW.ALL",449:"CLEAR.OUTLINE",450:"FUNCTION.WIZARD",451:"ADD.LIST.ITEM",452:"SET.LIST.ITEM",453:"REMOVE.LIST.ITEM",454:"SELECT.LIST.ITEM",455:"SET.CONTROL.VALUE",456:"SAVE.COPY.AS",458:"OPTIONS.LISTS.ADD",459:"OPTIONS.LISTS.DELETE",460:"SERIES.AXES",461:"SERIES.X",462:"SERIES.Y",463:"ERRORBAR.X",464:"ERRORBAR.Y",465:"FORMAT.CHART",466:"SERIES.ORDER",467:"MAIL.LOGOFF",468:"CLEAR.ROUTING.SLIP",469:"APP.ACTIVATE.MICROSOFT",470:"MAIL.EDIT.MAILER",471:"ON.SHEET",472:"STANDARD.WIDTH",473:"SCENARIO.MERGE",474:"SUMMARY.INFO",475:"FIND.FILE",476:"ACTIVE.CELL.FONT",477:"ENABLE.TIPWIZARD",478:"VBA.MAKE.ADDIN",480:"INSERTDATATABLE",481:"WORKGROUP.OPTIONS",482:"MAIL.SEND.MAILER",485:"AUTOCORRECT",489:"POST.DOCUMENT",491:"PICKLIST",493:"VIEW.SHOW",494:"VIEW.DEFINE",495:"VIEW.DELETE",509:"SHEET.BACKGROUND",510:"INSERT.MAP.OBJECT",511:"OPTIONS.MENONO",517:"MSOCHECKS",518:"NORMAL",519:"LAYOUT",520:"RM.PRINT.AREA",521:"CLEAR.PRINT.AREA",522:"ADD.PRINT.AREA",523:"MOVE.BRK",545:"HIDECURR.NOTE",546:"HIDEALL.NOTES",547:"DELETE.NOTE",548:"TRAVERSE.NOTES",549:"ACTIVATE.NOTES",620:"PROTECT.REVISIONS",621:"UNPROTECT.REVISIONS",647:"OPTIONS.ME",653:"WEB.PUBLISH",667:"NEWWEBQUERY",673:"PIVOT.TABLE.CHART",753:"OPTIONS.SAVE",755:"OPTIONS.SPELL",808:"HIDEALL.INKANNOTS"},Oi={0:"COUNT",1:"IF",2:"ISNA",3:"ISERROR",4:"SUM",5:"AVERAGE",6:"MIN",7:"MAX",8:"ROW",9:"COLUMN",10:"NA",11:"NPV",12:"STDEV",13:"DOLLAR",14:"FIXED",15:"SIN",16:"COS",17:"TAN",18:"ATAN",19:"PI",20:"SQRT",21:"EXP",22:"LN",23:"LOG10",24:"ABS",25:"INT",26:"SIGN",27:"ROUND",28:"LOOKUP",29:"INDEX",30:"REPT",31:"MID",32:"LEN",33:"VALUE",34:"TRUE",35:"FALSE",36:"AND",37:"OR",38:"NOT",39:"MOD",40:"DCOUNT",41:"DSUM",42:"DAVERAGE",43:"DMIN",44:"DMAX",45:"DSTDEV",46:"VAR",47:"DVAR",48:"TEXT",49:"LINEST",50:"TREND",51:"LOGEST",52:"GROWTH",53:"GOTO",54:"HALT",55:"RETURN",56:"PV",57:"FV",58:"NPER",59:"PMT",60:"RATE",61:"MIRR",62:"IRR",63:"RAND",64:"MATCH",65:"DATE",66:"TIME",67:"DAY",68:"MONTH",69:"YEAR",70:"WEEKDAY",71:"HOUR",72:"MINUTE",73:"SECOND",74:"NOW",75:"AREAS",76:"ROWS",77:"COLUMNS",78:"OFFSET",79:"ABSREF",80:"RELREF",81:"ARGUMENT",82:"SEARCH",83:"TRANSPOSE",84:"ERROR",85:"STEP",86:"TYPE",87:"ECHO",88:"SET.NAME",89:"CALLER",90:"DEREF",91:"WINDOWS",92:"SERIES",93:"DOCUMENTS",94:"ACTIVE.CELL",95:"SELECTION",96:"RESULT",97:"ATAN2",98:"ASIN",99:"ACOS",100:"CHOOSE",101:"HLOOKUP",102:"VLOOKUP",103:"LINKS",104:"INPUT",105:"ISREF",106:"GET.FORMULA",107:"GET.NAME",108:"SET.VALUE",109:"LOG",110:"EXEC",111:"CHAR",112:"LOWER",113:"UPPER",114:"PROPER",115:"LEFT",116:"RIGHT",117:"EXACT",118:"TRIM",119:"REPLACE",120:"SUBSTITUTE",121:"CODE",122:"NAMES",123:"DIRECTORY",124:"FIND",125:"CELL",126:"ISERR",127:"ISTEXT",128:"ISNUMBER",129:"ISBLANK",130:"T",131:"N",132:"FOPEN",133:"FCLOSE",134:"FSIZE",135:"FREADLN",136:"FREAD",137:"FWRITELN",138:"FWRITE",139:"FPOS",140:"DATEVALUE",141:"TIMEVALUE",142:"SLN",143:"SYD",144:"DDB",145:"GET.DEF",146:"REFTEXT",147:"TEXTREF",148:"INDIRECT",149:"REGISTER",150:"CALL",151:"ADD.BAR",152:"ADD.MENU",153:"ADD.COMMAND",154:"ENABLE.COMMAND",155:"CHECK.COMMAND",156:"RENAME.COMMAND",157:"SHOW.BAR",158:"DELETE.MENU",159:"DELETE.COMMAND",160:"GET.CHART.ITEM",161:"DIALOG.BOX",162:"CLEAN",163:"MDETERM",164:"MINVERSE",165:"MMULT",166:"FILES",167:"IPMT",168:"PPMT",169:"COUNTA",170:"CANCEL.KEY",171:"FOR",172:"WHILE",173:"BREAK",174:"NEXT",175:"INITIATE",176:"REQUEST",177:"POKE",178:"EXECUTE",179:"TERMINATE",180:"RESTART",181:"HELP",182:"GET.BAR",183:"PRODUCT",184:"FACT",185:"GET.CELL",186:"GET.WORKSPACE",187:"GET.WINDOW",188:"GET.DOCUMENT",189:"DPRODUCT",190:"ISNONTEXT",191:"GET.NOTE",192:"NOTE",193:"STDEVP",194:"VARP",195:"DSTDEVP",196:"DVARP",197:"TRUNC",198:"ISLOGICAL",199:"DCOUNTA",200:"DELETE.BAR",201:"UNREGISTER",204:"USDOLLAR",205:"FINDB",206:"SEARCHB",207:"REPLACEB",208:"LEFTB",209:"RIGHTB",210:"MIDB",211:"LENB",212:"ROUNDUP",213:"ROUNDDOWN",214:"ASC",215:"DBCS",216:"RANK",219:"ADDRESS",220:"DAYS360",221:"TODAY",222:"VDB",223:"ELSE",224:"ELSE.IF",225:"END.IF",226:"FOR.CELL",227:"MEDIAN",228:"SUMPRODUCT",229:"SINH",230:"COSH",231:"TANH",232:"ASINH",233:"ACOSH",234:"ATANH",235:"DGET",236:"CREATE.OBJECT",237:"VOLATILE",238:"LAST.ERROR",239:"CUSTOM.UNDO",240:"CUSTOM.REPEAT",241:"FORMULA.CONVERT",242:"GET.LINK.INFO",243:"TEXT.BOX",244:"INFO",245:"GROUP",246:"GET.OBJECT",247:"DB",248:"PAUSE",251:"RESUME",252:"FREQUENCY",253:"ADD.TOOLBAR",254:"DELETE.TOOLBAR",255:"User",256:"RESET.TOOLBAR",257:"EVALUATE",258:"GET.TOOLBAR",259:"GET.TOOL",260:"SPELLING.CHECK",261:"ERROR.TYPE",262:"APP.TITLE",263:"WINDOW.TITLE",264:"SAVE.TOOLBAR",265:"ENABLE.TOOL",266:"PRESS.TOOL",267:"REGISTER.ID",268:"GET.WORKBOOK",269:"AVEDEV",270:"BETADIST",271:"GAMMALN",272:"BETAINV",273:"BINOMDIST",274:"CHIDIST",275:"CHIINV",276:"COMBIN",277:"CONFIDENCE",278:"CRITBINOM",279:"EVEN",280:"EXPONDIST",281:"FDIST",282:"FINV",283:"FISHER",284:"FISHERINV",285:"FLOOR",286:"GAMMADIST",287:"GAMMAINV",288:"CEILING",289:"HYPGEOMDIST",290:"LOGNORMDIST",291:"LOGINV",292:"NEGBINOMDIST",293:"NORMDIST",294:"NORMSDIST",295:"NORMINV",296:"NORMSINV",297:"STANDARDIZE",298:"ODD",299:"PERMUT",300:"POISSON",301:"TDIST",302:"WEIBULL",303:"SUMXMY2",304:"SUMX2MY2",305:"SUMX2PY2",306:"CHITEST",307:"CORREL",308:"COVAR",309:"FORECAST",310:"FTEST",311:"INTERCEPT",312:"PEARSON",313:"RSQ",314:"STEYX",315:"SLOPE",316:"TTEST",317:"PROB",318:"DEVSQ",319:"GEOMEAN",320:"HARMEAN",321:"SUMSQ",322:"KURT",323:"SKEW",324:"ZTEST",325:"LARGE",326:"SMALL",327:"QUARTILE",328:"PERCENTILE",329:"PERCENTRANK",330:"MODE",331:"TRIMMEAN",332:"TINV",334:"MOVIE.COMMAND",335:"GET.MOVIE",336:"CONCATENATE",337:"POWER",338:"PIVOT.ADD.DATA",339:"GET.PIVOT.TABLE",340:"GET.PIVOT.FIELD",341:"GET.PIVOT.ITEM",342:"RADIANS",343:"DEGREES",344:"SUBTOTAL",345:"SUMIF",346:"COUNTIF",347:"COUNTBLANK",348:"SCENARIO.GET",349:"OPTIONS.LISTS.GET",350:"ISPMT",351:"DATEDIF",352:"DATESTRING",353:"NUMBERSTRING",354:"ROMAN",355:"OPEN.DIALOG",356:"SAVE.DIALOG",357:"VIEW.GET",358:"GETPIVOTDATA",359:"HYPERLINK",360:"PHONETIC",361:"AVERAGEA",362:"MAXA",363:"MINA",364:"STDEVPA",365:"VARPA",366:"STDEVA",367:"VARA",368:"BAHTTEXT",369:"THAIDAYOFWEEK",370:"THAIDIGIT",371:"THAIMONTHOFYEAR",372:"THAINUMSOUND",373:"THAINUMSTRING",374:"THAISTRINGLENGTH",375:"ISTHAIDIGIT",376:"ROUNDBAHTDOWN",377:"ROUNDBAHTUP",378:"THAIYEAR",379:"RTD",380:"CUBEVALUE",381:"CUBEMEMBER",382:"CUBEMEMBERPROPERTY",383:"CUBERANKEDMEMBER",384:"HEX2BIN",385:"HEX2DEC",386:"HEX2OCT",387:"DEC2BIN",388:"DEC2HEX",389:"DEC2OCT",390:"OCT2BIN",391:"OCT2HEX",392:"OCT2DEC",393:"BIN2DEC",394:"BIN2OCT",395:"BIN2HEX",396:"IMSUB",397:"IMDIV",398:"IMPOWER",399:"IMABS",400:"IMSQRT",401:"IMLN",402:"IMLOG2",403:"IMLOG10",404:"IMSIN",405:"IMCOS",406:"IMEXP",407:"IMARGUMENT",408:"IMCONJUGATE",409:"IMAGINARY",410:"IMREAL",411:"COMPLEX",412:"IMSUM",413:"IMPRODUCT",414:"SERIESSUM",415:"FACTDOUBLE",416:"SQRTPI",417:"QUOTIENT",418:"DELTA",419:"GESTEP",420:"ISEVEN",421:"ISODD",422:"MROUND",423:"ERF",424:"ERFC",425:"BESSELJ",426:"BESSELK",427:"BESSELY",428:"BESSELI",429:"XIRR",430:"XNPV",431:"PRICEMAT",432:"YIELDMAT",433:"INTRATE",434:"RECEIVED",435:"DISC",436:"PRICEDISC",437:"YIELDDISC",438:"TBILLEQ",439:"TBILLPRICE",440:"TBILLYIELD",441:"PRICE",442:"YIELD",443:"DOLLARDE",444:"DOLLARFR",445:"NOMINAL",446:"EFFECT",447:"CUMPRINC",448:"CUMIPMT",449:"EDATE",450:"EOMONTH",451:"YEARFRAC",452:"COUPDAYBS",453:"COUPDAYS",454:"COUPDAYSNC",455:"COUPNCD",456:"COUPNUM",457:"COUPPCD",458:"DURATION",459:"MDURATION",460:"ODDLPRICE",461:"ODDLYIELD",462:"ODDFPRICE",463:"ODDFYIELD",464:"RANDBETWEEN",465:"WEEKNUM",466:"AMORDEGRC",467:"AMORLINC",468:"CONVERT",724:"SHEETJS",469:"ACCRINT",470:"ACCRINTM",471:"WORKDAY",472:"NETWORKDAYS",473:"GCD",474:"MULTINOMIAL",475:"LCM",476:"FVSCHEDULE",477:"CUBEKPIMEMBER",478:"CUBESET",479:"CUBESETCOUNT",480:"IFERROR",481:"COUNTIFS",482:"SUMIFS",483:"AVERAGEIF",484:"AVERAGEIFS"},f1={2:1,3:1,10:0,15:1,16:1,17:1,18:1,19:0,20:1,21:1,22:1,23:1,24:1,25:1,26:1,27:2,30:2,31:3,32:1,33:1,34:0,35:0,38:1,39:2,40:3,41:3,42:3,43:3,44:3,45:3,47:3,48:2,53:1,61:3,63:0,65:3,66:3,67:1,68:1,69:1,70:1,71:1,72:1,73:1,74:0,75:1,76:1,77:1,79:2,80:2,83:1,85:0,86:1,89:0,90:1,94:0,95:0,97:2,98:1,99:1,101:3,102:3,105:1,106:1,108:2,111:1,112:1,113:1,114:1,117:2,118:1,119:4,121:1,126:1,127:1,128:1,129:1,130:1,131:1,133:1,134:1,135:1,136:2,137:2,138:2,140:1,141:1,142:3,143:4,144:4,161:1,162:1,163:1,164:1,165:2,172:1,175:2,176:2,177:3,178:2,179:1,184:1,186:1,189:3,190:1,195:3,196:3,197:1,198:1,199:3,201:1,207:4,210:3,211:1,212:2,213:2,214:1,215:1,225:0,229:1,230:1,231:1,232:1,233:1,234:1,235:3,244:1,247:4,252:2,257:1,261:1,271:1,273:4,274:2,275:2,276:2,277:3,278:3,279:1,280:3,281:3,282:3,283:1,284:1,285:2,286:4,287:3,288:2,289:4,290:3,291:3,292:3,293:4,294:1,295:3,296:1,297:3,298:1,299:2,300:3,301:3,302:4,303:2,304:2,305:2,306:2,307:2,308:2,309:3,310:2,311:2,312:2,313:2,314:2,315:2,316:4,325:2,326:2,327:2,328:2,331:2,332:2,337:2,342:1,343:1,346:2,347:1,350:4,351:3,352:1,353:2,360:1,368:1,369:1,370:1,371:1,372:1,373:1,374:1,375:1,376:1,377:1,378:1,382:3,385:1,392:1,393:1,396:2,397:2,398:2,399:1,400:1,401:1,402:1,403:1,404:1,405:1,406:1,407:1,408:1,409:1,410:1,414:4,415:1,416:1,417:2,420:1,421:1,422:2,424:1,425:2,426:2,427:2,428:2,430:3,438:3,439:3,440:3,443:2,444:2,445:2,446:2,447:6,448:6,449:2,450:2,464:2,468:3,476:2,479:1,480:2,65535:0};function l1(e){var t="of:="+e.replace(t0,"$1[.$2$3$4$5]").replace(/\]:\[/g,":");return t.replace(/;/g,"|").replace(/,/g,";")}function o1(e){return e.replace(/\./,"!")}var Ct=typeof Map<"u";function i0(e,t,r){var n=0,a=e.length;if(r){if(Ct?r.has(t):Object.prototype.hasOwnProperty.call(r,t)){for(var i=Ct?r.get(t):r[t];n<i.length;++n)if(e[i[n]].t===t)return e.Count++,i[n]}}else for(;n<a;++n)if(e[n].t===t)return e.Count++,n;return e[a]={t},e.Count++,e.Unique++,r&&(Ct?(r.has(t)||r.set(t,[]),r.get(t).push(a)):(Object.prototype.hasOwnProperty.call(r,t)||(r[t]=[]),r[t].push(a))),a}function En(e,t){var r={min:e+1,max:e+1},n=-1;return t.MDW&&(Rr=t.MDW),t.width!=null?r.customWidth=1:t.wpx!=null?n=cn(t.wpx):t.wch!=null&&(n=t.wch),n>-1?(r.width=Wn(n),r.customWidth=1):t.width!=null&&(r.width=t.width),t.hidden&&(r.hidden=!0),t.level!=null&&(r.outlineLevel=r.level=t.level),r}function Di(e,t){if(!!e){var r=[.7,.7,.75,.75,.3,.3];t=="xlml"&&(r=[1,1,1,1,.5,.5]),e.left==null&&(e.left=r[0]),e.right==null&&(e.right=r[1]),e.top==null&&(e.top=r[2]),e.bottom==null&&(e.bottom=r[3]),e.header==null&&(e.header=r[4]),e.footer==null&&(e.footer=r[5])}}function Wr(e,t,r){var n=r.revssf[t.z!=null?t.z:"General"],a=60,i=e.length;if(n==null&&r.ssf){for(;a<392;++a)if(r.ssf[a]==null){Aa(t.z,a),r.ssf[a]=t.z,r.revssf[t.z]=n=a;break}}for(a=0;a!=i;++a)if(e[a].numFmtId===n)return a;return e[i]={numFmtId:n,fontId:0,fillId:0,borderId:0,xfId:0,applyNumberFormat:1},i}function c1(e,t,r){if(e&&e["!ref"]){var n=Ae(e["!ref"]);if(n.e.c<n.s.c||n.e.r<n.s.r)throw new Error("Bad range ("+r+"): "+e["!ref"])}}function h1(e){if(e.length===0)return"";for(var t='<mergeCells count="'+e.length+'">',r=0;r!=e.length;++r)t+='<mergeCell ref="'+ke(e[r])+'"/>';return t+"</mergeCells>"}function u1(e,t,r,n,a){var i=!1,s={},f=null;if(n.bookType!=="xlsx"&&t.vbaraw){var o=t.SheetNames[r];try{t.Workbook&&(o=t.Workbook.Sheets[r].CodeName||o)}catch{}i=!0,s.codeName=Cr(Te(o))}if(e&&e["!outline"]){var l={summaryBelow:1,summaryRight:1};e["!outline"].above&&(l.summaryBelow=0),e["!outline"].left&&(l.summaryRight=0),f=(f||"")+J("outlinePr",null,l)}!i&&!f||(a[a.length]=J("sheetPr",f,s))}var x1=["objects","scenarios","selectLockedCells","selectUnlockedCells"],d1=["formatColumns","formatRows","formatCells","insertColumns","insertRows","insertHyperlinks","deleteColumns","deleteRows","sort","autoFilter","pivotTables"];function v1(e){var t={sheet:1};return x1.forEach(function(r){e[r]!=null&&e[r]&&(t[r]="1")}),d1.forEach(function(r){e[r]!=null&&!e[r]&&(t[r]="0")}),e.password&&(t.password=ui(e.password).toString(16).toUpperCase()),J("sheetProtection",null,t)}function p1(e){return Di(e),J("pageMargins",null,e)}function m1(e,t){for(var r=["<cols>"],n,a=0;a!=t.length;++a)!(n=t[a])||(r[r.length]=J("col",null,En(a,n)));return r[r.length]="</cols>",r.join("")}function g1(e,t,r,n){var a=typeof e.ref=="string"?e.ref:ke(e.ref);r.Workbook||(r.Workbook={Sheets:[]}),r.Workbook.Names||(r.Workbook.Names=[]);var i=r.Workbook.Names,s=fr(a);s.s.r==s.e.r&&(s.e.r=fr(t["!ref"]).e.r,a=ke(s));for(var f=0;f<i.length;++f){var o=i[f];if(o.Name=="_xlnm._FilterDatabase"&&o.Sheet==n){o.Ref="'"+r.SheetNames[n]+"'!"+a;break}}return f==i.length&&i.push({Name:"_xlnm._FilterDatabase",Sheet:n,Ref:"'"+r.SheetNames[n]+"'!"+a}),J("autoFilter",null,{ref:a})}function _1(e,t,r,n){var a={workbookViewId:"0"};return(((n||{}).Workbook||{}).Views||[])[0]&&(a.rightToLeft=n.Workbook.Views[0].RTL?"1":"0"),J("sheetViews",J("sheetView",null,a),{})}function T1(e,t,r,n){if(e.c&&r["!comments"].push([t,e.c]),e.v===void 0&&typeof e.f!="string"||e.t==="z"&&!e.f)return"";var a="",i=e.t,s=e.v;if(e.t!=="z")switch(e.t){case"b":a=e.v?"1":"0";break;case"n":a=""+e.v;break;case"e":a=Mt[e.v];break;case"d":n&&n.cellDates?a=qe(e.v,-1).toISOString():(e=tr(e),e.t="n",a=""+(e.v=rr(qe(e.v)))),typeof e.z>"u"&&(e.z=Re[14]);break;default:a=e.v;break}var f=Ve("v",Te(a)),o={r:t},l=Wr(n.cellXfs,e,n);switch(l!==0&&(o.s=l),e.t){case"n":break;case"d":o.t="d";break;case"b":o.t="b";break;case"e":o.t="e";break;case"z":break;default:if(e.v==null){delete e.t;break}if(e.v.length>32767)throw new Error("Text length must not exceed 32767 characters");if(n&&n.bookSST){f=Ve("v",""+i0(n.Strings,e.v,n.revStrings)),o.t="s";break}o.t="str";break}if(e.t!=i&&(e.t=i,e.v=s),typeof e.f=="string"&&e.f){var c=e.F&&e.F.slice(0,t.length)==t?{t:"array",ref:e.F}:null;f=J("f",Te(e.f),c)+(e.v!=null?f:"")}return e.l&&r["!links"].push([t,e.l]),e.D&&(o.cm=1),J("c",f,o)}function E1(e,t,r,n){var a=[],i=[],s=Ae(e["!ref"]),f="",o,l="",c=[],v=0,x=0,d=e["!rows"],T=Array.isArray(e),u={r:l},g,R=-1;for(x=s.s.c;x<=s.e.c;++x)c[x]=ze(x);for(v=s.s.r;v<=s.e.r;++v){for(i=[],l=Ge(v),x=s.s.c;x<=s.e.c;++x){o=c[x]+l;var C=T?(e[v]||[])[x]:e[o];C!==void 0&&(f=T1(C,o,e,t))!=null&&i.push(f)}(i.length>0||d&&d[v])&&(u={r:l},d&&d[v]&&(g=d[v],g.hidden&&(u.hidden=1),R=-1,g.hpx?R=hn(g.hpx):g.hpt&&(R=g.hpt),R>-1&&(u.ht=R,u.customHeight=1),g.level&&(u.outlineLevel=g.level)),a[a.length]=J("row",i.join(""),u))}if(d)for(;v<d.length;++v)d&&d[v]&&(u={r:v+1},g=d[v],g.hidden&&(u.hidden=1),R=-1,g.hpx?R=hn(g.hpx):g.hpt&&(R=g.hpt),R>-1&&(u.ht=R,u.customHeight=1),g.level&&(u.outlineLevel=g.level),a[a.length]=J("row","",u));return a.join("")}function Ri(e,t,r,n){var a=[Ie,J("worksheet",null,{xmlns:xt[0],"xmlns:r":Le.r})],i=r.SheetNames[e],s=0,f="",o=r.Sheets[i];o==null&&(o={});var l=o["!ref"]||"A1",c=Ae(l);if(c.e.c>16383||c.e.r>1048575){if(t.WTF)throw new Error("Range "+l+" exceeds format limit A1:XFD1048576");c.e.c=Math.min(c.e.c,16383),c.e.r=Math.min(c.e.c,1048575),l=ke(c)}n||(n={}),o["!comments"]=[];var v=[];u1(o,r,e,t,a),a[a.length]=J("dimension",null,{ref:l}),a[a.length]=_1(o,t,e,r),t.sheetFormat&&(a[a.length]=J("sheetFormatPr",null,{defaultRowHeight:t.sheetFormat.defaultRowHeight||"16",baseColWidth:t.sheetFormat.baseColWidth||"10",outlineLevelRow:t.sheetFormat.outlineLevelRow||"7"})),o["!cols"]!=null&&o["!cols"].length>0&&(a[a.length]=m1(o,o["!cols"])),a[s=a.length]="<sheetData/>",o["!links"]=[],o["!ref"]!=null&&(f=E1(o,t),f.length>0&&(a[a.length]=f)),a.length>s+1&&(a[a.length]="</sheetData>",a[s]=a[s].replace("/>",">")),o["!protect"]&&(a[a.length]=v1(o["!protect"])),o["!autofilter"]!=null&&(a[a.length]=g1(o["!autofilter"],o,r,e)),o["!merges"]!=null&&o["!merges"].length>0&&(a[a.length]=h1(o["!merges"]));var x=-1,d,T=-1;return o["!links"].length>0&&(a[a.length]="<hyperlinks>",o["!links"].forEach(function(u){!u[1].Target||(d={ref:u[0]},u[1].Target.charAt(0)!="#"&&(T=_e(n,-1,Te(u[1].Target).replace(/#.*$/,""),de.HLINK),d["r:id"]="rId"+T),(x=u[1].Target.indexOf("#"))>-1&&(d.location=Te(u[1].Target.slice(x+1))),u[1].Tooltip&&(d.tooltip=Te(u[1].Tooltip)),a[a.length]=J("hyperlink",null,d))}),a[a.length]="</hyperlinks>"),delete o["!links"],o["!margins"]!=null&&(a[a.length]=p1(o["!margins"])),(!t||t.ignoreEC||t.ignoreEC==null)&&(a[a.length]=Ve("ignoredErrors",J("ignoredError",null,{numberStoredAsText:1,sqref:l}))),v.length>0&&(T=_e(n,-1,"../drawings/drawing"+(e+1)+".xml",de.DRAW),a[a.length]=J("drawing",null,{"r:id":"rId"+T}),o["!drawing"]=v),o["!comments"].length>0&&(T=_e(n,-1,"../drawings/vmlDrawing"+(e+1)+".vml",de.VML),a[a.length]=J("legacyDrawing",null,{"r:id":"rId"+T}),o["!legacy"]=T),a.length>1&&(a[a.length]="</worksheet>",a[1]=a[1].replace("/>",">")),a.join("")}function w1(e,t){var r={},n=e.l+t;r.r=e.read_shift(4),e.l+=4;var a=e.read_shift(2);e.l+=1;var i=e.read_shift(1);return e.l=n,i&7&&(r.level=i&7),i&16&&(r.hidden=!0),i&32&&(r.hpt=a/20),r}function S1(e,t,r){var n=b(145),a=(r["!rows"]||[])[e]||{};n.write_shift(4,e),n.write_shift(4,0);var i=320;a.hpx?i=hn(a.hpx)*20:a.hpt&&(i=a.hpt*20),n.write_shift(2,i),n.write_shift(1,0);var s=0;a.level&&(s|=a.level),a.hidden&&(s|=16),(a.hpx||a.hpt)&&(s|=32),n.write_shift(1,s),n.write_shift(1,0);var f=0,o=n.l;n.l+=4;for(var l={r:e,c:0},c=0;c<16;++c)if(!(t.s.c>c+1<<10||t.e.c<c<<10)){for(var v=-1,x=-1,d=c<<10;d<c+1<<10;++d){l.c=d;var T=Array.isArray(r)?(r[l.r]||[])[l.c]:r[Ee(l)];T&&(v<0&&(v=d),x=d)}v<0||(++f,n.write_shift(4,v),n.write_shift(4,x))}var u=n.l;return n.l=o,n.write_shift(4,f),n.l=u,n.length>n.l?n.slice(0,n.l):n}function A1(e,t,r,n){var a=S1(n,r,t);(a.length>17||(t["!rows"]||[])[n])&&G(e,0,a)}var F1=et,y1=vt;function C1(){}function O1(e,t){var r={},n=e[e.l];return++e.l,r.above=!(n&64),r.left=!(n&128),e.l+=18,r.name=Mf(e),r}function D1(e,t,r){r==null&&(r=b(84+4*e.length));var n=192;t&&(t.above&&(n&=-65),t.left&&(n&=-129)),r.write_shift(1,n);for(var a=1;a<3;++a)r.write_shift(1,0);return fn({auto:1},r),r.write_shift(-4,-1),r.write_shift(-4,-1),$a(e,r),r.slice(0,r.l)}function R1(e){var t=ur(e);return[t]}function N1(e,t,r){return r==null&&(r=b(8)),Zr(t,r)}function k1(e){var t=qr(e);return[t]}function I1(e,t,r){return r==null&&(r=b(4)),Qr(t,r)}function P1(e){var t=ur(e),r=e.read_shift(1);return[t,r,"b"]}function L1(e,t,r){return r==null&&(r=b(9)),Zr(t,r),r.write_shift(1,e.v?1:0),r}function B1(e){var t=qr(e),r=e.read_shift(1);return[t,r,"b"]}function M1(e,t,r){return r==null&&(r=b(5)),Qr(t,r),r.write_shift(1,e.v?1:0),r}function U1(e){var t=ur(e),r=e.read_shift(1);return[t,r,"e"]}function b1(e,t,r){return r==null&&(r=b(9)),Zr(t,r),r.write_shift(1,e.v),r}function W1(e){var t=qr(e),r=e.read_shift(1);return[t,r,"e"]}function H1(e,t,r){return r==null&&(r=b(8)),Qr(t,r),r.write_shift(1,e.v),r.write_shift(2,0),r.write_shift(1,0),r}function V1(e){var t=ur(e),r=e.read_shift(4);return[t,r,"s"]}function G1(e,t,r){return r==null&&(r=b(12)),Zr(t,r),r.write_shift(4,t.v),r}function X1(e){var t=qr(e),r=e.read_shift(4);return[t,r,"s"]}function $1(e,t,r){return r==null&&(r=b(8)),Qr(t,r),r.write_shift(4,t.v),r}function z1(e){var t=ur(e),r=pt(e);return[t,r,"n"]}function K1(e,t,r){return r==null&&(r=b(16)),Zr(t,r),jr(e.v,r),r}function j1(e){var t=qr(e),r=pt(e);return[t,r,"n"]}function Y1(e,t,r){return r==null&&(r=b(12)),Qr(t,r),jr(e.v,r),r}function J1(e){var t=ur(e),r=za(e);return[t,r,"n"]}function Z1(e,t,r){return r==null&&(r=b(12)),Zr(t,r),Ka(e.v,r),r}function q1(e){var t=qr(e),r=za(e);return[t,r,"n"]}function Q1(e,t,r){return r==null&&(r=b(8)),Qr(t,r),Ka(e.v,r),r}function eh(e){var t=ur(e),r=Zn(e);return[t,r,"is"]}function rh(e){var t=ur(e),r=Ke(e);return[t,r,"str"]}function th(e,t,r){return r==null&&(r=b(12+4*e.v.length)),Zr(t,r),Me(e.v,r),r.length>r.l?r.slice(0,r.l):r}function nh(e){var t=qr(e),r=Ke(e);return[t,r,"str"]}function ah(e,t,r){return r==null&&(r=b(8+4*e.v.length)),Qr(t,r),Me(e.v,r),r.length>r.l?r.slice(0,r.l):r}function ih(e,t,r){var n=e.l+t,a=ur(e);a.r=r["!row"];var i=e.read_shift(1),s=[a,i,"b"];if(r.cellFormula){e.l+=2;var f=Tn(e,n-e.l,r);s[3]=ut(f,null,a,r.supbooks,r)}else e.l=n;return s}function sh(e,t,r){var n=e.l+t,a=ur(e);a.r=r["!row"];var i=e.read_shift(1),s=[a,i,"e"];if(r.cellFormula){e.l+=2;var f=Tn(e,n-e.l,r);s[3]=ut(f,null,a,r.supbooks,r)}else e.l=n;return s}function fh(e,t,r){var n=e.l+t,a=ur(e);a.r=r["!row"];var i=pt(e),s=[a,i,"n"];if(r.cellFormula){e.l+=2;var f=Tn(e,n-e.l,r);s[3]=ut(f,null,a,r.supbooks,r)}else e.l=n;return s}function lh(e,t,r){var n=e.l+t,a=ur(e);a.r=r["!row"];var i=Ke(e),s=[a,i,"str"];if(r.cellFormula){e.l+=2;var f=Tn(e,n-e.l,r);s[3]=ut(f,null,a,r.supbooks,r)}else e.l=n;return s}var oh=et,ch=vt;function hh(e,t){return t==null&&(t=b(4)),t.write_shift(4,e),t}function uh(e,t){var r=e.l+t,n=et(e),a=qn(e),i=Ke(e),s=Ke(e),f=Ke(e);e.l=r;var o={rfx:n,relId:a,loc:i,display:f};return s&&(o.Tooltip=s),o}function xh(e,t){var r=b(50+4*(e[1].Target.length+(e[1].Tooltip||"").length));vt({s:Be(e[0]),e:Be(e[0])},r),Qn("rId"+t,r);var n=e[1].Target.indexOf("#"),a=n==-1?"":e[1].Target.slice(n+1);return Me(a||"",r),Me(e[1].Tooltip||"",r),Me("",r),r.slice(0,r.l)}function dh(){}function vh(e,t,r){var n=e.l+t,a=ja(e),i=e.read_shift(1),s=[a];if(s[2]=i,r.cellFormula){var f=n1(e,n-e.l,r);s[1]=f}else e.l=n;return s}function ph(e,t,r){var n=e.l+t,a=et(e),i=[a];if(r.cellFormula){var s=i1(e,n-e.l,r);i[1]=s,e.l=n}else e.l=n;return i}function mh(e,t,r){r==null&&(r=b(18));var n=En(e,t);r.write_shift(-4,e),r.write_shift(-4,e),r.write_shift(4,(n.width||10)*256),r.write_shift(4,0);var a=0;return t.hidden&&(a|=1),typeof n.width=="number"&&(a|=2),t.level&&(a|=t.level<<8),r.write_shift(2,a),r}var Ni=["left","right","top","bottom","header","footer"];function gh(e){var t={};return Ni.forEach(function(r){t[r]=pt(e)}),t}function _h(e,t){return t==null&&(t=b(6*8)),Di(e),Ni.forEach(function(r){jr(e[r],t)}),t}function Th(e){var t=e.read_shift(2);return e.l+=28,{RTL:t&32}}function Eh(e,t,r){r==null&&(r=b(30));var n=924;return(((t||{}).Views||[])[0]||{}).RTL&&(n|=32),r.write_shift(2,n),r.write_shift(4,0),r.write_shift(4,0),r.write_shift(4,0),r.write_shift(1,0),r.write_shift(1,0),r.write_shift(2,0),r.write_shift(2,100),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(4,0),r}function wh(e){var t=b(24);return t.write_shift(4,4),t.write_shift(4,1),vt(e,t),t}function Sh(e,t){return t==null&&(t=b(16*4+2)),t.write_shift(2,e.password?ui(e.password):0),t.write_shift(4,1),[["objects",!1],["scenarios",!1],["formatCells",!0],["formatColumns",!0],["formatRows",!0],["insertColumns",!0],["insertRows",!0],["insertHyperlinks",!0],["deleteColumns",!0],["deleteRows",!0],["selectLockedCells",!1],["sort",!0],["autoFilter",!0],["pivotTables",!0],["selectUnlockedCells",!1]].forEach(function(r){r[1]?t.write_shift(4,e[r[0]]!=null&&!e[r[0]]?1:0):t.write_shift(4,e[r[0]]!=null&&e[r[0]]?0:1)}),t}function Ah(){}function Fh(){}function yh(e,t,r,n,a,i,s){if(t.v===void 0)return!1;var f="";switch(t.t){case"b":f=t.v?"1":"0";break;case"d":t=tr(t),t.z=t.z||Re[14],t.v=rr(qe(t.v)),t.t="n";break;case"n":case"e":f=""+t.v;break;default:f=t.v;break}var o={r,c:n};switch(o.s=Wr(a.cellXfs,t,a),t.l&&i["!links"].push([Ee(o),t.l]),t.c&&i["!comments"].push([Ee(o),t.c]),t.t){case"s":case"str":return a.bookSST?(f=i0(a.Strings,t.v,a.revStrings),o.t="s",o.v=f,s?G(e,18,$1(t,o)):G(e,7,G1(t,o))):(o.t="str",s?G(e,17,ah(t,o)):G(e,6,th(t,o))),!0;case"n":return t.v==(t.v|0)&&t.v>-1e3&&t.v<1e3?s?G(e,13,Q1(t,o)):G(e,2,Z1(t,o)):s?G(e,16,Y1(t,o)):G(e,5,K1(t,o)),!0;case"b":return o.t="b",s?G(e,15,M1(t,o)):G(e,4,L1(t,o)),!0;case"e":return o.t="e",s?G(e,14,H1(t,o)):G(e,3,b1(t,o)),!0}return s?G(e,12,I1(t,o)):G(e,1,N1(t,o)),!0}function Ch(e,t,r,n){var a=Ae(t["!ref"]||"A1"),i,s="",f=[];G(e,145);var o=Array.isArray(t),l=a.e.r;t["!rows"]&&(l=Math.max(a.e.r,t["!rows"].length-1));for(var c=a.s.r;c<=l;++c){s=Ge(c),A1(e,t,a,c);var v=!1;if(c<=a.e.r)for(var x=a.s.c;x<=a.e.c;++x){c===a.s.r&&(f[x]=ze(x)),i=f[x]+s;var d=o?(t[c]||[])[x]:t[i];if(!d){v=!1;continue}v=yh(e,d,c,x,n,t,v)}}G(e,146)}function Oh(e,t){!t||!t["!merges"]||(G(e,177,hh(t["!merges"].length)),t["!merges"].forEach(function(r){G(e,176,ch(r))}),G(e,178))}function Dh(e,t){!t||!t["!cols"]||(G(e,390),t["!cols"].forEach(function(r,n){r&&G(e,60,mh(n,r))}),G(e,391))}function Rh(e,t){!t||!t["!ref"]||(G(e,648),G(e,649,wh(Ae(t["!ref"]))),G(e,650))}function Nh(e,t,r){t["!links"].forEach(function(n){if(!!n[1].Target){var a=_e(r,-1,n[1].Target.replace(/#.*$/,""),de.HLINK);G(e,494,xh(n,a))}}),delete t["!links"]}function kh(e,t,r,n){if(t["!comments"].length>0){var a=_e(n,-1,"../drawings/vmlDrawing"+(r+1)+".vml",de.VML);G(e,551,Qn("rId"+a)),t["!legacy"]=a}}function Ih(e,t,r,n){if(!!t["!autofilter"]){var a=t["!autofilter"],i=typeof a.ref=="string"?a.ref:ke(a.ref);r.Workbook||(r.Workbook={Sheets:[]}),r.Workbook.Names||(r.Workbook.Names=[]);var s=r.Workbook.Names,f=fr(i);f.s.r==f.e.r&&(f.e.r=fr(t["!ref"]).e.r,i=ke(f));for(var o=0;o<s.length;++o){var l=s[o];if(l.Name=="_xlnm._FilterDatabase"&&l.Sheet==n){l.Ref="'"+r.SheetNames[n]+"'!"+i;break}}o==s.length&&s.push({Name:"_xlnm._FilterDatabase",Sheet:n,Ref:"'"+r.SheetNames[n]+"'!"+i}),G(e,161,vt(Ae(i))),G(e,162)}}function Ph(e,t,r){G(e,133),G(e,137,Eh(t,r)),G(e,138),G(e,134)}function Lh(e,t){!t["!protect"]||G(e,535,Sh(t["!protect"]))}function Bh(e,t,r,n){var a=er(),i=r.SheetNames[e],s=r.Sheets[i]||{},f=i;try{r&&r.Workbook&&(f=r.Workbook.Sheets[e].CodeName||f)}catch{}var o=Ae(s["!ref"]||"A1");if(o.e.c>16383||o.e.r>1048575){if(t.WTF)throw new Error("Range "+(s["!ref"]||"A1")+" exceeds format limit A1:XFD1048576");o.e.c=Math.min(o.e.c,16383),o.e.r=Math.min(o.e.c,1048575)}return s["!links"]=[],s["!comments"]=[],G(a,129),(r.vbaraw||s["!outline"])&&G(a,147,D1(f,s["!outline"])),G(a,148,y1(o)),Ph(a,s,r.Workbook),Dh(a,s),Ch(a,s,e,t),Lh(a,s),Ih(a,s,r,e),Oh(a,s),Nh(a,s,n),s["!margins"]&&G(a,476,_h(s["!margins"])),(!t||t.ignoreEC||t.ignoreEC==null)&&Rh(a,s),kh(a,s,e,n),G(a,130),a.end()}function Mh(e,t){e.l+=10;var r=Ke(e);return{name:r}}var Uh=[["allowRefreshQuery",!1,"bool"],["autoCompressPictures",!0,"bool"],["backupFile",!1,"bool"],["checkCompatibility",!1,"bool"],["CodeName",""],["date1904",!1,"bool"],["defaultThemeVersion",0,"int"],["filterPrivacy",!1,"bool"],["hidePivotFieldList",!1,"bool"],["promptedSolutions",!1,"bool"],["publishItems",!1,"bool"],["refreshAllConnections",!1,"bool"],["saveExternalLinkValues",!0,"bool"],["showBorderUnselectedTables",!0,"bool"],["showInkAnnotation",!0,"bool"],["showObjects","all"],["showPivotChartFilter",!1,"bool"],["updateLinks","userSet"]];function bh(e){return!e.Workbook||!e.Workbook.WBProps?"false":vf(e.Workbook.WBProps.date1904)?"true":"false"}var Wh="][*?/\\".split("");function ki(e,t){if(e.length>31){if(t)return!1;throw new Error("Sheet names cannot exceed 31 chars")}var r=!0;return Wh.forEach(function(n){if(e.indexOf(n)!=-1){if(!t)throw new Error("Sheet name cannot contain : \\ / ? * [ ]");r=!1}}),r}function Hh(e,t,r){e.forEach(function(n,a){ki(n);for(var i=0;i<a;++i)if(n==e[i])throw new Error("Duplicate Sheet Name: "+n);if(r){var s=t&&t[a]&&t[a].CodeName||n;if(s.charCodeAt(0)==95&&s.length>22)throw new Error("Bad Code Name: Worksheet"+s)}})}function Vh(e){if(!e||!e.SheetNames||!e.Sheets)throw new Error("Invalid Workbook");if(!e.SheetNames.length)throw new Error("Workbook is empty");var t=e.Workbook&&e.Workbook.Sheets||[];Hh(e.SheetNames,t,!!e.vbaraw);for(var r=0;r<e.SheetNames.length;++r)c1(e.Sheets[e.SheetNames[r]],e.SheetNames[r],r)}function Ii(e){var t=[Ie];t[t.length]=J("workbook",null,{xmlns:xt[0],"xmlns:r":Le.r});var r=e.Workbook&&(e.Workbook.Names||[]).length>0,n={codeName:"ThisWorkbook"};e.Workbook&&e.Workbook.WBProps&&(Uh.forEach(function(f){e.Workbook.WBProps[f[0]]!=null&&e.Workbook.WBProps[f[0]]!=f[1]&&(n[f[0]]=e.Workbook.WBProps[f[0]])}),e.Workbook.WBProps.CodeName&&(n.codeName=e.Workbook.WBProps.CodeName,delete n.CodeName)),t[t.length]=J("workbookPr",null,n);var a=e.Workbook&&e.Workbook.Sheets||[],i=0;if(a&&a[0]&&!!a[0].Hidden){for(t[t.length]="<bookViews>",i=0;i!=e.SheetNames.length&&!(!a[i]||!a[i].Hidden);++i);i==e.SheetNames.length&&(i=0),t[t.length]='<workbookView firstSheet="'+i+'" activeTab="'+i+'"/>',t[t.length]="</bookViews>"}for(t[t.length]="<sheets>",i=0;i!=e.SheetNames.length;++i){var s={name:Te(e.SheetNames[i].slice(0,31))};if(s.sheetId=""+(i+1),s["r:id"]="rId"+(i+1),a[i])switch(a[i].Hidden){case 1:s.state="hidden";break;case 2:s.state="veryHidden";break}t[t.length]=J("sheet",null,s)}return t[t.length]="</sheets>",r&&(t[t.length]="<definedNames>",e.Workbook&&e.Workbook.Names&&e.Workbook.Names.forEach(function(f){var o={name:f.Name};f.Comment&&(o.comment=f.Comment),f.Sheet!=null&&(o.localSheetId=""+f.Sheet),f.Hidden&&(o.hidden="1"),f.Ref&&(t[t.length]=J("definedName",Te(f.Ref),o))}),t[t.length]="</definedNames>"),t.length>2&&(t[t.length]="</workbook>",t[1]=t[1].replace("/>",">")),t.join("")}function Gh(e,t){var r={};return r.Hidden=e.read_shift(4),r.iTabID=e.read_shift(4),r.strRelID=bn(e),r.name=Ke(e),r}function Xh(e,t){return t||(t=b(127)),t.write_shift(4,e.Hidden),t.write_shift(4,e.iTabID),Qn(e.strRelID,t),Me(e.name.slice(0,31),t),t.length>t.l?t.slice(0,t.l):t}function $h(e,t){var r={},n=e.read_shift(4);r.defaultThemeVersion=e.read_shift(4);var a=t>8?Ke(e):"";return a.length>0&&(r.CodeName=a),r.autoCompressPictures=!!(n&65536),r.backupFile=!!(n&64),r.checkCompatibility=!!(n&4096),r.date1904=!!(n&1),r.filterPrivacy=!!(n&8),r.hidePivotFieldList=!!(n&1024),r.promptedSolutions=!!(n&16),r.publishItems=!!(n&2048),r.refreshAllConnections=!!(n&262144),r.saveExternalLinkValues=!!(n&128),r.showBorderUnselectedTables=!!(n&4),r.showInkAnnotation=!!(n&32),r.showObjects=["all","placeholders","none"][n>>13&3],r.showPivotChartFilter=!!(n&32768),r.updateLinks=["userSet","never","always"][n>>8&3],r}function zh(e,t){t||(t=b(72));var r=0;return e&&e.filterPrivacy&&(r|=8),t.write_shift(4,r),t.write_shift(4,0),$a(e&&e.CodeName||"ThisWorkbook",t),t.slice(0,t.l)}function Kh(e,t,r){var n=e.l+t;e.l+=4,e.l+=1;var a=e.read_shift(4),i=Uf(e),s=a1(e,0,r),f=qn(e);e.l=n;var o={Name:i,Ptg:s};return a<268435455&&(o.Sheet=a),f&&(o.Comment=f),o}function jh(e,t){G(e,143);for(var r=0;r!=t.SheetNames.length;++r){var n=t.Workbook&&t.Workbook.Sheets&&t.Workbook.Sheets[r]&&t.Workbook.Sheets[r].Hidden||0,a={Hidden:n,iTabID:r+1,strRelID:"rId"+(r+1),name:t.SheetNames[r]};G(e,156,Xh(a))}G(e,144)}function Yh(e,t){t||(t=b(127));for(var r=0;r!=4;++r)t.write_shift(4,0);return Me("SheetJS",t),Me(Qt.version,t),Me(Qt.version,t),Me("7262",t),t.length>t.l?t.slice(0,t.l):t}function Jh(e,t){t||(t=b(29)),t.write_shift(-4,0),t.write_shift(-4,460),t.write_shift(4,28800),t.write_shift(4,17600),t.write_shift(4,500),t.write_shift(4,e),t.write_shift(4,e);var r=120;return t.write_shift(1,r),t.length>t.l?t.slice(0,t.l):t}function Zh(e,t){if(!(!t.Workbook||!t.Workbook.Sheets)){for(var r=t.Workbook.Sheets,n=0,a=-1,i=-1;n<r.length;++n)!r[n]||!r[n].Hidden&&a==-1?a=n:r[n].Hidden==1&&i==-1&&(i=n);i>a||(G(e,135),G(e,158,Jh(a)),G(e,136))}}function qh(e,t){var r=er();return G(r,131),G(r,128,Yh()),G(r,153,zh(e.Workbook&&e.Workbook.WBProps||null)),Zh(r,e),jh(r,e),G(r,132),r.end()}function Qh(e,t,r){return(t.slice(-4)===".bin"?qh:Ii)(e)}function eu(e,t,r,n,a){return(t.slice(-4)===".bin"?Bh:Ri)(e,r,n,a)}function ru(e,t,r){return(t.slice(-4)===".bin"?_o:vi)(e,r)}function tu(e,t,r){return(t.slice(-4)===".bin"?$l:hi)(e,r)}function nu(e,t,r){return(t.slice(-4)===".bin"?Lo:Ti)(e)}function au(e){return(e.slice(-4)===".bin"?Co:gi)()}function iu(e,t){var r=[];return e.Props&&r.push(el(e.Props,t)),e.Custprops&&r.push(rl(e.Props,e.Custprops)),r.join("")}function su(){return""}function fu(e,t){var r=['<Style ss:ID="Default" ss:Name="Normal"><NumberFormat/></Style>'];return t.cellXfs.forEach(function(n,a){var i=[];i.push(J("NumberFormat",null,{"ss:Format":Te(Re[n.numFmtId])}));var s={"ss:ID":"s"+(21+a)};r.push(J("Style",i.join(""),s))}),J("Styles",r.join(""))}function Pi(e){return J("NamedRange",null,{"ss:Name":e.Name,"ss:RefersTo":"="+n0(e.Ref,{r:0,c:0})})}function lu(e){if(!((e||{}).Workbook||{}).Names)return"";for(var t=e.Workbook.Names,r=[],n=0;n<t.length;++n){var a=t[n];a.Sheet==null&&(a.Name.match(/^_xlfn\./)||r.push(Pi(a)))}return J("Names",r.join(""))}function ou(e,t,r,n){if(!e||!((n||{}).Workbook||{}).Names)return"";for(var a=n.Workbook.Names,i=[],s=0;s<a.length;++s){var f=a[s];f.Sheet==r&&(f.Name.match(/^_xlfn\./)||i.push(Pi(f)))}return i.join("")}function cu(e,t,r,n){if(!e)return"";var a=[];if(e["!margins"]&&(a.push("<PageSetup>"),e["!margins"].header&&a.push(J("Header",null,{"x:Margin":e["!margins"].header})),e["!margins"].footer&&a.push(J("Footer",null,{"x:Margin":e["!margins"].footer})),a.push(J("PageMargins",null,{"x:Bottom":e["!margins"].bottom||"0.75","x:Left":e["!margins"].left||"0.7","x:Right":e["!margins"].right||"0.7","x:Top":e["!margins"].top||"0.75"})),a.push("</PageSetup>")),n&&n.Workbook&&n.Workbook.Sheets&&n.Workbook.Sheets[r])if(n.Workbook.Sheets[r].Hidden)a.push(J("Visible",n.Workbook.Sheets[r].Hidden==1?"SheetHidden":"SheetVeryHidden",{}));else{for(var i=0;i<r&&!(n.Workbook.Sheets[i]&&!n.Workbook.Sheets[i].Hidden);++i);i==r&&a.push("<Selected/>")}return((((n||{}).Workbook||{}).Views||[])[0]||{}).RTL&&a.push("<DisplayRightToLeft/>"),e["!protect"]&&(a.push(Ve("ProtectContents","True")),e["!protect"].objects&&a.push(Ve("ProtectObjects","True")),e["!protect"].scenarios&&a.push(Ve("ProtectScenarios","True")),e["!protect"].selectLockedCells!=null&&!e["!protect"].selectLockedCells?a.push(Ve("EnableSelection","NoSelection")):e["!protect"].selectUnlockedCells!=null&&!e["!protect"].selectUnlockedCells&&a.push(Ve("EnableSelection","UnlockedCells")),[["formatCells","AllowFormatCells"],["formatColumns","AllowSizeCols"],["formatRows","AllowSizeRows"],["insertColumns","AllowInsertCols"],["insertRows","AllowInsertRows"],["insertHyperlinks","AllowInsertHyperlinks"],["deleteColumns","AllowDeleteCols"],["deleteRows","AllowDeleteRows"],["sort","AllowSort"],["autoFilter","AllowFilter"],["pivotTables","AllowUsePivotTables"]].forEach(function(s){e["!protect"][s[0]]&&a.push("<"+s[1]+"/>")})),a.length==0?"":J("WorksheetOptions",a.join(""),{xmlns:ir.x})}function hu(e){return e.map(function(t){var r=df(t.t||""),n=J("ss:Data",r,{xmlns:"http://www.w3.org/TR/REC-html40"});return J("Comment",n,{"ss:Author":t.a})}).join("")}function uu(e,t,r,n,a,i,s){if(!e||e.v==null&&e.f==null)return"";var f={};if(e.f&&(f["ss:Formula"]="="+Te(n0(e.f,s))),e.F&&e.F.slice(0,t.length)==t){var o=Be(e.F.slice(t.length+1));f["ss:ArrayRange"]="RC:R"+(o.r==s.r?"":"["+(o.r-s.r)+"]")+"C"+(o.c==s.c?"":"["+(o.c-s.c)+"]")}if(e.l&&e.l.Target&&(f["ss:HRef"]=Te(e.l.Target),e.l.Tooltip&&(f["x:HRefScreenTip"]=Te(e.l.Tooltip))),r["!merges"])for(var l=r["!merges"],c=0;c!=l.length;++c)l[c].s.c!=s.c||l[c].s.r!=s.r||(l[c].e.c>l[c].s.c&&(f["ss:MergeAcross"]=l[c].e.c-l[c].s.c),l[c].e.r>l[c].s.r&&(f["ss:MergeDown"]=l[c].e.r-l[c].s.r));var v="",x="";switch(e.t){case"z":if(!n.sheetStubs)return"";break;case"n":v="Number",x=String(e.v);break;case"b":v="Boolean",x=e.v?"1":"0";break;case"e":v="Error",x=Mt[e.v];break;case"d":v="DateTime",x=new Date(e.v).toISOString(),e.z==null&&(e.z=e.z||Re[14]);break;case"s":v="String",x=xf(e.v||"");break}var d=Wr(n.cellXfs,e,n);f["ss:StyleID"]="s"+(21+d),f["ss:Index"]=s.c+1;var T=e.v!=null?x:"",u=e.t=="z"?"":'<Data ss:Type="'+v+'">'+T+"</Data>";return(e.c||[]).length>0&&(u+=hu(e.c)),J("Cell",u,f)}function xu(e,t){var r='<Row ss:Index="'+(e+1)+'"';return t&&(t.hpt&&!t.hpx&&(t.hpx=di(t.hpt)),t.hpx&&(r+=' ss:AutoFitHeight="0" ss:Height="'+t.hpx+'"'),t.hidden&&(r+=' ss:Hidden="1"')),r+">"}function du(e,t,r,n){if(!e["!ref"])return"";var a=Ae(e["!ref"]),i=e["!merges"]||[],s=0,f=[];e["!cols"]&&e["!cols"].forEach(function(g,R){r0(g);var C=!!g.width,y=En(R,g),U={"ss:Index":R+1};C&&(U["ss:Width"]=on(y.width)),g.hidden&&(U["ss:Hidden"]="1"),f.push(J("Column",null,U))});for(var o=Array.isArray(e),l=a.s.r;l<=a.e.r;++l){for(var c=[xu(l,(e["!rows"]||[])[l])],v=a.s.c;v<=a.e.c;++v){var x=!1;for(s=0;s!=i.length;++s)if(!(i[s].s.c>v)&&!(i[s].s.r>l)&&!(i[s].e.c<v)&&!(i[s].e.r<l)){(i[s].s.c!=v||i[s].s.r!=l)&&(x=!0);break}if(!x){var d={r:l,c:v},T=Ee(d),u=o?(e[l]||[])[v]:e[T];c.push(uu(u,T,e,t,r,n,d))}}c.push("</Row>"),c.length>2&&f.push(c.join(""))}return f.join("")}function vu(e,t,r){var n=[],a=r.SheetNames[e],i=r.Sheets[a],s=i?ou(i,t,e,r):"";return s.length>0&&n.push("<Names>"+s+"</Names>"),s=i?du(i,t,e,r):"",s.length>0&&n.push("<Table>"+s+"</Table>"),n.push(cu(i,t,e,r)),n.join("")}function pu(e,t){t||(t={}),e.SSF||(e.SSF=tr(Re)),e.SSF&&(pn(),vn(e.SSF),t.revssf=mn(e.SSF),t.revssf[e.SSF[65535]]=0,t.ssf=e.SSF,t.cellXfs=[],Wr(t.cellXfs,{},{revssf:{General:0}}));var r=[];r.push(iu(e,t)),r.push(su()),r.push(""),r.push("");for(var n=0;n<e.SheetNames.length;++n)r.push(J("Worksheet",vu(n,t,e),{"ss:Name":Te(e.SheetNames[n])}));return r[2]=fu(e,t),r[3]=lu(e),Ie+J("Workbook",r.join(""),{xmlns:ir.ss,"xmlns:o":ir.o,"xmlns:x":ir.x,"xmlns:ss":ir.ss,"xmlns:dt":ir.dt,"xmlns:html":ir.html})}var Pn={SI:"e0859ff2f94f6810ab9108002b27b3d9",DSI:"02d5cdd59c2e1b10939708002b2cf9ae",UDI:"05d5cdd59c2e1b10939708002b2cf9ae"};function mu(e,t){var r=[],n=[],a=[],i=0,s,f=k0($0,"n"),o=k0(z0,"n");if(e.Props)for(s=Xe(e.Props),i=0;i<s.length;++i)(Object.prototype.hasOwnProperty.call(f,s[i])?r:Object.prototype.hasOwnProperty.call(o,s[i])?n:a).push([s[i],e.Props[s[i]]]);if(e.Custprops)for(s=Xe(e.Custprops),i=0;i<s.length;++i)Object.prototype.hasOwnProperty.call(e.Props||{},s[i])||(Object.prototype.hasOwnProperty.call(f,s[i])?r:Object.prototype.hasOwnProperty.call(o,s[i])?n:a).push([s[i],e.Custprops[s[i]]]);var l=[];for(i=0;i<a.length;++i)ai.indexOf(a[i][0])>-1||ri.indexOf(a[i][0])>-1||a[i][1]!=null&&l.push(a[i]);n.length&&we.utils.cfb_add(t,"/SummaryInformation",Z0(n,Pn.SI,o,z0)),(r.length||l.length)&&we.utils.cfb_add(t,"/DocumentSummaryInformation",Z0(r,Pn.DSI,f,$0,l.length?l:null,Pn.UDI))}function gu(e,t){var r=t||{},n=we.utils.cfb_new({root:"R"}),a="/Workbook";switch(r.bookType||"xls"){case"xls":r.bookType="biff8";case"xla":r.bookType||(r.bookType="xla");case"biff8":a="/Workbook",r.biff=8;break;case"biff5":a="/Book",r.biff=5;break;default:throw new Error("invalid type "+r.bookType+" for XLS CFB")}return we.utils.cfb_add(n,a,Li(e,r)),r.biff==8&&(e.Props||e.Custprops)&&mu(e,n),r.biff==8&&e.vbaraw&&Bo(n,we.read(e.vbaraw,{type:typeof e.vbaraw=="string"?"binary":"buffer"})),n}var _u={0:{f:w1},1:{f:R1},2:{f:J1},3:{f:U1},4:{f:P1},5:{f:z1},6:{f:rh},7:{f:V1},8:{f:lh},9:{f:fh},10:{f:ih},11:{f:sh},12:{f:k1},13:{f:q1},14:{f:W1},15:{f:B1},16:{f:j1},17:{f:nh},18:{f:X1},19:{f:Zn},20:{},21:{},22:{},23:{},24:{},25:{},26:{},27:{},28:{},29:{},30:{},31:{},32:{},33:{},34:{},35:{T:1},36:{T:-1},37:{T:1},38:{T:-1},39:{f:Kh},40:{},42:{},43:{f:eo},44:{f:ql},45:{f:no},46:{f:io},47:{f:ao},48:{},49:{f:Nf},50:{},51:{f:wo},52:{T:1},53:{T:-1},54:{T:1},55:{T:-1},56:{T:1},57:{T:-1},58:{},59:{},60:{f:kl},62:{f:eh},63:{f:Oo},64:{f:Ah},65:{},66:{},67:{},68:{},69:{},70:{},128:{},129:{T:1},130:{T:-1},131:{T:1,f:Sr,p:0},132:{T:-1},133:{T:1},134:{T:-1},135:{T:1},136:{T:-1},137:{T:1,f:Th},138:{T:-1},139:{T:1},140:{T:-1},141:{T:1},142:{T:-1},143:{T:1},144:{T:-1},145:{T:1},146:{T:-1},147:{f:O1},148:{f:F1,p:16},151:{f:dh},152:{},153:{f:$h},154:{},155:{},156:{f:Gh},157:{},158:{},159:{T:1,f:Vl},160:{T:-1},161:{T:1,f:et},162:{T:-1},163:{T:1},164:{T:-1},165:{T:1},166:{T:-1},167:{},168:{},169:{},170:{},171:{},172:{T:1},173:{T:-1},174:{},175:{},176:{f:oh},177:{T:1},178:{T:-1},179:{T:1},180:{T:-1},181:{T:1},182:{T:-1},183:{T:1},184:{T:-1},185:{T:1},186:{T:-1},187:{T:1},188:{T:-1},189:{T:1},190:{T:-1},191:{T:1},192:{T:-1},193:{T:1},194:{T:-1},195:{T:1},196:{T:-1},197:{T:1},198:{T:-1},199:{T:1},200:{T:-1},201:{T:1},202:{T:-1},203:{T:1},204:{T:-1},205:{T:1},206:{T:-1},207:{T:1},208:{T:-1},209:{T:1},210:{T:-1},211:{T:1},212:{T:-1},213:{T:1},214:{T:-1},215:{T:1},216:{T:-1},217:{T:1},218:{T:-1},219:{T:1},220:{T:-1},221:{T:1},222:{T:-1},223:{T:1},224:{T:-1},225:{T:1},226:{T:-1},227:{T:1},228:{T:-1},229:{T:1},230:{T:-1},231:{T:1},232:{T:-1},233:{T:1},234:{T:-1},235:{T:1},236:{T:-1},237:{T:1},238:{T:-1},239:{T:1},240:{T:-1},241:{T:1},242:{T:-1},243:{T:1},244:{T:-1},245:{T:1},246:{T:-1},247:{T:1},248:{T:-1},249:{T:1},250:{T:-1},251:{T:1},252:{T:-1},253:{T:1},254:{T:-1},255:{T:1},256:{T:-1},257:{T:1},258:{T:-1},259:{T:1},260:{T:-1},261:{T:1},262:{T:-1},263:{T:1},264:{T:-1},265:{T:1},266:{T:-1},267:{T:1},268:{T:-1},269:{T:1},270:{T:-1},271:{T:1},272:{T:-1},273:{T:1},274:{T:-1},275:{T:1},276:{T:-1},277:{},278:{T:1},279:{T:-1},280:{T:1},281:{T:-1},282:{T:1},283:{T:1},284:{T:-1},285:{T:1},286:{T:-1},287:{T:1},288:{T:-1},289:{T:1},290:{T:-1},291:{T:1},292:{T:-1},293:{T:1},294:{T:-1},295:{T:1},296:{T:-1},297:{T:1},298:{T:-1},299:{T:1},300:{T:-1},301:{T:1},302:{T:-1},303:{T:1},304:{T:-1},305:{T:1},306:{T:-1},307:{T:1},308:{T:-1},309:{T:1},310:{T:-1},311:{T:1},312:{T:-1},313:{T:-1},314:{T:1},315:{T:-1},316:{T:1},317:{T:-1},318:{T:1},319:{T:-1},320:{T:1},321:{T:-1},322:{T:1},323:{T:-1},324:{T:1},325:{T:-1},326:{T:1},327:{T:-1},328:{T:1},329:{T:-1},330:{T:1},331:{T:-1},332:{T:1},333:{T:-1},334:{T:1},335:{f:To},336:{T:-1},337:{f:Fo,T:1},338:{T:-1},339:{T:1},340:{T:-1},341:{T:1},342:{T:-1},343:{T:1},344:{T:-1},345:{T:1},346:{T:-1},347:{T:1},348:{T:-1},349:{T:1},350:{T:-1},351:{},352:{},353:{T:1},354:{T:-1},355:{f:bn},357:{},358:{},359:{},360:{T:1},361:{},362:{f:yl},363:{},364:{},366:{},367:{},368:{},369:{},370:{},371:{},372:{T:1},373:{T:-1},374:{T:1},375:{T:-1},376:{T:1},377:{T:-1},378:{T:1},379:{T:-1},380:{T:1},381:{T:-1},382:{T:1},383:{T:-1},384:{T:1},385:{T:-1},386:{T:1},387:{T:-1},388:{T:1},389:{T:-1},390:{T:1},391:{T:-1},392:{T:1},393:{T:-1},394:{T:1},395:{T:-1},396:{},397:{},398:{},399:{},400:{},401:{T:1},403:{},404:{},405:{},406:{},407:{},408:{},409:{},410:{},411:{},412:{},413:{},414:{},415:{},416:{},417:{},418:{},419:{},420:{},421:{},422:{T:1},423:{T:1},424:{T:-1},425:{T:-1},426:{f:vh},427:{f:ph},428:{},429:{T:1},430:{T:-1},431:{T:1},432:{T:-1},433:{T:1},434:{T:-1},435:{T:1},436:{T:-1},437:{T:1},438:{T:-1},439:{T:1},440:{T:-1},441:{T:1},442:{T:-1},443:{T:1},444:{T:-1},445:{T:1},446:{T:-1},447:{T:1},448:{T:-1},449:{T:1},450:{T:-1},451:{T:1},452:{T:-1},453:{T:1},454:{T:-1},455:{T:1},456:{T:-1},457:{T:1},458:{T:-1},459:{T:1},460:{T:-1},461:{T:1},462:{T:-1},463:{T:1},464:{T:-1},465:{T:1},466:{T:-1},467:{T:1},468:{T:-1},469:{T:1},470:{T:-1},471:{},472:{},473:{T:1},474:{T:-1},475:{},476:{f:gh},477:{},478:{},479:{T:1},480:{T:-1},481:{T:1},482:{T:-1},483:{T:1},484:{T:-1},485:{f:C1},486:{T:1},487:{T:-1},488:{T:1},489:{T:-1},490:{T:1},491:{T:-1},492:{T:1},493:{T:-1},494:{f:uh},495:{T:1},496:{T:-1},497:{T:1},498:{T:-1},499:{},500:{T:1},501:{T:-1},502:{T:1},503:{T:-1},504:{},505:{T:1},506:{T:-1},507:{},508:{T:1},509:{T:-1},510:{T:1},511:{T:-1},512:{},513:{},514:{T:1},515:{T:-1},516:{T:1},517:{T:-1},518:{T:1},519:{T:-1},520:{T:1},521:{T:-1},522:{},523:{},524:{},525:{},526:{},527:{},528:{T:1},529:{T:-1},530:{T:1},531:{T:-1},532:{T:1},533:{T:-1},534:{},535:{},536:{},537:{},538:{T:1},539:{T:-1},540:{T:1},541:{T:-1},542:{T:1},548:{},549:{},550:{f:bn},551:{},552:{},553:{},554:{T:1},555:{T:-1},556:{T:1},557:{T:-1},558:{T:1},559:{T:-1},560:{T:1},561:{T:-1},562:{},564:{},565:{T:1},566:{T:-1},569:{T:1},570:{T:-1},572:{},573:{T:1},574:{T:-1},577:{},578:{},579:{},580:{},581:{},582:{},583:{},584:{},585:{},586:{},587:{},588:{T:-1},589:{},590:{T:1},591:{T:-1},592:{T:1},593:{T:-1},594:{T:1},595:{T:-1},596:{},597:{T:1},598:{T:-1},599:{T:1},600:{T:-1},601:{T:1},602:{T:-1},603:{T:1},604:{T:-1},605:{T:1},606:{T:-1},607:{},608:{T:1},609:{T:-1},610:{},611:{T:1},612:{T:-1},613:{T:1},614:{T:-1},615:{T:1},616:{T:-1},617:{T:1},618:{T:-1},619:{T:1},620:{T:-1},625:{},626:{T:1},627:{T:-1},628:{T:1},629:{T:-1},630:{T:1},631:{T:-1},632:{f:Io},633:{T:1},634:{T:-1},635:{T:1,f:No},636:{T:-1},637:{f:Lf},638:{T:1},639:{},640:{T:-1},641:{T:1},642:{T:-1},643:{T:1},644:{},645:{T:-1},646:{T:1},648:{T:1},649:{},650:{T:-1},651:{f:Mh},652:{},653:{T:1},654:{T:-1},655:{T:1},656:{T:-1},657:{T:1},658:{T:-1},659:{},660:{T:1},661:{},662:{T:-1},663:{},664:{T:1},665:{},666:{T:-1},667:{},668:{},669:{},671:{T:1},672:{T:-1},673:{T:1},674:{T:-1},675:{},676:{},677:{},678:{},679:{},680:{},681:{},1024:{},1025:{},1026:{T:1},1027:{T:-1},1028:{T:1},1029:{T:-1},1030:{},1031:{T:1},1032:{T:-1},1033:{T:1},1034:{T:-1},1035:{},1036:{},1037:{},1038:{T:1},1039:{T:-1},1040:{},1041:{T:1},1042:{T:-1},1043:{},1044:{},1045:{},1046:{T:1},1047:{T:-1},1048:{T:1},1049:{T:-1},1050:{},1051:{T:1},1052:{T:1},1053:{f:Fh},1054:{T:1},1055:{},1056:{T:1},1057:{T:-1},1058:{T:1},1059:{T:-1},1061:{},1062:{T:1},1063:{T:-1},1064:{T:1},1065:{T:-1},1066:{T:1},1067:{T:-1},1068:{T:1},1069:{T:-1},1070:{T:1},1071:{T:-1},1072:{T:1},1073:{T:-1},1075:{T:1},1076:{T:-1},1077:{T:1},1078:{T:-1},1079:{T:1},1080:{T:-1},1081:{T:1},1082:{T:-1},1083:{T:1},1084:{T:-1},1085:{},1086:{T:1},1087:{T:-1},1088:{T:1},1089:{T:-1},1090:{T:1},1091:{T:-1},1092:{T:1},1093:{T:-1},1094:{T:1},1095:{T:-1},1096:{},1097:{T:1},1098:{},1099:{T:-1},1100:{T:1},1101:{T:-1},1102:{},1103:{},1104:{},1105:{},1111:{},1112:{},1113:{T:1},1114:{T:-1},1115:{T:1},1116:{T:-1},1117:{},1118:{T:1},1119:{T:-1},1120:{T:1},1121:{T:-1},1122:{T:1},1123:{T:-1},1124:{T:1},1125:{T:-1},1126:{},1128:{T:1},1129:{T:-1},1130:{},1131:{T:1},1132:{T:-1},1133:{T:1},1134:{T:-1},1135:{T:1},1136:{T:-1},1137:{T:1},1138:{T:-1},1139:{T:1},1140:{T:-1},1141:{},1142:{T:1},1143:{T:-1},1144:{T:1},1145:{T:-1},1146:{},1147:{T:1},1148:{T:-1},1149:{T:1},1150:{T:-1},1152:{T:1},1153:{T:-1},1154:{T:-1},1155:{T:-1},1156:{T:-1},1157:{T:1},1158:{T:-1},1159:{T:1},1160:{T:-1},1161:{T:1},1162:{T:-1},1163:{T:1},1164:{T:-1},1165:{T:1},1166:{T:-1},1167:{T:1},1168:{T:-1},1169:{T:1},1170:{T:-1},1171:{},1172:{T:1},1173:{T:-1},1177:{},1178:{T:1},1180:{},1181:{},1182:{},2048:{T:1},2049:{T:-1},2050:{},2051:{T:1},2052:{T:-1},2053:{},2054:{},2055:{T:1},2056:{T:-1},2057:{T:1},2058:{T:-1},2060:{},2067:{},2068:{T:1},2069:{T:-1},2070:{},2071:{},2072:{T:1},2073:{T:-1},2075:{},2076:{},2077:{T:1},2078:{T:-1},2079:{},2080:{T:1},2081:{T:-1},2082:{},2083:{T:1},2084:{T:-1},2085:{T:1},2086:{T:-1},2087:{T:1},2088:{T:-1},2089:{T:1},2090:{T:-1},2091:{},2092:{},2093:{T:1},2094:{T:-1},2095:{},2096:{T:1},2097:{T:-1},2098:{T:1},2099:{T:-1},2100:{T:1},2101:{T:-1},2102:{},2103:{T:1},2104:{T:-1},2105:{},2106:{T:1},2107:{T:-1},2108:{},2109:{T:1},2110:{T:-1},2111:{T:1},2112:{T:-1},2113:{T:1},2114:{T:-1},2115:{},2116:{},2117:{},2118:{T:1},2119:{T:-1},2120:{},2121:{T:1},2122:{T:-1},2123:{T:1},2124:{T:-1},2125:{},2126:{T:1},2127:{T:-1},2128:{},2129:{T:1},2130:{T:-1},2131:{T:1},2132:{T:-1},2133:{T:1},2134:{},2135:{},2136:{},2137:{T:1},2138:{T:-1},2139:{T:1},2140:{T:-1},2141:{},3072:{},3073:{},4096:{T:1},4097:{T:-1},5002:{T:1},5003:{T:-1},5081:{T:1},5082:{T:-1},5083:{},5084:{T:1},5085:{T:-1},5086:{T:1},5087:{T:-1},5088:{},5089:{},5090:{},5092:{T:1},5093:{T:-1},5094:{},5095:{T:1},5096:{T:-1},5097:{},5099:{},65535:{n:""}};function Z(e,t,r,n){var a=t;if(!isNaN(a)){var i=n||(r||[]).length||0,s=e.next(4);s.write_shift(2,a),s.write_shift(2,i),i>0&&jn(r)&&e.push(r)}}function Tu(e,t,r,n){var a=n||(r||[]).length||0;if(a<=8224)return Z(e,t,r,a);var i=t;if(!isNaN(i)){for(var s=r.parts||[],f=0,o=0,l=0;l+(s[f]||8224)<=8224;)l+=s[f]||8224,f++;var c=e.next(4);for(c.write_shift(2,i),c.write_shift(2,l),e.push(r.slice(o,o+l)),o+=l;o<a;){for(c=e.next(4),c.write_shift(2,60),l=0;l+(s[f]||8224)<=8224;)l+=s[f]||8224,f++;c.write_shift(2,l),e.push(r.slice(o,o+l)),o+=l}}}function bt(e,t,r){return e||(e=b(7)),e.write_shift(2,t),e.write_shift(2,r),e.write_shift(2,0),e.write_shift(1,0),e}function Eu(e,t,r,n){var a=b(9);return bt(a,e,t),si(r,n||"b",a),a}function wu(e,t,r){var n=b(8+2*r.length);return bt(n,e,t),n.write_shift(1,r.length),n.write_shift(r.length,r,"sbcs"),n.l<n.length?n.slice(0,n.l):n}function Su(e,t,r,n){if(t.v!=null)switch(t.t){case"d":case"n":var a=t.t=="d"?rr(qe(t.v)):t.v;a==(a|0)&&a>=0&&a<65536?Z(e,2,Bl(r,n,a)):Z(e,3,Ll(r,n,a));return;case"b":case"e":Z(e,5,Eu(r,n,t.v,t.t));return;case"s":case"str":Z(e,4,wu(r,n,(t.v||"").slice(0,255)));return}Z(e,1,bt(null,r,n))}function Au(e,t,r,n){var a=Array.isArray(t),i=Ae(t["!ref"]||"A1"),s,f="",o=[];if(i.e.c>255||i.e.r>16383){if(n.WTF)throw new Error("Range "+(t["!ref"]||"A1")+" exceeds format limit A1:IV16384");i.e.c=Math.min(i.e.c,255),i.e.r=Math.min(i.e.c,16383),s=ke(i)}for(var l=i.s.r;l<=i.e.r;++l){f=Ge(l);for(var c=i.s.c;c<=i.e.c;++c){l===i.s.r&&(o[c]=ze(c)),s=o[c]+f;var v=a?(t[l]||[])[c]:t[s];!v||Su(e,v,l,c)}}}function Fu(e,t){for(var r=t||{},n=er(),a=0,i=0;i<e.SheetNames.length;++i)e.SheetNames[i]==r.sheet&&(a=i);if(a==0&&!!r.sheet&&e.SheetNames[0]!=r.sheet)throw new Error("Sheet not found: "+r.sheet);return Z(n,r.biff==4?1033:r.biff==3?521:9,e0(e,16,r)),Au(n,e.Sheets[e.SheetNames[a]],a,r),Z(n,10),n.end()}function yu(e,t,r){Z(e,49,gl({sz:12,color:{theme:1},name:"Arial",family:2,scheme:"minor"},r))}function Cu(e,t,r){!t||[[5,8],[23,26],[41,44],[50,392]].forEach(function(n){for(var a=n[0];a<=n[1];++a)t[a]!=null&&Z(e,1054,El(a,t[a],r))})}function Ou(e,t){var r=b(19);r.write_shift(4,2151),r.write_shift(4,0),r.write_shift(4,0),r.write_shift(2,3),r.write_shift(1,1),r.write_shift(4,0),Z(e,2151,r),r=b(39),r.write_shift(4,2152),r.write_shift(4,0),r.write_shift(4,0),r.write_shift(2,3),r.write_shift(1,0),r.write_shift(4,0),r.write_shift(2,1),r.write_shift(4,4),r.write_shift(2,0),oi(Ae(t["!ref"]||"A1"),r),r.write_shift(4,4),Z(e,2152,r)}function Du(e,t){for(var r=0;r<16;++r)Z(e,224,Q0({numFmtId:0,style:!0},0,t));t.cellXfs.forEach(function(n){Z(e,224,Q0(n,0,t))})}function Ru(e,t){for(var r=0;r<t["!links"].length;++r){var n=t["!links"][r];Z(e,440,Dl(n)),n[1].Tooltip&&Z(e,2048,Rl(n))}delete t["!links"]}function Nu(e,t){if(!!t){var r=0;t.forEach(function(n,a){++r<=256&&n&&Z(e,125,Il(En(a,n),a))})}}function ku(e,t,r,n,a){var i=16+Wr(a.cellXfs,t,a);if(t.v==null&&!t.bf){Z(e,513,Yr(r,n,i));return}if(t.bf)Z(e,6,t1(t,r,n,a,i));else switch(t.t){case"d":case"n":var s=t.t=="d"?rr(qe(t.v)):t.v;Z(e,515,Fl(r,n,s,i));break;case"b":case"e":Z(e,517,Al(r,n,t.v,i,a,t.t));break;case"s":case"str":if(a.bookSST){var f=i0(a.Strings,t.v,a.revStrings);Z(e,253,_l(r,n,f,i))}else Z(e,516,Tl(r,n,(t.v||"").slice(0,255),i,a));break;default:Z(e,513,Yr(r,n,i))}}function Iu(e,t,r){var n=er(),a=r.SheetNames[e],i=r.Sheets[a]||{},s=(r||{}).Workbook||{},f=(s.Sheets||[])[e]||{},o=Array.isArray(i),l=t.biff==8,c,v="",x=[],d=Ae(i["!ref"]||"A1"),T=l?65536:16384;if(d.e.c>255||d.e.r>=T){if(t.WTF)throw new Error("Range "+(i["!ref"]||"A1")+" exceeds format limit A1:IV16384");d.e.c=Math.min(d.e.c,255),d.e.r=Math.min(d.e.c,T-1)}Z(n,2057,e0(r,16,t)),Z(n,13,hr(1)),Z(n,12,hr(100)),Z(n,15,Ze(!0)),Z(n,17,Ze(!1)),Z(n,16,jr(.001)),Z(n,95,Ze(!0)),Z(n,42,Ze(!1)),Z(n,43,Ze(!1)),Z(n,130,hr(1)),Z(n,128,Sl([0,0])),Z(n,131,Ze(!1)),Z(n,132,Ze(!1)),l&&Nu(n,i["!cols"]),Z(n,512,wl(d,t)),l&&(i["!links"]=[]);for(var u=d.s.r;u<=d.e.r;++u){v=Ge(u);for(var g=d.s.c;g<=d.e.c;++g){u===d.s.r&&(x[g]=ze(g)),c=x[g]+v;var R=o?(i[u]||[])[g]:i[c];!R||(ku(n,R,u,g,t),l&&R.l&&i["!links"].push([c,R.l]))}}var C=f.CodeName||f.name||a;return l&&Z(n,574,ml((s.Views||[])[0])),l&&(i["!merges"]||[]).length&&Z(n,229,Ol(i["!merges"])),l&&Ru(n,i),Z(n,442,li(C)),l&&Ou(n,i),Z(n,10),n.end()}function Pu(e,t,r){var n=er(),a=(e||{}).Workbook||{},i=a.Sheets||[],s=a.WBProps||{},f=r.biff==8,o=r.biff==5;if(Z(n,2057,e0(e,5,r)),r.bookType=="xla"&&Z(n,135),Z(n,225,f?hr(1200):null),Z(n,193,al(2)),o&&Z(n,191),o&&Z(n,192),Z(n,226),Z(n,92,xl("SheetJS",r)),Z(n,66,hr(f?1200:1252)),f&&Z(n,353,hr(0)),f&&Z(n,448),Z(n,317,Pl(e.SheetNames.length)),f&&e.vbaraw&&Z(n,211),f&&e.vbaraw){var l=s.CodeName||"ThisWorkbook";Z(n,442,li(l))}Z(n,156,hr(17)),Z(n,25,Ze(!1)),Z(n,18,Ze(!1)),Z(n,19,hr(0)),f&&Z(n,431,Ze(!1)),f&&Z(n,444,hr(0)),Z(n,61,pl()),Z(n,64,Ze(!1)),Z(n,141,hr(0)),Z(n,34,Ze(bh(e)=="true")),Z(n,14,Ze(!0)),f&&Z(n,439,Ze(!1)),Z(n,218,hr(0)),yu(n,e,r),Cu(n,e.SSF,r),Du(n,r),f&&Z(n,352,Ze(!1));var c=n.end(),v=er();f&&Z(v,140,Nl()),f&&r.Strings&&Tu(v,252,vl(r.Strings)),Z(v,10);var x=v.end(),d=er(),T=0,u=0;for(u=0;u<e.SheetNames.length;++u)T+=(f?12:11)+(f?2:1)*e.SheetNames[u].length;var g=c.length+T+x.length;for(u=0;u<e.SheetNames.length;++u){var R=i[u]||{};Z(d,133,dl({pos:g,hs:R.Hidden||0,dt:0,name:e.SheetNames[u]},r)),g+=t[u].length}var C=d.end();if(T!=C.length)throw new Error("BS8 "+T+" != "+C.length);var y=[];return c.length&&y.push(c),C.length&&y.push(C),x.length&&y.push(x),He(y)}function Lu(e,t){var r=t||{},n=[];e&&!e.SSF&&(e.SSF=tr(Re)),e&&e.SSF&&(pn(),vn(e.SSF),r.revssf=mn(e.SSF),r.revssf[e.SSF[65535]]=0,r.ssf=e.SSF),r.Strings=[],r.Strings.Count=0,r.Strings.Unique=0,s0(r),r.cellXfs=[],Wr(r.cellXfs,{},{revssf:{General:0}}),e.Props||(e.Props={});for(var a=0;a<e.SheetNames.length;++a)n[n.length]=Iu(a,r,e);return n.unshift(Pu(e,n,r)),He(n)}function Li(e,t){for(var r=0;r<=e.SheetNames.length;++r){var n=e.Sheets[e.SheetNames[r]];if(!(!n||!n["!ref"])){var a=fr(n["!ref"]);a.e.c>255&&typeof console<"u"&&console.error&&console.error("Worksheet '"+e.SheetNames[r]+"' extends beyond column IV (255).  Data may be lost.")}}var i=t||{};switch(i.biff||2){case 8:case 5:return Lu(e,t);case 4:case 3:case 2:return Fu(e,t)}throw new Error("invalid type "+i.bookType+" for BIFF")}function Bu(e,t,r,n){for(var a=e["!merges"]||[],i=[],s=t.s.c;s<=t.e.c;++s){for(var f=0,o=0,l=0;l<a.length;++l)if(!(a[l].s.r>r||a[l].s.c>s)&&!(a[l].e.r<r||a[l].e.c<s)){if(a[l].s.r<r||a[l].s.c<s){f=-1;break}f=a[l].e.r-a[l].s.r+1,o=a[l].e.c-a[l].s.c+1;break}if(!(f<0)){var c=Ee({r,c:s}),v=n.dense?(e[r]||[])[s]:e[c],x=v&&v.v!=null&&(v.h||uf(v.w||(kr(v),v.w)||""))||"",d={};f>1&&(d.rowspan=f),o>1&&(d.colspan=o),n.editable?x='<span contenteditable="true">'+x+"</span>":v&&(d["data-t"]=v&&v.t||"z",v.v!=null&&(d["data-v"]=v.v),v.z!=null&&(d["data-z"]=v.z),v.l&&(v.l.Target||"#").charAt(0)!="#"&&(x='<a href="'+v.l.Target+'">'+x+"</a>")),d.id=(n.id||"sjs")+"-"+c,i.push(J("td",x,d))}}var T="<tr>";return T+i.join("")+"</tr>"}var Mu='<html><head><meta charset="utf-8"/><title>SheetJS Table Export</title></head><body>',Uu="</body></html>";function bu(e,t,r){var n=[];return n.join("")+"<table"+(r&&r.id?' id="'+r.id+'"':"")+">"}function Bi(e,t){var r=t||{},n=r.header!=null?r.header:Mu,a=r.footer!=null?r.footer:Uu,i=[n],s=fr(e["!ref"]);r.dense=Array.isArray(e),i.push(bu(e,s,r));for(var f=s.s.r;f<=s.e.r;++f)i.push(Bu(e,s,f,r));return i.push("</table>"+a),i.join("")}function Mi(e,t,r){var n=r||{},a=0,i=0;if(n.origin!=null)if(typeof n.origin=="number")a=n.origin;else{var s=typeof n.origin=="string"?Be(n.origin):n.origin;a=s.r,i=s.c}var f=t.getElementsByTagName("tr"),o=Math.min(n.sheetRows||1e7,f.length),l={s:{r:0,c:0},e:{r:a,c:i}};if(e["!ref"]){var c=fr(e["!ref"]);l.s.r=Math.min(l.s.r,c.s.r),l.s.c=Math.min(l.s.c,c.s.c),l.e.r=Math.max(l.e.r,c.e.r),l.e.c=Math.max(l.e.c,c.e.c),a==-1&&(l.e.r=a=c.e.r+1)}var v=[],x=0,d=e["!rows"]||(e["!rows"]=[]),T=0,u=0,g=0,R=0,C=0,y=0;for(e["!cols"]||(e["!cols"]=[]);T<f.length&&u<o;++T){var U=f[T];if(sa(U)){if(n.display)continue;d[u]={hidden:!0}}var Y=U.children;for(g=R=0;g<Y.length;++g){var Q=Y[g];if(!(n.display&&sa(Q))){var O=Q.hasAttribute("data-v")?Q.getAttribute("data-v"):Q.hasAttribute("v")?Q.getAttribute("v"):pf(Q.innerHTML),V=Q.getAttribute("data-z")||Q.getAttribute("z");for(x=0;x<v.length;++x){var B=v[x];B.s.c==R+i&&B.s.r<u+a&&u+a<=B.e.r&&(R=B.e.c+1-i,x=-1)}y=+Q.getAttribute("colspan")||1,((C=+Q.getAttribute("rowspan")||1)>1||y>1)&&v.push({s:{r:u+a,c:R+i},e:{r:u+a+(C||1)-1,c:R+i+(y||1)-1}});var I={t:"s",v:O},M=Q.getAttribute("data-t")||Q.getAttribute("t")||"";O!=null&&(O.length==0?I.t=M||"z":n.raw||O.trim().length==0||M=="s"||(O==="TRUE"?I={t:"b",v:!0}:O==="FALSE"?I={t:"b",v:!1}:isNaN(Dr(O))?isNaN(Rt(O).getDate())||(I={t:"d",v:qe(O)},n.cellDates||(I={t:"n",v:rr(I.v)}),I.z=n.dateNF||Re[14]):I={t:"n",v:Dr(O)})),I.z===void 0&&V!=null&&(I.z=V);var H="",K=Q.getElementsByTagName("A");if(K&&K.length)for(var ae=0;ae<K.length&&!(K[ae].hasAttribute("href")&&(H=K[ae].getAttribute("href"),H.charAt(0)!="#"));++ae);H&&H.charAt(0)!="#"&&(I.l={Target:H}),n.dense?(e[u+a]||(e[u+a]=[]),e[u+a][R+i]=I):e[Ee({c:R+i,r:u+a})]=I,l.e.c<R+i&&(l.e.c=R+i),R+=y}}++u}return v.length&&(e["!merges"]=(e["!merges"]||[]).concat(v)),l.e.r=Math.max(l.e.r,u-1+a),e["!ref"]=ke(l),u>=o&&(e["!fullref"]=ke((l.e.r=f.length-T+u-1+a,l))),e}function Ui(e,t){var r=t||{},n=r.dense?[]:{};return Mi(n,e,t)}function Wu(e,t){return Jr(Ui(e,t),t)}function sa(e){var t="",r=Hu(e);return r&&(t=r(e).getPropertyValue("display")),t||(t=e.style&&e.style.display),t==="none"}function Hu(e){return e.ownerDocument.defaultView&&typeof e.ownerDocument.defaultView.getComputedStyle=="function"?e.ownerDocument.defaultView.getComputedStyle:typeof getComputedStyle=="function"?getComputedStyle:null}var Vu=function(){var e=["<office:master-styles>",'<style:master-page style:name="mp1" style:page-layout-name="mp1">',"<style:header/>",'<style:header-left style:display="false"/>',"<style:footer/>",'<style:footer-left style:display="false"/>',"</style:master-page>","</office:master-styles>"].join(""),t="<office:document-styles "+Nt({"xmlns:office":"urn:oasis:names:tc:opendocument:xmlns:office:1.0","xmlns:table":"urn:oasis:names:tc:opendocument:xmlns:table:1.0","xmlns:style":"urn:oasis:names:tc:opendocument:xmlns:style:1.0","xmlns:text":"urn:oasis:names:tc:opendocument:xmlns:text:1.0","xmlns:draw":"urn:oasis:names:tc:opendocument:xmlns:drawing:1.0","xmlns:fo":"urn:oasis:names:tc:opendocument:xmlns:xsl-fo-compatible:1.0","xmlns:xlink":"http://www.w3.org/1999/xlink","xmlns:dc":"http://purl.org/dc/elements/1.1/","xmlns:number":"urn:oasis:names:tc:opendocument:xmlns:datastyle:1.0","xmlns:svg":"urn:oasis:names:tc:opendocument:xmlns:svg-compatible:1.0","xmlns:of":"urn:oasis:names:tc:opendocument:xmlns:of:1.2","office:version":"1.2"})+">"+e+"</office:document-styles>";return function(){return Ie+t}}(),fa=function(){var e=function(i){return Te(i).replace(/  +/g,function(s){return'<text:s text:c="'+s.length+'"/>'}).replace(/\t/g,"<text:tab/>").replace(/\n/g,"</text:p><text:p>").replace(/^ /,"<text:s/>").replace(/ $/,"<text:s/>")},t=`          <table:table-cell />
`,r=`          <table:covered-table-cell/>
`,n=function(i,s,f){var o=[];o.push('      <table:table table:name="'+Te(s.SheetNames[f])+`" table:style-name="ta1">
`);var l=0,c=0,v=fr(i["!ref"]||"A1"),x=i["!merges"]||[],d=0,T=Array.isArray(i);if(i["!cols"])for(c=0;c<=v.e.c;++c)o.push("        <table:table-column"+(i["!cols"][c]?' table:style-name="co'+i["!cols"][c].ods+'"':"")+`></table:table-column>
`);var u="",g=i["!rows"]||[];for(l=0;l<v.s.r;++l)u=g[l]?' table:style-name="ro'+g[l].ods+'"':"",o.push("        <table:table-row"+u+`></table:table-row>
`);for(;l<=v.e.r;++l){for(u=g[l]?' table:style-name="ro'+g[l].ods+'"':"",o.push("        <table:table-row"+u+`>
`),c=0;c<v.s.c;++c)o.push(t);for(;c<=v.e.c;++c){var R=!1,C={},y="";for(d=0;d!=x.length;++d)if(!(x[d].s.c>c)&&!(x[d].s.r>l)&&!(x[d].e.c<c)&&!(x[d].e.r<l)){(x[d].s.c!=c||x[d].s.r!=l)&&(R=!0),C["table:number-columns-spanned"]=x[d].e.c-x[d].s.c+1,C["table:number-rows-spanned"]=x[d].e.r-x[d].s.r+1;break}if(R){o.push(r);continue}var U=Ee({r:l,c}),Y=T?(i[l]||[])[c]:i[U];if(Y&&Y.f&&(C["table:formula"]=Te(l1(Y.f)),Y.F&&Y.F.slice(0,U.length)==U)){var Q=fr(Y.F);C["table:number-matrix-columns-spanned"]=Q.e.c-Q.s.c+1,C["table:number-matrix-rows-spanned"]=Q.e.r-Q.s.r+1}if(!Y){o.push(t);continue}switch(Y.t){case"b":y=Y.v?"TRUE":"FALSE",C["office:value-type"]="boolean",C["office:boolean-value"]=Y.v?"true":"false";break;case"n":y=Y.w||String(Y.v||0),C["office:value-type"]="float",C["office:value"]=Y.v||0;break;case"s":case"str":y=Y.v==null?"":Y.v,C["office:value-type"]="string";break;case"d":y=Y.w||qe(Y.v).toISOString(),C["office:value-type"]="date",C["office:date-value"]=qe(Y.v).toISOString(),C["table:style-name"]="ce1";break;default:o.push(t);continue}var O=e(y);if(Y.l&&Y.l.Target){var V=Y.l.Target;V=V.charAt(0)=="#"?"#"+o1(V.slice(1)):V,V.charAt(0)!="#"&&!V.match(/^\w+:/)&&(V="../"+V),O=J("text:a",O,{"xlink:href":V.replace(/&/g,"&amp;")})}o.push("          "+J("table:table-cell",J("text:p",O,{}),C)+`
`)}o.push(`        </table:table-row>
`)}return o.push(`      </table:table>
`),o.join("")},a=function(i,s){i.push(` <office:automatic-styles>
`),i.push(`  <number:date-style style:name="N37" number:automatic-order="true">
`),i.push(`   <number:month number:style="long"/>
`),i.push(`   <number:text>/</number:text>
`),i.push(`   <number:day number:style="long"/>
`),i.push(`   <number:text>/</number:text>
`),i.push(`   <number:year/>
`),i.push(`  </number:date-style>
`);var f=0;s.SheetNames.map(function(l){return s.Sheets[l]}).forEach(function(l){if(!!l&&l["!cols"]){for(var c=0;c<l["!cols"].length;++c)if(l["!cols"][c]){var v=l["!cols"][c];if(v.width==null&&v.wpx==null&&v.wch==null)continue;r0(v),v.ods=f;var x=l["!cols"][c].wpx+"px";i.push('  <style:style style:name="co'+f+`" style:family="table-column">
`),i.push('   <style:table-column-properties fo:break-before="auto" style:column-width="'+x+`"/>
`),i.push(`  </style:style>
`),++f}}});var o=0;s.SheetNames.map(function(l){return s.Sheets[l]}).forEach(function(l){if(!!l&&l["!rows"]){for(var c=0;c<l["!rows"].length;++c)if(l["!rows"][c]){l["!rows"][c].ods=o;var v=l["!rows"][c].hpx+"px";i.push('  <style:style style:name="ro'+o+`" style:family="table-row">
`),i.push('   <style:table-row-properties fo:break-before="auto" style:row-height="'+v+`"/>
`),i.push(`  </style:style>
`),++o}}}),i.push(`  <style:style style:name="ta1" style:family="table" style:master-page-name="mp1">
`),i.push(`   <style:table-properties table:display="true" style:writing-mode="lr-tb"/>
`),i.push(`  </style:style>
`),i.push(`  <style:style style:name="ce1" style:family="table-cell" style:parent-style-name="Default" style:data-style-name="N37"/>
`),i.push(` </office:automatic-styles>
`)};return function(s,f){var o=[Ie],l=Nt({"xmlns:office":"urn:oasis:names:tc:opendocument:xmlns:office:1.0","xmlns:table":"urn:oasis:names:tc:opendocument:xmlns:table:1.0","xmlns:style":"urn:oasis:names:tc:opendocument:xmlns:style:1.0","xmlns:text":"urn:oasis:names:tc:opendocument:xmlns:text:1.0","xmlns:draw":"urn:oasis:names:tc:opendocument:xmlns:drawing:1.0","xmlns:fo":"urn:oasis:names:tc:opendocument:xmlns:xsl-fo-compatible:1.0","xmlns:xlink":"http://www.w3.org/1999/xlink","xmlns:dc":"http://purl.org/dc/elements/1.1/","xmlns:meta":"urn:oasis:names:tc:opendocument:xmlns:meta:1.0","xmlns:number":"urn:oasis:names:tc:opendocument:xmlns:datastyle:1.0","xmlns:presentation":"urn:oasis:names:tc:opendocument:xmlns:presentation:1.0","xmlns:svg":"urn:oasis:names:tc:opendocument:xmlns:svg-compatible:1.0","xmlns:chart":"urn:oasis:names:tc:opendocument:xmlns:chart:1.0","xmlns:dr3d":"urn:oasis:names:tc:opendocument:xmlns:dr3d:1.0","xmlns:math":"http://www.w3.org/1998/Math/MathML","xmlns:form":"urn:oasis:names:tc:opendocument:xmlns:form:1.0","xmlns:script":"urn:oasis:names:tc:opendocument:xmlns:script:1.0","xmlns:ooo":"http://openoffice.org/2004/office","xmlns:ooow":"http://openoffice.org/2004/writer","xmlns:oooc":"http://openoffice.org/2004/calc","xmlns:dom":"http://www.w3.org/2001/xml-events","xmlns:xforms":"http://www.w3.org/2002/xforms","xmlns:xsd":"http://www.w3.org/2001/XMLSchema","xmlns:xsi":"http://www.w3.org/2001/XMLSchema-instance","xmlns:sheet":"urn:oasis:names:tc:opendocument:sh33tjs:1.0","xmlns:rpt":"http://openoffice.org/2005/report","xmlns:of":"urn:oasis:names:tc:opendocument:xmlns:of:1.2","xmlns:xhtml":"http://www.w3.org/1999/xhtml","xmlns:grddl":"http://www.w3.org/2003/g/data-view#","xmlns:tableooo":"http://openoffice.org/2009/table","xmlns:drawooo":"http://openoffice.org/2010/draw","xmlns:calcext":"urn:org:documentfoundation:names:experimental:calc:xmlns:calcext:1.0","xmlns:loext":"urn:org:documentfoundation:names:experimental:office:xmlns:loext:1.0","xmlns:field":"urn:openoffice:names:experimental:ooo-ms-interop:xmlns:field:1.0","xmlns:formx":"urn:openoffice:names:experimental:ooxml-odf-interop:xmlns:form:1.0","xmlns:css3t":"http://www.w3.org/TR/css3-text/","office:version":"1.2"}),c=Nt({"xmlns:config":"urn:oasis:names:tc:opendocument:xmlns:config:1.0","office:mimetype":"application/vnd.oasis.opendocument.spreadsheet"});f.bookType=="fods"?(o.push("<office:document"+l+c+`>
`),o.push(Qa().replace(/office:document-meta/g,"office:meta"))):o.push("<office:document-content"+l+`>
`),a(o,s),o.push(`  <office:body>
`),o.push(`    <office:spreadsheet>
`);for(var v=0;v!=s.SheetNames.length;++v)o.push(n(s.Sheets[s.SheetNames[v]],s,v));return o.push(`    </office:spreadsheet>
`),o.push(`  </office:body>
`),f.bookType=="fods"?o.push("</office:document>"):o.push("</office:document-content>"),o.join("")}}();function bi(e,t){if(t.bookType=="fods")return fa(e,t);var r=Xn(),n="",a=[],i=[];return n="mimetype",ue(r,n,"application/vnd.oasis.opendocument.spreadsheet"),n="content.xml",ue(r,n,fa(e,t)),a.push([n,"text/xml"]),i.push([n,"ContentFile"]),n="styles.xml",ue(r,n,Vu(e,t)),a.push([n,"text/xml"]),i.push([n,"StylesFile"]),n="meta.xml",ue(r,n,Ie+Qa()),a.push([n,"text/xml"]),i.push([n,"MetadataFile"]),n="manifest.rdf",ue(r,n,Qf(i)),a.push([n,"application/rdf+xml"]),n="META-INF/manifest.xml",ue(r,n,Zf(a)),r}/*! sheetjs (C) 2013-present SheetJS -- http://sheetjs.com */function un(e){return new DataView(e.buffer,e.byteOffset,e.byteLength)}function Gu(e){return typeof TextEncoder<"u"?new TextEncoder().encode(e):pr(Cr(e))}function Xu(e,t){e:for(var r=0;r<=e.length-t.length;++r){for(var n=0;n<t.length;++n)if(e[r+n]!=t[n])continue e;return!0}return!1}function br(e){var t=e.reduce(function(a,i){return a+i.length},0),r=new Uint8Array(t),n=0;return e.forEach(function(a){r.set(a,n),n+=a.length}),r}function $u(e,t,r){var n=Math.floor(r==0?0:Math.LOG10E*Math.log(Math.abs(r)))+6176-20,a=r/Math.pow(10,n-6176);e[t+15]|=n>>7,e[t+14]|=(n&127)<<1;for(var i=0;a>=1;++i,a/=256)e[t+i]=a&255;e[t+15]|=r>=0?0:128}function kt(e,t){var r=t?t[0]:0,n=e[r]&127;e:if(e[r++]>=128&&(n|=(e[r]&127)<<7,e[r++]<128||(n|=(e[r]&127)<<14,e[r++]<128)||(n|=(e[r]&127)<<21,e[r++]<128)||(n+=(e[r]&127)*Math.pow(2,28),++r,e[r++]<128)||(n+=(e[r]&127)*Math.pow(2,35),++r,e[r++]<128)||(n+=(e[r]&127)*Math.pow(2,42),++r,e[r++]<128)))break e;return t&&(t[0]=r),n}function ge(e){var t=new Uint8Array(7);t[0]=e&127;var r=1;e:if(e>127){if(t[r-1]|=128,t[r]=e>>7&127,++r,e<=16383||(t[r-1]|=128,t[r]=e>>14&127,++r,e<=2097151)||(t[r-1]|=128,t[r]=e>>21&127,++r,e<=268435455)||(t[r-1]|=128,t[r]=e/256>>>21&127,++r,e<=34359738367)||(t[r-1]|=128,t[r]=e/65536>>>21&127,++r,e<=4398046511103))break e;t[r-1]|=128,t[r]=e/16777216>>>21&127,++r}return t.slice(0,r)}function ht(e){var t=0,r=e[t]&127;e:if(e[t++]>=128){if(r|=(e[t]&127)<<7,e[t++]<128||(r|=(e[t]&127)<<14,e[t++]<128)||(r|=(e[t]&127)<<21,e[t++]<128))break e;r|=(e[t]&127)<<28}return r}function Pe(e){for(var t=[],r=[0];r[0]<e.length;){var n=r[0],a=kt(e,r),i=a&7;a=Math.floor(a/8);var s=0,f;if(a==0)break;switch(i){case 0:{for(var o=r[0];e[r[0]++]>=128;);f=e.slice(o,r[0])}break;case 5:s=4,f=e.slice(r[0],r[0]+s),r[0]+=s;break;case 1:s=8,f=e.slice(r[0],r[0]+s),r[0]+=s;break;case 2:s=kt(e,r),f=e.slice(r[0],r[0]+s),r[0]+=s;break;case 3:case 4:default:throw new Error("PB Type ".concat(i," for Field ").concat(a," at offset ").concat(n))}var l={data:f,type:i};t[a]==null?t[a]=[l]:t[a].push(l)}return t}function be(e){var t=[];return e.forEach(function(r,n){r.forEach(function(a){!a.data||(t.push(ge(n*8+a.type)),a.type==2&&t.push(ge(a.data.length)),t.push(a.data))})}),br(t)}function dr(e){for(var t,r=[],n=[0];n[0]<e.length;){var a=kt(e,n),i=Pe(e.slice(n[0],n[0]+a));n[0]+=a;var s={id:ht(i[1][0].data),messages:[]};i[2].forEach(function(f){var o=Pe(f.data),l=ht(o[3][0].data);s.messages.push({meta:o,data:e.slice(n[0],n[0]+l)}),n[0]+=l}),(t=i[3])!=null&&t[0]&&(s.merge=ht(i[3][0].data)>>>0>0),r.push(s)}return r}function at(e){var t=[];return e.forEach(function(r){var n=[];n[1]=[{data:ge(r.id),type:0}],n[2]=[],r.merge!=null&&(n[3]=[{data:ge(+!!r.merge),type:0}]);var a=[];r.messages.forEach(function(s){a.push(s.data),s.meta[3]=[{type:0,data:ge(s.data.length)}],n[2].push({data:be(s.meta),type:2})});var i=be(n);t.push(ge(i.length)),t.push(i),a.forEach(function(s){return t.push(s)})}),br(t)}function zu(e,t){if(e!=0)throw new Error("Unexpected Snappy chunk type ".concat(e));for(var r=[0],n=kt(t,r),a=[];r[0]<t.length;){var i=t[r[0]]&3;if(i==0){var s=t[r[0]++]>>2;if(s<60)++s;else{var f=s-59;s=t[r[0]],f>1&&(s|=t[r[0]+1]<<8),f>2&&(s|=t[r[0]+2]<<16),f>3&&(s|=t[r[0]+3]<<24),s>>>=0,s++,r[0]+=f}a.push(t.slice(r[0],r[0]+s)),r[0]+=s;continue}else{var o=0,l=0;if(i==1?(l=(t[r[0]]>>2&7)+4,o=(t[r[0]++]&224)<<3,o|=t[r[0]++]):(l=(t[r[0]++]>>2)+1,i==2?(o=t[r[0]]|t[r[0]+1]<<8,r[0]+=2):(o=(t[r[0]]|t[r[0]+1]<<8|t[r[0]+2]<<16|t[r[0]+3]<<24)>>>0,r[0]+=4)),a=[br(a)],o==0)throw new Error("Invalid offset 0");if(o>a[0].length)throw new Error("Invalid offset beyond length");if(l>=o)for(a.push(a[0].slice(-o)),l-=o;l>=a[a.length-1].length;)a.push(a[a.length-1]),l-=a[a.length-1].length;a.push(a[0].slice(-o,-o+l))}}var c=br(a);if(c.length!=n)throw new Error("Unexpected length: ".concat(c.length," != ").concat(n));return c}function vr(e){for(var t=[],r=0;r<e.length;){var n=e[r++],a=e[r]|e[r+1]<<8|e[r+2]<<16;r+=3,t.push(zu(n,e.slice(r,r+a))),r+=a}if(r!==e.length)throw new Error("data is not a valid framed stream!");return br(t)}function it(e){for(var t=[],r=0;r<e.length;){var n=Math.min(e.length-r,268435455),a=new Uint8Array(4);t.push(a);var i=ge(n),s=i.length;t.push(i),n<=60?(s++,t.push(new Uint8Array([n-1<<2]))):n<=256?(s+=2,t.push(new Uint8Array([240,n-1&255]))):n<=65536?(s+=3,t.push(new Uint8Array([244,n-1&255,n-1>>8&255]))):n<=16777216?(s+=4,t.push(new Uint8Array([248,n-1&255,n-1>>8&255,n-1>>16&255]))):n<=4294967296&&(s+=5,t.push(new Uint8Array([252,n-1&255,n-1>>8&255,n-1>>16&255,n-1>>>24&255]))),t.push(e.slice(r,r+n)),s+=n,a[0]=0,a[1]=s&255,a[2]=s>>8&255,a[3]=s>>16&255,r+=n}return br(t)}function Ln(e,t){var r=new Uint8Array(32),n=un(r),a=12,i=0;switch(r[0]=5,e.t){case"n":r[1]=2,$u(r,a,e.v),i|=1,a+=16;break;case"b":r[1]=6,n.setFloat64(a,e.v?1:0,!0),i|=2,a+=8;break;case"s":if(t.indexOf(e.v)==-1)throw new Error("Value ".concat(e.v," missing from SST!"));r[1]=3,n.setUint32(a,t.indexOf(e.v),!0),i|=8,a+=4;break;default:throw"unsupported cell type "+e.t}return n.setUint32(8,i,!0),r.slice(0,a)}function Bn(e,t){var r=new Uint8Array(32),n=un(r),a=12,i=0;switch(r[0]=3,e.t){case"n":r[2]=2,n.setFloat64(a,e.v,!0),i|=32,a+=8;break;case"b":r[2]=6,n.setFloat64(a,e.v?1:0,!0),i|=32,a+=8;break;case"s":if(t.indexOf(e.v)==-1)throw new Error("Value ".concat(e.v," missing from SST!"));r[2]=3,n.setUint32(a,t.indexOf(e.v),!0),i|=16,a+=4;break;default:throw"unsupported cell type "+e.t}return n.setUint32(4,i,!0),r.slice(0,a)}function Pr(e){var t=Pe(e);return kt(t[1][0].data)}function Ku(e,t,r){var n,a,i,s;if(!((n=e[6])!=null&&n[0])||!((a=e[7])!=null&&a[0]))throw"Mutation only works on post-BNC storages!";var f=((s=(i=e[8])==null?void 0:i[0])==null?void 0:s.data)&&ht(e[8][0].data)>0||!1;if(f)throw"Math only works with normal offsets";for(var o=0,l=un(e[7][0].data),c=0,v=[],x=un(e[4][0].data),d=0,T=[],u=0;u<t.length;++u){if(t[u]==null){l.setUint16(u*2,65535,!0),x.setUint16(u*2,65535);continue}l.setUint16(u*2,c,!0),x.setUint16(u*2,d,!0);var g,R;switch(typeof t[u]){case"string":g=Ln({t:"s",v:t[u]},r),R=Bn({t:"s",v:t[u]},r);break;case"number":g=Ln({t:"n",v:t[u]},r),R=Bn({t:"n",v:t[u]},r);break;case"boolean":g=Ln({t:"b",v:t[u]},r),R=Bn({t:"b",v:t[u]},r);break;default:throw new Error("Unsupported value "+t[u])}v.push(g),c+=g.length,T.push(R),d+=R.length,++o}for(e[2][0].data=ge(o);u<e[7][0].data.length/2;++u)l.setUint16(u*2,65535,!0),x.setUint16(u*2,65535,!0);return e[6][0].data=br(v),e[3][0].data=br(T),o}function ju(e,t){if(!t||!t.numbers)throw new Error("Must pass a `numbers` option -- check the README");var r=e.Sheets[e.SheetNames[0]];e.SheetNames.length>1&&console.error("The Numbers writer currently writes only the first table");var n=fr(r["!ref"]);n.s.r=n.s.c=0;var a=!1;n.e.c>9&&(a=!0,n.e.c=9),n.e.r>49&&(a=!0,n.e.r=49),a&&console.error("The Numbers writer is currently limited to ".concat(ke(n)));var i=xn(r,{range:n,header:1}),s=["~Sh33tJ5~"];i.forEach(function(D){return D.forEach(function(F){typeof F=="string"&&s.push(F)})});var f={},o=[],l=we.read(t.numbers,{type:"base64"});l.FileIndex.map(function(D,F){return[D,l.FullPaths[F]]}).forEach(function(D){var F=D[0],A=D[1];if(F.type==2&&!!F.name.match(/\.iwa/)){var X=F.content,le=vr(X),oe=dr(le);oe.forEach(function(se){o.push(se.id),f[se.id]={deps:[],location:A,type:ht(se.messages[0].meta[1][0].data)}})}}),o.sort(function(D,F){return D-F});var c=o.filter(function(D){return D>1}).map(function(D){return[D,ge(D)]});l.FileIndex.map(function(D,F){return[D,l.FullPaths[F]]}).forEach(function(D){var F=D[0];if(D[1],!!F.name.match(/\.iwa/)){var A=dr(vr(F.content));A.forEach(function(X){X.messages.forEach(function(le){c.forEach(function(oe){X.messages.some(function(se){return ht(se.meta[1][0].data)!=11006&&Xu(se.data,oe[1])})&&f[oe[0]].deps.push(X.id)})})})}});for(var v=we.find(l,f[1].location),x=dr(vr(v.content)),d,T=0;T<x.length;++T){var u=x[T];u.id==1&&(d=u)}var g=Pr(Pe(d.messages[0].data)[1][0].data);for(v=we.find(l,f[g].location),x=dr(vr(v.content)),T=0;T<x.length;++T)u=x[T],u.id==g&&(d=u);for(g=Pr(Pe(d.messages[0].data)[2][0].data),v=we.find(l,f[g].location),x=dr(vr(v.content)),T=0;T<x.length;++T)u=x[T],u.id==g&&(d=u);for(g=Pr(Pe(d.messages[0].data)[2][0].data),v=we.find(l,f[g].location),x=dr(vr(v.content)),T=0;T<x.length;++T)u=x[T],u.id==g&&(d=u);var R=Pe(d.messages[0].data);{R[6][0].data=ge(n.e.r+1),R[7][0].data=ge(n.e.c+1);var C=Pr(R[46][0].data),y=we.find(l,f[C].location),U=dr(vr(y.content));{for(var Y=0;Y<U.length&&U[Y].id!=C;++Y);if(U[Y].id!=C)throw"Bad ColumnRowUIDMapArchive";var Q=Pe(U[Y].messages[0].data);Q[1]=[],Q[2]=[],Q[3]=[];for(var O=0;O<=n.e.c;++O){var V=[];V[1]=V[2]=[{type:0,data:ge(O+420690)}],Q[1].push({type:2,data:be(V)}),Q[2].push({type:0,data:ge(O)}),Q[3].push({type:0,data:ge(O)})}Q[4]=[],Q[5]=[],Q[6]=[];for(var B=0;B<=n.e.r;++B)V=[],V[1]=V[2]=[{type:0,data:ge(B+726270)}],Q[4].push({type:2,data:be(V)}),Q[5].push({type:0,data:ge(B)}),Q[6].push({type:0,data:ge(B)});U[Y].messages[0].data=be(Q)}y.content=it(at(U)),y.size=y.content.length,delete R[46];var I=Pe(R[4][0].data);{I[7][0].data=ge(n.e.r+1);var M=Pe(I[1][0].data),H=Pr(M[2][0].data);y=we.find(l,f[H].location),U=dr(vr(y.content));{if(U[0].id!=H)throw"Bad HeaderStorageBucket";var K=Pe(U[0].messages[0].data);for(B=0;B<i.length;++B){var ae=Pe(K[2][0].data);ae[1][0].data=ge(B),ae[4][0].data=ge(i[B].length),K[2][B]={type:K[2][0].type,data:be(ae)}}U[0].messages[0].data=be(K)}y.content=it(at(U)),y.size=y.content.length;var ne=Pr(I[2][0].data);y=we.find(l,f[ne].location),U=dr(vr(y.content));{if(U[0].id!=ne)throw"Bad HeaderStorageBucket";for(K=Pe(U[0].messages[0].data),O=0;O<=n.e.c;++O)ae=Pe(K[2][0].data),ae[1][0].data=ge(O),ae[4][0].data=ge(n.e.r+1),K[2][O]={type:K[2][0].type,data:be(ae)};U[0].messages[0].data=be(K)}y.content=it(at(U)),y.size=y.content.length;var he=Pr(I[4][0].data);(function(){for(var D=we.find(l,f[he].location),F=dr(vr(D.content)),A,X=0;X<F.length;++X){var le=F[X];le.id==he&&(A=le)}var oe=Pe(A.messages[0].data);{oe[3]=[];var se=[];s.forEach(function(xe,Ye){se[1]=[{type:0,data:ge(Ye)}],se[2]=[{type:0,data:ge(1)}],se[3]=[{type:2,data:Gu(xe)}],oe[3].push({type:2,data:be(se)})})}A.messages[0].data=be(oe);var ee=at(F),Se=it(ee);D.content=Se,D.size=D.content.length})();var fe=Pe(I[3][0].data);{var Fe=fe[1][0];delete fe[2];var pe=Pe(Fe.data);{var Ue=Pr(pe[2][0].data);(function(){for(var D=we.find(l,f[Ue].location),F=dr(vr(D.content)),A,X=0;X<F.length;++X){var le=F[X];le.id==Ue&&(A=le)}var oe=Pe(A.messages[0].data);{delete oe[6],delete fe[7];var se=new Uint8Array(oe[5][0].data);oe[5]=[];for(var ee=0,Se=0;Se<=n.e.r;++Se){var xe=Pe(se);ee+=Ku(xe,i[Se],s),xe[1][0].data=ge(Se),oe[5].push({data:be(xe),type:2})}oe[1]=[{type:0,data:ge(n.e.c+1)}],oe[2]=[{type:0,data:ge(n.e.r+1)}],oe[3]=[{type:0,data:ge(ee)}],oe[4]=[{type:0,data:ge(n.e.r+1)}]}A.messages[0].data=be(oe);var Ye=at(F),me=it(Ye);D.content=me,D.size=D.content.length})()}Fe.data=be(pe)}I[3][0].data=be(fe)}R[4][0].data=be(I)}d.messages[0].data=be(R);var je=at(x),S=it(je);return v.content=S,v.size=v.content.length,l}function Yu(e){return function(r){for(var n=0;n!=e.length;++n){var a=e[n];r[a[0]]===void 0&&(r[a[0]]=a[1]),a[2]==="n"&&(r[a[0]]=Number(r[a[0]]))}}}function s0(e){Yu([["cellDates",!1],["bookSST",!1],["bookType","xlsx"],["compression",!1],["WTF",!1]])(e)}function Ju(e,t){return t.bookType=="ods"?bi(e,t):t.bookType=="numbers"?ju(e,t):t.bookType=="xlsb"?Zu(e,t):qu(e,t)}function Zu(e,t){ft=1024,e&&!e.SSF&&(e.SSF=tr(Re)),e&&e.SSF&&(pn(),vn(e.SSF),t.revssf=mn(e.SSF),t.revssf[e.SSF[65535]]=0,t.ssf=e.SSF),t.rels={},t.wbrels={},t.Strings=[],t.Strings.Count=0,t.Strings.Unique=0,Ct?t.revStrings=new Map:(t.revStrings={},t.revStrings.foo=[],delete t.revStrings.foo);var r=t.bookType=="xlsb"?"bin":"xml",n=Ei.indexOf(t.bookType)>-1,a=Ja();s0(t=t||{});var i=Xn(),s="",f=0;if(t.cellXfs=[],Wr(t.cellXfs,{},{revssf:{General:0}}),e.Props||(e.Props={}),s="docProps/core.xml",ue(i,s,ei(e.Props,t)),a.coreprops.push(s),_e(t.rels,2,s,de.CORE_PROPS),s="docProps/app.xml",!(e.Props&&e.Props.SheetNames))if(!e.Workbook||!e.Workbook.Sheets)e.Props.SheetNames=e.SheetNames;else{for(var o=[],l=0;l<e.SheetNames.length;++l)(e.Workbook.Sheets[l]||{}).Hidden!=2&&o.push(e.SheetNames[l]);e.Props.SheetNames=o}for(e.Props.Worksheets=e.Props.SheetNames.length,ue(i,s,ti(e.Props)),a.extprops.push(s),_e(t.rels,3,s,de.EXT_PROPS),e.Custprops!==e.Props&&Xe(e.Custprops||{}).length>0&&(s="docProps/custom.xml",ue(i,s,ni(e.Custprops)),a.custprops.push(s),_e(t.rels,4,s,de.CUST_PROPS)),f=1;f<=e.SheetNames.length;++f){var c={"!id":{}},v=e.Sheets[e.SheetNames[f-1]],x=(v||{})["!type"]||"sheet";switch(x){case"chart":default:s="xl/worksheets/sheet"+f+"."+r,ue(i,s,eu(f-1,s,t,e,c)),a.sheets.push(s),_e(t.wbrels,-1,"worksheets/sheet"+f+"."+r,de.WS[0])}if(v){var d=v["!comments"],T=!1,u="";d&&d.length>0&&(u="xl/comments"+f+"."+r,ue(i,u,nu(d,u)),a.comments.push(u),_e(c,-1,"../comments"+f+"."+r,de.CMNT),T=!0),v["!legacy"]&&T&&ue(i,"xl/drawings/vmlDrawing"+f+".vml",_i(f,v["!comments"])),delete v["!comments"],delete v["!legacy"]}c["!id"].rId1&&ue(i,qa(s),ot(c))}return t.Strings!=null&&t.Strings.length>0&&(s="xl/sharedStrings."+r,ue(i,s,tu(t.Strings,s,t)),a.strs.push(s),_e(t.wbrels,-1,"sharedStrings."+r,de.SST)),s="xl/workbook."+r,ue(i,s,Qh(e,s)),a.workbooks.push(s),_e(t.rels,1,s,de.WB),s="xl/theme/theme1.xml",ue(i,s,mi(e.Themes,t)),a.themes.push(s),_e(t.wbrels,-1,"theme/theme1.xml",de.THEME),s="xl/styles."+r,ue(i,s,ru(e,s,t)),a.styles.push(s),_e(t.wbrels,-1,"styles."+r,de.STY),e.vbaraw&&n&&(s="xl/vbaProject.bin",ue(i,s,e.vbaraw),a.vba.push(s),_e(t.wbrels,-1,"vbaProject.bin",de.VBA)),s="xl/metadata."+r,ue(i,s,au(s)),a.metadata.push(s),_e(t.wbrels,-1,"metadata."+r,de.XLMETA),ue(i,"[Content_Types].xml",Za(a,t)),ue(i,"_rels/.rels",ot(t.rels)),ue(i,"xl/_rels/workbook."+r+".rels",ot(t.wbrels)),delete t.revssf,delete t.ssf,i}function qu(e,t){ft=1024,e&&!e.SSF&&(e.SSF=tr(Re)),e&&e.SSF&&(pn(),vn(e.SSF),t.revssf=mn(e.SSF),t.revssf[e.SSF[65535]]=0,t.ssf=e.SSF),t.rels={},t.wbrels={},t.Strings=[],t.Strings.Count=0,t.Strings.Unique=0,Ct?t.revStrings=new Map:(t.revStrings={},t.revStrings.foo=[],delete t.revStrings.foo);var r="xml",n=Ei.indexOf(t.bookType)>-1,a=Ja();s0(t=t||{});var i=Xn(),s="",f=0;if(t.cellXfs=[],Wr(t.cellXfs,{},{revssf:{General:0}}),e.Props||(e.Props={}),s="docProps/core.xml",ue(i,s,ei(e.Props,t)),a.coreprops.push(s),_e(t.rels,2,s,de.CORE_PROPS),s="docProps/app.xml",!(e.Props&&e.Props.SheetNames))if(!e.Workbook||!e.Workbook.Sheets)e.Props.SheetNames=e.SheetNames;else{for(var o=[],l=0;l<e.SheetNames.length;++l)(e.Workbook.Sheets[l]||{}).Hidden!=2&&o.push(e.SheetNames[l]);e.Props.SheetNames=o}e.Props.Worksheets=e.Props.SheetNames.length,ue(i,s,ti(e.Props)),a.extprops.push(s),_e(t.rels,3,s,de.EXT_PROPS),e.Custprops!==e.Props&&Xe(e.Custprops||{}).length>0&&(s="docProps/custom.xml",ue(i,s,ni(e.Custprops)),a.custprops.push(s),_e(t.rels,4,s,de.CUST_PROPS));var c=["SheetJ5"];for(t.tcid=0,f=1;f<=e.SheetNames.length;++f){var v={"!id":{}},x=e.Sheets[e.SheetNames[f-1]],d=(x||{})["!type"]||"sheet";switch(d){case"chart":default:s="xl/worksheets/sheet"+f+"."+r,ue(i,s,Ri(f-1,t,e,v)),a.sheets.push(s),_e(t.wbrels,-1,"worksheets/sheet"+f+"."+r,de.WS[0])}if(x){var T=x["!comments"],u=!1,g="";if(T&&T.length>0){var R=!1;T.forEach(function(C){C[1].forEach(function(y){y.T==!0&&(R=!0)})}),R&&(g="xl/threadedComments/threadedComment"+f+"."+r,ue(i,g,Do(T,c,t)),a.threadedcomments.push(g),_e(v,-1,"../threadedComments/threadedComment"+f+"."+r,de.TCMNT)),g="xl/comments"+f+"."+r,ue(i,g,Ti(T)),a.comments.push(g),_e(v,-1,"../comments"+f+"."+r,de.CMNT),u=!0}x["!legacy"]&&u&&ue(i,"xl/drawings/vmlDrawing"+f+".vml",_i(f,x["!comments"])),delete x["!comments"],delete x["!legacy"]}v["!id"].rId1&&ue(i,qa(s),ot(v))}return t.Strings!=null&&t.Strings.length>0&&(s="xl/sharedStrings."+r,ue(i,s,hi(t.Strings,t)),a.strs.push(s),_e(t.wbrels,-1,"sharedStrings."+r,de.SST)),s="xl/workbook."+r,ue(i,s,Ii(e)),a.workbooks.push(s),_e(t.rels,1,s,de.WB),s="xl/theme/theme1.xml",ue(i,s,mi(e.Themes,t)),a.themes.push(s),_e(t.wbrels,-1,"theme/theme1.xml",de.THEME),s="xl/styles."+r,ue(i,s,vi(e,t)),a.styles.push(s),_e(t.wbrels,-1,"styles."+r,de.STY),e.vbaraw&&n&&(s="xl/vbaProject.bin",ue(i,s,e.vbaraw),a.vba.push(s),_e(t.wbrels,-1,"vbaProject.bin",de.VBA)),s="xl/metadata."+r,ue(i,s,gi()),a.metadata.push(s),_e(t.wbrels,-1,"metadata."+r,de.XLMETA),c.length>1&&(s="xl/persons/person.xml",ue(i,s,Ro(c)),a.people.push(s),_e(t.wbrels,-1,"persons/person.xml",de.PEOPLE)),ue(i,"[Content_Types].xml",Za(a,t)),ue(i,"_rels/.rels",ot(t.rels)),ue(i,"xl/_rels/workbook."+r+".rels",ot(t.wbrels)),delete t.revssf,delete t.ssf,i}function Qu(e,t){var r="";switch((t||{}).type||"base64"){case"buffer":return[e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7]];case"base64":r=Nr(e.slice(0,12));break;case"binary":r=e;break;case"array":return[e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7]];default:throw new Error("Unrecognized type "+(t&&t.type||"undefined"))}return[r.charCodeAt(0),r.charCodeAt(1),r.charCodeAt(2),r.charCodeAt(3),r.charCodeAt(4),r.charCodeAt(5),r.charCodeAt(6),r.charCodeAt(7)]}function Wi(e,t){switch(t.type){case"base64":case"binary":break;case"buffer":case"array":t.type="";break;case"file":return Lt(t.file,we.write(e,{type:ve?"buffer":""}));case"string":throw new Error("'string' output type invalid for '"+t.bookType+"' files");default:throw new Error("Unrecognized type "+t.type)}return we.write(e,t)}function ex(e,t){var r=tr(t||{}),n=Ju(e,r);return rx(n,r)}function rx(e,t){var r={},n=ve?"nodebuffer":typeof Uint8Array<"u"?"array":"string";if(t.compression&&(r.compression="DEFLATE"),t.password)r.type=n;else switch(t.type){case"base64":r.type="base64";break;case"binary":r.type="string";break;case"string":throw new Error("'string' output type invalid for '"+t.bookType+"' files");case"buffer":case"file":r.type=n;break;default:throw new Error("Unrecognized type "+t.type)}var a=e.FullPaths?we.write(e,{fileType:"zip",type:{nodebuffer:"buffer",string:"binary"}[r.type]||r.type,compression:!!t.compression}):e.generate(r);if(typeof Deno<"u"&&typeof a=="string"){if(t.type=="binary"||t.type=="base64")return a;a=new Uint8Array(dn(a))}return t.password&&typeof encrypt_agile<"u"?Wi(encrypt_agile(a,t.password),t):t.type==="file"?Lt(t.file,a):t.type=="string"?St(a):a}function tx(e,t){var r=t||{},n=gu(e,r);return Wi(n,r)}function wr(e,t,r){r||(r="");var n=r+e;switch(t.type){case"base64":return Dt(Cr(n));case"binary":return Cr(n);case"string":return e;case"file":return Lt(t.file,n,"utf8");case"buffer":return ve?Ir(n,"utf8"):typeof TextEncoder<"u"?new TextEncoder().encode(n):wr(n,{type:"binary"}).split("").map(function(a){return a.charCodeAt(0)})}throw new Error("Unrecognized type "+t.type)}function nx(e,t){switch(t.type){case"base64":return Dt(e);case"binary":return e;case"string":return e;case"file":return Lt(t.file,e,"binary");case"buffer":return ve?Ir(e,"binary"):e.split("").map(function(r){return r.charCodeAt(0)})}throw new Error("Unrecognized type "+t.type)}function qt(e,t){switch(t.type){case"string":case"base64":case"binary":for(var r="",n=0;n<e.length;++n)r+=String.fromCharCode(e[n]);return t.type=="base64"?Dt(r):t.type=="string"?St(r):r;case"file":return Lt(t.file,e);case"buffer":return e;default:throw new Error("Unrecognized type "+t.type)}}function Hi(e,t){Os(),Vh(e);var r=tr(t||{});if(r.cellStyles&&(r.cellNF=!0,r.sheetStubs=!0),r.type=="array"){r.type="binary";var n=Hi(e,r);return r.type="array",dn(n)}var a=0;if(r.sheet&&(typeof r.sheet=="number"?a=r.sheet:a=e.SheetNames.indexOf(r.sheet),!e.SheetNames[a]))throw new Error("Sheet not found: "+r.sheet+" : "+typeof r.sheet);switch(r.bookType||"xlsb"){case"xml":case"xlml":return wr(pu(e,r),r);case"slk":case"sylk":return wr(Ul.from_sheet(e.Sheets[e.SheetNames[a]],r),r);case"htm":case"html":return wr(Bi(e.Sheets[e.SheetNames[a]],r),r);case"txt":return nx(Vi(e.Sheets[e.SheetNames[a]],r),r);case"csv":return wr(f0(e.Sheets[e.SheetNames[a]],r),r,"\uFEFF");case"dif":return wr(bl.from_sheet(e.Sheets[e.SheetNames[a]],r),r);case"dbf":return qt(Ml.from_sheet(e.Sheets[e.SheetNames[a]],r),r);case"prn":return wr(Wl.from_sheet(e.Sheets[e.SheetNames[a]],r),r);case"rtf":return wr(Kl.from_sheet(e.Sheets[e.SheetNames[a]],r),r);case"eth":return wr(ci.from_sheet(e.Sheets[e.SheetNames[a]],r),r);case"fods":return wr(bi(e,r),r);case"wk1":return qt(ea.sheet_to_wk1(e.Sheets[e.SheetNames[a]],r),r);case"wk3":return qt(ea.book_to_wk3(e,r),r);case"biff2":r.biff||(r.biff=2);case"biff3":r.biff||(r.biff=3);case"biff4":return r.biff||(r.biff=4),qt(Li(e,r),r);case"biff5":r.biff||(r.biff=5);case"biff8":case"xla":case"xls":return r.biff||(r.biff=8),tx(e,r);case"xlsx":case"xlsm":case"xlam":case"xlsb":case"numbers":case"ods":return ex(e,r);default:throw new Error("Unrecognized bookType |"+r.bookType+"|")}}function ax(e,t,r,n,a,i,s,f){var o=Ge(r),l=f.defval,c=f.raw||!Object.prototype.hasOwnProperty.call(f,"raw"),v=!0,x=a===1?[]:{};if(a!==1)if(Object.defineProperty)try{Object.defineProperty(x,"__rowNum__",{value:r,enumerable:!1})}catch{x.__rowNum__=r}else x.__rowNum__=r;if(!s||e[r])for(var d=t.s.c;d<=t.e.c;++d){var T=s?e[r][d]:e[n[d]+o];if(T===void 0||T.t===void 0){if(l===void 0)continue;i[d]!=null&&(x[i[d]]=l);continue}var u=T.v;switch(T.t){case"z":if(u==null)break;continue;case"e":u=u==0?null:void 0;break;case"s":case"d":case"b":case"n":break;default:throw new Error("unrecognized type "+T.t)}if(i[d]!=null){if(u==null)if(T.t=="e"&&u===null)x[i[d]]=null;else if(l!==void 0)x[i[d]]=l;else if(c&&u===null)x[i[d]]=null;else continue;else x[i[d]]=c&&(T.t!=="n"||T.t==="n"&&f.rawNumbers!==!1)?u:kr(T,u,f);u!=null&&(v=!1)}}return{row:x,isempty:v}}function xn(e,t){if(e==null||e["!ref"]==null)return[];var r={t:"n",v:0},n=0,a=1,i=[],s=0,f="",o={s:{r:0,c:0},e:{r:0,c:0}},l=t||{},c=l.range!=null?l.range:e["!ref"];switch(l.header===1?n=1:l.header==="A"?n=2:Array.isArray(l.header)?n=3:l.header==null&&(n=0),typeof c){case"string":o=Ae(c);break;case"number":o=Ae(e["!ref"]),o.s.r=c;break;default:o=c}n>0&&(a=0);var v=Ge(o.s.r),x=[],d=[],T=0,u=0,g=Array.isArray(e),R=o.s.r,C=0,y={};g&&!e[R]&&(e[R]=[]);var U=l.skipHidden&&e["!cols"]||[],Y=l.skipHidden&&e["!rows"]||[];for(C=o.s.c;C<=o.e.c;++C)if(!(U[C]||{}).hidden)switch(x[C]=ze(C),r=g?e[R][C]:e[x[C]+v],n){case 1:i[C]=C-o.s.c;break;case 2:i[C]=x[C];break;case 3:i[C]=l.header[C-o.s.c];break;default:if(r==null&&(r={w:"__EMPTY",t:"s"}),f=s=kr(r,null,l),u=y[s]||0,!u)y[s]=1;else{do f=s+"_"+u++;while(y[f]);y[s]=u,y[f]=1}i[C]=f}for(R=o.s.r+a;R<=o.e.r;++R)if(!(Y[R]||{}).hidden){var Q=ax(e,o,R,x,n,i,g,l);(Q.isempty===!1||(n===1?l.blankrows!==!1:!!l.blankrows))&&(d[T++]=Q.row)}return d.length=T,d}var la=/"/g;function ix(e,t,r,n,a,i,s,f){for(var o=!0,l=[],c="",v=Ge(r),x=t.s.c;x<=t.e.c;++x)if(!!n[x]){var d=f.dense?(e[r]||[])[x]:e[n[x]+v];if(d==null)c="";else if(d.v!=null){o=!1,c=""+(f.rawNumbers&&d.t=="n"?d.v:kr(d,null,f));for(var T=0,u=0;T!==c.length;++T)if((u=c.charCodeAt(T))===a||u===i||u===34||f.forceQuotes){c='"'+c.replace(la,'""')+'"';break}c=="ID"&&(c='"ID"')}else d.f!=null&&!d.F?(o=!1,c="="+d.f,c.indexOf(",")>=0&&(c='"'+c.replace(la,'""')+'"')):c="";l.push(c)}return f.blankrows===!1&&o?null:l.join(s)}function f0(e,t){var r=[],n=t==null?{}:t;if(e==null||e["!ref"]==null)return"";var a=Ae(e["!ref"]),i=n.FS!==void 0?n.FS:",",s=i.charCodeAt(0),f=n.RS!==void 0?n.RS:`
`,o=f.charCodeAt(0),l=new RegExp((i=="|"?"\\|":i)+"+$"),c="",v=[];n.dense=Array.isArray(e);for(var x=n.skipHidden&&e["!cols"]||[],d=n.skipHidden&&e["!rows"]||[],T=a.s.c;T<=a.e.c;++T)(x[T]||{}).hidden||(v[T]=ze(T));for(var u=0,g=a.s.r;g<=a.e.r;++g)(d[g]||{}).hidden||(c=ix(e,a,g,v,s,o,i,n),c!=null&&(n.strip&&(c=c.replace(l,"")),(c||n.blankrows!==!1)&&r.push((u++?f:"")+c)));return delete n.dense,r.join("")}function Vi(e,t){t||(t={}),t.FS="	",t.RS=`
`;var r=f0(e,t);return r}function sx(e){var t="",r,n="";if(e==null||e["!ref"]==null)return[];var a=Ae(e["!ref"]),i="",s=[],f,o=[],l=Array.isArray(e);for(f=a.s.c;f<=a.e.c;++f)s[f]=ze(f);for(var c=a.s.r;c<=a.e.r;++c)for(i=Ge(c),f=a.s.c;f<=a.e.c;++f)if(t=s[f]+i,r=l?(e[c]||[])[f]:e[t],n="",r!==void 0){if(r.F!=null){if(t=r.F,!r.f)continue;n=r.f,t.indexOf(":")==-1&&(t=t+":"+t)}if(r.f!=null)n=r.f;else{if(r.t=="z")continue;if(r.t=="n"&&r.v!=null)n=""+r.v;else if(r.t=="b")n=r.v?"TRUE":"FALSE";else if(r.w!==void 0)n="'"+r.w;else{if(r.v===void 0)continue;r.t=="s"?n="'"+r.v:n=""+r.v}}o[o.length]=t+"="+n}return o}function Gi(e,t,r){var n=r||{},a=+!n.skipHeader,i=e||{},s=0,f=0;if(i&&n.origin!=null)if(typeof n.origin=="number")s=n.origin;else{var o=typeof n.origin=="string"?Be(n.origin):n.origin;s=o.r,f=o.c}var l,c={s:{c:0,r:0},e:{c:f,r:s+t.length-1+a}};if(i["!ref"]){var v=Ae(i["!ref"]);c.e.c=Math.max(c.e.c,v.e.c),c.e.r=Math.max(c.e.r,v.e.r),s==-1&&(s=v.e.r+1,c.e.r=s+t.length-1+a)}else s==-1&&(s=0,c.e.r=t.length-1+a);var x=n.header||[],d=0;t.forEach(function(u,g){Xe(u).forEach(function(R){(d=x.indexOf(R))==-1&&(x[d=x.length]=R);var C=u[R],y="z",U="",Y=Ee({c:f+d,r:s+g+a});l=It(i,Y),C&&typeof C=="object"&&!(C instanceof Date)?i[Y]=C:(typeof C=="number"?y="n":typeof C=="boolean"?y="b":typeof C=="string"?y="s":C instanceof Date?(y="d",n.cellDates||(y="n",C=rr(C)),U=n.dateNF||Re[14]):C===null&&n.nullError&&(y="e",C=0),l?(l.t=y,l.v=C,delete l.w,delete l.R,U&&(l.z=U)):i[Y]=l={t:y,v:C},U&&(l.z=U))})}),c.e.c=Math.max(c.e.c,f+x.length-1);var T=Ge(s);if(a)for(d=0;d<x.length;++d)i[ze(d+f)+T]={t:"s",v:x[d]};return i["!ref"]=ke(c),i}function fx(e,t){return Gi(null,e,t)}function It(e,t,r){if(typeof t=="string"){if(Array.isArray(e)){var n=Be(t);return e[n.r]||(e[n.r]=[]),e[n.r][n.c]||(e[n.r][n.c]={t:"z"})}return e[t]||(e[t]={t:"z"})}return typeof t!="number"?It(e,Ee(t)):It(e,Ee({r:t,c:r||0}))}function lx(e,t){if(typeof t=="number"){if(t>=0&&e.SheetNames.length>t)return t;throw new Error("Cannot find sheet # "+t)}else if(typeof t=="string"){var r=e.SheetNames.indexOf(t);if(r>-1)return r;throw new Error("Cannot find sheet name |"+t+"|")}else throw new Error("Cannot find sheet |"+t+"|")}function ox(){return{SheetNames:[],Sheets:{}}}function cx(e,t,r,n){var a=1;if(!r)for(;a<=65535&&e.SheetNames.indexOf(r="Sheet"+a)!=-1;++a,r=void 0);if(!r||e.SheetNames.length>=65535)throw new Error("Too many worksheets");if(n&&e.SheetNames.indexOf(r)>=0){var i=r.match(/(^.*?)(\d+)$/);a=i&&+i[2]||0;var s=i&&i[1]||r;for(++a;a<=65535&&e.SheetNames.indexOf(r=s+a)!=-1;++a);}if(ki(r),e.SheetNames.indexOf(r)>=0)throw new Error("Worksheet with name |"+r+"| already exists!");return e.SheetNames.push(r),e.Sheets[r]=t,r}function hx(e,t,r){e.Workbook||(e.Workbook={}),e.Workbook.Sheets||(e.Workbook.Sheets=[]);var n=lx(e,t);switch(e.Workbook.Sheets[n]||(e.Workbook.Sheets[n]={}),r){case 0:case 1:case 2:break;default:throw new Error("Bad sheet visibility setting "+r)}e.Workbook.Sheets[n].Hidden=r}function ux(e,t){return e.z=t,e}function Xi(e,t,r){return t?(e.l={Target:t},r&&(e.l.Tooltip=r)):delete e.l,e}function xx(e,t,r){return Xi(e,"#"+t,r)}function dx(e,t,r){e.c||(e.c=[]),e.c.push({t,a:r||"SheetJS"})}function vx(e,t,r,n){for(var a=typeof t!="string"?t:Ae(t),i=typeof t=="string"?t:ke(t),s=a.s.r;s<=a.e.r;++s)for(var f=a.s.c;f<=a.e.c;++f){var o=It(e,s,f);o.t="n",o.F=i,delete o.v,s==a.s.r&&f==a.s.c&&(o.f=r,n&&(o.D=!0))}return e}var px={encode_col:ze,encode_row:Ge,encode_cell:Ee,encode_range:ke,decode_col:Jn,decode_row:Yn,split_cell:Rf,decode_cell:Be,decode_range:fr,format_cell:kr,sheet_add_aoa:Xa,sheet_add_json:Gi,sheet_add_dom:Mi,aoa_to_sheet:dt,json_to_sheet:fx,table_to_sheet:Ui,table_to_book:Wu,sheet_to_csv:f0,sheet_to_txt:Vi,sheet_to_json:xn,sheet_to_html:Bi,sheet_to_formulae:sx,sheet_to_row_object_array:xn,sheet_get_cell:It,book_new:ox,book_append_sheet:cx,book_set_sheet_visibility:hx,cell_set_number_format:ux,cell_set_hyperlink:Xi,cell_set_internal_link:xx,cell_add_comment:dx,sheet_set_array_formula:vx,consts:{SHEET_VISIBLE:0,SHEET_HIDDEN:1,SHEET_VERY_HIDDEN:2}};function mx(e,t){t=t||"sheet1";let r={SheetNames:[t],Sheets:{}};r.Sheets={[t]:e};let n={bookType:"xlsx",bookSST:!1,type:"binary"};const a=function(f){let o=new ArrayBuffer(f.length),l=new Uint8Array(o);for(let c=0;c!=f.length;++c)l[c]=f.charCodeAt(c)&255;return o},i=Hi(r,n);return new Blob([a(i)],{type:"application/octet-stream"})}function gx(e){let t=0;for(let r in e)e.hasOwnProperty(r)&&t++;return t}function oa(e){return new Promise(t=>{let r=[],n=[];for(const i in e)if(e.hasOwnProperty(i)){const s=e[i];let f=[];for(const o in s)if(s.hasOwnProperty(o)){const l=s[o];f.push(l),n.length<gx(s)&&n.push(o)}r.push(f)}r.splice(0,0,n);const a=px.aoa_to_sheet(r);t(mx(a))})}const _x={class:"trend-container"},Tx={class:"trend-right"},Ex={class:"search-form"},wx={class:"operate-btn"},Sx=fs({__name:"index",setup(e){const{t}=ls(),r=or("1"),n=or("1");g0(()=>n.value,I=>{I==="0"&&(a.value=!1)});const a=or(!1),i={startTime:Math.round(new Date().getTime()/1e3-20*60),endTime:Math.round(new Date().getTime()/1e3),timeInterval:15,timeUnit:1},s=os(ms(i)),f=or([i.startTime,i.endTime]),o=or([{value:1,name:"\u79D2"},{value:60,name:"\u5206"},{value:3600,name:"\u65F6"}]),l=or(0),c=async()=>{try{let I=await Ss();I.result&&I.result.resultCode==="0"&&(l.value=Math.round(I.data/1e3))}catch{}},v=async I=>{if(I&&I.length>0){I=I.map(Number),await c();let M=0,H=0;l.value<I[0]?(M=l.value,Xt.warning(t("views.monitor.trend.View.kaishishijianbixuxiaoyudangqianshijian\uFF01"))):M=I[0],l.value<I[1]?(H=l.value,Xt.warning(t("views.monitor.trend.View.jieshushijianbixuxiaoyudangqianshijian\uFF01"))):H=I[1],s.startTime=M,s.endTime=H,f.value=[M,H]}else s.startTime="",s.endTime=""},x=or(),d=or([]),T=I=>{var M;if(!(s.startTime&&s.endTime)){Xt.warning(t("views.monitor.trend.View.chaxunshijianbunengweikong\uFF01"));return}if(!s.timeInterval){Xt.warning("\u67E5\u8BE2\u95F4\u9694\u4E0D\u80FD\u4E3A\u7A7A\uFF01");return}(s.endTime-s.startTime)/(s.timeInterval*s.timeUnit)>1200&&ca({title:"\u6E29\u99A8\u63D0\u793A",message:"\u67E5\u8BE2\u6570\u636E\u91CF\u8FC7\u5927\uFF0C\u8BF7\u60A8\u8010\u5FC3\u7B49\u5F85\uFF08\u53EF\u66F4\u6539\u67E5\u8BE2\u65F6\u95F4\u6216\u67E5\u8BE2\u95F4\u9694\u51CF\u5C11\u6570\u636E\u91CF\u67E5\u8BE2\uFF09\uFF01",type:"info",duration:3e3}),g.value=[],C.value=[],x.value=I,d.value=((M=x.value)==null?void 0:M.point)||[],n.value="1",r.value==="1"?R():y()};ps("currentGroup",x);const u=()=>{let M={data:[]};return M.data=d.value.map(H=>({DeviceID:H.deviceId,State:H.code,start_time:s.startTime,end_time:s.endTime,interval:s.timeInterval*s.timeUnit,type:5})),M},g=or([]),R=async()=>{var H,K,ae;g.value=[];let{data:I}=await Es(u());if(!((K=(H=I[0])==null?void 0:H.data)!=null&&K.length))return;g.value=(ae=I[0].data)==null?void 0:ae.map((ne,he)=>({time:ne.time,attributes:d.value.map((fe,Fe)=>{var pe,Ue;return{...fe,value:(Ue=(pe=I[Fe].data[he])==null?void 0:pe.value)!=null?Ue:"-"}})}));let M=[{time:"\u6700\u5927\u503C",key:"max",isExtend:!0,attributes:[]},{time:"\u6700\u5C0F\u503C",key:"min",isExtend:!0,attributes:[]},{time:"\u5E73\u5747\u503C",key:"avg",isExtend:!0,attributes:[]}];M.forEach(ne=>{ne.attributes=d.value.map((he,fe)=>{var Fe;return{...he,value:(Fe=I[fe][ne.key]&&I[fe][ne.key][2])!=null?Fe:"-"}})}),g.value=[...g.value,...M],g.value},C=or(),y=async()=>{C.value=[];let I=await ws(u());!I.data||(C.value=d.value.map((M,H)=>{var ne;let K=I.data[H].data,ae=(ne=Array.from(K,({value:he})=>he))==null?void 0:ne.filter(he=>typeof he=="number");return{data:K,max:ae.length?Math.max(...ae):"-",min:ae.length?Math.min(...ae):"-",average:ae.length?ae.reduce((he,fe)=>he+fe)/ae.length:"-",...M}}),I.data)},U=async()=>{let I=[];d.value.forEach(ae=>{I.push(`${ae.deviceId}:${ae.code}`)});const M={state:I};let H=new Date().getTime(),{data:K}=await As(M);C.value.forEach((ae,ne)=>{var pe;if(K[ne].timestamp==0||((pe=K[ne].quality[ae.code])==null?void 0:pe.includes("invalid")))return;let he=ae.data.length,fe=K[ne].state[ae.code],Fe={false:0,true:1};typeof fe=="number"&&(fe>ae.max&&(ae.max=fe),fe<ae.min&&(ae.min=fe),ae.average=he?(ae.average*he+fe)/(he+1):fe),he&&he>=1200&&ae.data.shift(),ae.data.push({DeviceID:K[ne].id,State:ae.code,quality:[],time:H,value:["false","true"].indexOf(fe)>=0?Fe[fe]:fe})})},Y=()=>{let{trendGroupName:I,trendGroupID:M}=x.value;if(r.value==="1"){let H=g.value;if(H.length!=0){let K=[],ae={};H.forEach(he=>{ae={\u65F6\u95F4:he.isExtend?he.time:E0(he.time)},he.attributes.forEach(fe=>{ae[B(fe)]=fe.value}),K.push(ae)});let ne=I==="\u81EA\u5B9A\u4E49\u6570\u636E\u7EC4"?I:`${I}(${M})`;w0(oa,`${ne}-\u8D8B\u52BF\u8BE6\u60C5-${new Date().getTime()}`,K)}}if(r.value==="0"){let H=C.value;if(H.length!=0){let K={},ae={},ne="";H.forEach(fe=>{var Fe;ne=B(fe),ae={[t("views.monitor.trend.View.shebeimingcheng")]:fe.deviceName,[t("views.monitor.trend.View.shebeibianma")]:fe.deviceId,[t("views.monitor.trend.View.shuxingdianmingcheng")]:fe!=null&&fe.aliasCodeName?fe.aliasCodeName:fe.codeName,[t("views.monitor.trend.View.shuxingdianbianma")]:fe.code,[t("views.monitor.trend.View.zuidazhi")]:fe.max,[t("views.monitor.trend.View.zuixiaozhi")]:fe.min,[t("views.monitor.trend.View.pingjunzhi")]:fe.average},(Fe=fe.data)!=null&&Fe.length&&fe.data.forEach(pe=>{ae[E0(pe.time)]=pe.value}),K[ne]=ae});let he=I==="\u81EA\u5B9A\u4E49\u6570\u636E\u7EC4"?I:`${I}(${M})`;w0(oa,`${he}-\u8D8B\u52BF\u8BE6\u60C5-${new Date().getTime()}`,K)}}},Q=or(null),O=()=>{Q.value&&(clearInterval(Q.value),Q.value=null)},V=or(1);g0([()=>a.value,()=>V.value],async([I])=>{O(),I&&(await y(),Q.value=window.setInterval(async()=>{await U()},V.value*1e3))}),cs(()=>O());const B=I=>I.deviceName+":"+(I!=null&&I.aliasCodeName?I.aliasCodeName:I.codeName);return(I,M)=>{var S;const H=Tr("el-radio-button"),K=Tr("el-radio-group"),ae=Tr("el-date-picker"),ne=Tr("el-form-item"),he=Tr("el-input-number"),fe=Tr("el-option"),Fe=Tr("el-select"),pe=Tr("el-button"),Ue=Tr("el-switch"),je=Tr("el-form");return tt(),_0("div",_x,[Qe(gs,{class:"trend-left",onPointChange:T}),Cn("div",Tx,[Qe(K,{modelValue:r.value,"onUpdate:modelValue":M[0]||(M[0]=D=>r.value=D),class:"view-mode",onChange:M[1]||(M[1]=D=>T(x.value))},{default:xr(()=>[Qe(H,{label:"1"},{default:xr(()=>[Vt("\u8868\u683C")]),_:1}),Qe(H,{label:"0"},{default:xr(()=>[Vt("\u66F2\u7EBF")]),_:1})]),_:1},8,["modelValue"]),Cn("div",Ex,[Qe(je,{model:s,onSubmit:M[7]||(M[7]=ds(()=>{},["prevent"])),inline:!0},{default:xr(()=>[Qe(ne,{label:"\u67E5\u8BE2\u65F6\u95F4"},{default:xr(()=>[Qe(ae,{modelValue:f.value,"onUpdate:modelValue":M[2]||(M[2]=D=>f.value=D),type:"datetimerange","range-separator":"-","start-placeholder":"\u8BF7\u9009\u62E9\u8D77\u59CB\u65F6\u95F4","end-placeholder":"\u8BF7\u9009\u62E9\u7ED3\u675F\u65F6\u95F4","value-format":"X",onChange:v},null,8,["modelValue"])]),_:1}),Qe(ne,{label:"\u67E5\u8BE2\u95F4\u9694",class:"interval-item"},{default:xr(()=>[Qe(he,{modelValue:s.timeInterval,"onUpdate:modelValue":M[3]||(M[3]=D=>s.timeInterval=D),"controls-position":"right",min:"1",precision:"0"},null,8,["modelValue"]),Qe(Fe,{modelValue:s.timeUnit,"onUpdate:modelValue":M[4]||(M[4]=D=>s.timeUnit=D),placeholder:"\u8BF7\u9009\u62E9\u65F6\u95F4\u5355\u4F4D",class:"ml16"},{default:xr(()=>[(tt(!0),_0(hs,null,us(o.value,D=>(tt(),Gt(fe,{label:D.name,value:D.value,key:D.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),Qe(ne,null,{default:xr(()=>[Qe(pe,{type:"primary",icon:T0(xs),onClick:M[5]||(M[5]=D=>T(x.value))},{default:xr(()=>[Vt("\u67E5\u8BE2")]),_:1},8,["icon"])]),_:1}),r.value==="0"?(tt(),Gt(ne,{key:0,label:"\u5B9E\u65F6\u8D8B\u52BF"},{default:xr(()=>[Qe(Ue,{modelValue:a.value,"onUpdate:modelValue":M[6]||(M[6]=D=>a.value=D),disabled:n.value==="0"},null,8,["modelValue","disabled"])]),_:1})):On("",!0)]),_:1},8,["model"]),Cn("div",wx,[Qe(pe,{plain:"",icon:T0(vs),disabled:!((S=d.value)!=null&&S.length),onClick:Y},{default:xr(()=>[Vt("\u5BFC\u51FA")]),_:1},8,["icon","disabled"])])]),r.value==="1"?(tt(),Gt(_s,{key:0,trendData:g.value,pointList:d.value},null,8,["trendData","pointList"])):On("",!0),r.value==="0"?(tt(),Gt(Ts,{key:1,trendData:C.value,mode:n.value,"onUpdate:mode":M[8]||(M[8]=D=>n.value=D)},null,8,["trendData","mode"])):On("",!0)])])}}});const Hx=Fs(Sx,[["__scopeId","data-v-cbad8e3b"]]);export{Hx as default};
