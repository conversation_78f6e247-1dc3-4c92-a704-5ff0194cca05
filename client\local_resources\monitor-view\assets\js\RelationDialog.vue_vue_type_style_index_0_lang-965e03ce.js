import{d as k,aJ as C,r as s,A as I,a3 as R,h as p,o as V,B as A,w as i,b as r,i as m,t as g,a as P,j as q,aK as N,ak as S}from"./main-dd669ad8.js";import{g as f}from"./product-327919f5.js";import{a as E}from"./device-b598f94e.js";import{_ as $}from"./index.vue_vue_type_script_setup_true_name_ProTable_lang-ce2538eb.js";const H={class:"no-card table-box"},M=k({__name:"RelationDialog",emits:["editHandle"],setup(L,{expose:v,emit:_}){const{t}=C(),n=s(!1),h=s(t("views.edge.case.components.RelationDialog.guanlianshebei")),a=s();I();const u=s({classID:""}),d=s(-1);R(()=>{var e;return(e=a.value)==null?void 0:e.searchParam},e=>{e&&!e.classType&&(a.value.searchParam.classType="product",a.value.search())},{deep:!0});const b=async()=>{let e=[];try{let l=await f({classType:"product"});l&&(e=e.concat(l.data));let o=await f({classType:"point"});return o&&(e=e.concat(o.data)),{data:e}}catch{return{data:e}}},y=(e,l)=>{n.value=!0,u.value.classID=l,N(()=>{d.value=e,a.value.searchParam.classType="product",a.value.search()})},w=[{type:"selection",fixed:"left",width:50},{prop:"name",label:t("views.edge.case.components.RelationDialog.mingcheng"),align:"left",search:{el:"input",span:1,key:"deviceName"}},{prop:"id",label:t("views.edge.case.components.RelationDialog.bianma"),align:"left",search:{el:"input",span:1,key:"deviceId"}},{prop:"classType",isOnlySearch:!0,isShow:!1,label:"\u7C7B\u578B",align:"left",enum:[{value:"product",label:"\u666E\u901A\u8BBE\u5907"},{value:"point",label:"\u5355\u70B9\u8BBE\u5907"}],search:{el:"select",span:1,props:{clearable:!1}}},{prop:"classID",label:t("views.edge.case.components.RelationDialog.yuanxing"),align:"left",enum:b,fieldNames:{label:"name",value:"classID"}}],D=e=>{c(),e()},c=()=>{a.value.clearSelection(),n.value=!1},B=async()=>{const e=a.value.selectedListIds;if(e.length===0)return S.warning(t("views.edge.case.components.RelationDialog.qingxuanzeguanlianshebei"));_("editHandle",{index:d.value,data:e}),c()};return v({openDialog:y}),(e,l)=>{const o=p("el-button"),x=p("el-dialog");return V(),A(x,{modelValue:n.value,"onUpdate:modelValue":l[0]||(l[0]=T=>n.value=T),title:h.value,"before-close":D,"destroy-on-close":"",class:"relation-dialog","close-on-click-modal":!1,width:"840px",draggable:""},{footer:i(()=>[r(o,{onClick:c},{default:i(()=>[m(g(e.$t("common.button.quxiao")),1)]),_:1}),r(o,{type:"primary",onClick:B},{default:i(()=>[m(g(e.$t("common.button.queren")),1)]),_:1})]),default:i(()=>[P("div",H,[r($,{ref_key:"proTable",ref:a,"highlight-current-row":"",columns:w,border:!1,"select-id":"id",toolButton:!1,"init-param":u.value,requestApi:q(E)},null,8,["init-param","requestApi"])])]),_:1},8,["modelValue","title"])}}});export{M as _};
