import B from"./index-0bcda3f6.js";import T from"./View-e7038c36.js";import{d as f,a0 as b,u as E,r as n,G,x as u,a3 as R,h as _,o as a,c as m,b as r,j as S,a as V,B as p,w as d,a5 as L}from"./main-dd669ad8.js";import{s as q}from"./index-103b094d.js";import{_ as z}from"./_plugin-vue_export-helper-361d09b5.js";import"./mittBus-a5a7f363.js";import"./monitor-1739c0cb.js";const D={class:"template"},M={key:0,class:"iframe_box"},N=["src"],j=f({name:"Template"}),F=f({...j,setup(U){const c=b(),h=E(),v=n(window.__DEBUGGER_VIEW_),l=G(),y=u(()=>l.themeConfig),s=n(),i=n(),k=u(()=>c.meta.linkList);R(()=>c.meta.maximize,e=>{l.setThemeConfig({...y.value,maximize:e})},{immediate:!0});const x=e=>{if(s.value=e.type,e.type!="default"&&e.type!="custom"){let t={name:e.src};e.params.length>0&&(t.query={},e.params.forEach(o=>{t.query[o.key]=o.value})),h.replace(t)}i.value=q(e,v.value)};return(e,t)=>{const o=_("router-view"),w=_("el-main");return a(),m("div",D,[r(B,{list:S(k),onTabClick:x},null,8,["list"]),s.value==="default"||s.value==="custom"?(a(),m("div",M,[V("iframe",{frameborder:"0",src:i.value,style:{width:"100%",height:"100%"}},null,8,N)])):(a(),p(w,{key:1},{default:d(()=>[r(o,null,{default:d(({Component:C,route:g})=>[(a(),p(L(C),{key:g.path}))]),_:1})]),_:1})),r(T,{ref:"viewRef"},null,512)])}}});const Q=z(F,[["__scopeId","data-v-9fd581e7"]]);export{Q as default};
