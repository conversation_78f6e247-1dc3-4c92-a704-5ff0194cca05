import{S as r,U as t,V as n}from"./main-dd669ad8.js";const s=window.__DEBUGGER_VIEW_?"":n,u=e=>r.post(s+t+"/alarmcf/real/query",e,{headers:{noLoading:!0}}),c=e=>r.post(s+t+"/alarmcf/real/confirm",e),i=e=>r.post(s+t+"/alarmcf/real/ignore",e),p=e=>r.post(s+t+"/alarmcf/real/suppress",e),g=e=>r.post(s+t+"/alarmcf/real/unsuppress",e),d=()=>r.get(s+t+"/alarmcf/real/suppressed",{}),l=e=>r.post(s+t+"/alarmcf/his/query",e),m=e=>r.post(s+t+"/rtdb/log/query",e),f=e=>r.post(t+"/monitor/log/login/page",e),y=e=>r.post(s+t+"/trendGroup/query/list",e),L=e=>r.post(s+t+"/trendGroup/delete",e),T=e=>r.post(s+t+"/trendGroup/add",e),q=e=>r.post(s+t+"/trendGroup/update",e),h=()=>r.get(s+t+"/config/common/time"),P=e=>r.post(s+t+"/rtdb/query/state",e,{headers:{noLoading:!0}}),v=e=>r.post(s+t+"/trend/query",e),G=e=>r.post(s+t+"/trend/query/full/time",e),_=e=>{const o=e?{projectID:e}:{};return r.get(s+t+"/project/configure/query",o,{headers:{noLoading:!0}})},b=e=>r.getFileText(s+t+"/project/configure/alarm/voice/download",e,{headers:{noLoading:!0},responseType:"blob"}),A=e=>r.get(t+"/alarmcf/sound/statistic",e,{headers:{noLoading:!0}}),R=e=>r.get(t+"/svgservice/scenes/list/from-device",{deviceId:e});export{A as a,b,l as c,u as d,c as e,R as f,_ as g,d as h,i,m as j,f as k,G as l,v as m,h as n,P as o,y as p,T as q,q as r,p as s,L as t,g as u};
