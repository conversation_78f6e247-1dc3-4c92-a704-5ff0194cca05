import{c as C}from"./cloneDeep-174273b5.js";import{i as S,t as L}from"./index-2ff848b4.js";import{d as k,am as B,r as F,g as z,a7 as E,a3 as N,o as T,c as M,a as G,R}from"./main-dd669ad8.js";import{_ as A}from"./_plugin-vue_export-helper-361d09b5.js";const D=k({__name:"BarChart",props:{id:{default:"lineChart"},width:{default:"100%"},height:{default:"400px"},data:{default:[]}},setup(n){const u=n,a=B("currentGroup"),b="#10182E",r="#FFFFFF99",i="#2E4076",s=F();let t=null;const v=()=>{var m,f,x,w;let o=C(u.data),l=0,c=0,h={},_=o.map(e=>{typeof e.max=="number"&&l<e.max&&(l=e.max),typeof e.min=="number"&&c>e.min&&(c=e.min);let d=e.deviceName+":"+(e!=null&&e.aliasCodeName?e.aliasCodeName:e.codeName),g=e.data.map(y=>({name:d,value:[y.time,y.value]}));return h[d]=e.isShow,{name:d,type:"bar",barWidth:"10%",itemStyle:{color:e.color},data:g}});!s.value||(t=S(s.value),t.setOption({tooltip:{trigger:"axis"},legend:{show:!0,selected:h,selectedMode:!1,bottom:"bottom",itemGap:16,itemWidth:8,itemHeight:8,icon:"circle",textStyle:{fontSize:14,color:r},formatter:e=>L(e,90,"","\u2026",{}),tooltip:{show:!0,extraCssText:"max-width:300px;white-space: pre-wrap;word-break:break-all;padding: 8px;border-radius: 4px;background-color: #fff;color: #000;"}},grid:{top:"30",left:"0",right:"0",bottom:"30",containLabel:!0,show:!0,backgroundColor:b,borderColor:"transparent"},xAxis:{type:"time",boundaryGap:!0,axisTick:{show:!1},axisLabel:{color:r,rotate:45},splitLine:{show:!0,lineStyle:{type:"dashed",color:i}},axisLine:{show:!0,lineStyle:{color:i}}},yAxis:[{type:"value",min:(f=(m=a.value)==null?void 0:m.min)!=null?f:c,max:(w=(x=a.value)==null?void 0:x.max)!=null?w:l,position:"left",nameTextStyle:{color:r},axisLabel:{color:r},splitLine:{show:!0,lineStyle:{type:"dashed",color:i}},axisLine:{show:!0,lineStyle:{color:i}}}],series:_}))};z(()=>window.addEventListener("resize",p)),E(()=>{window.removeEventListener("resize",p),t==null||t.dispose(),t=null});const p=()=>t&&t.resize();return N([()=>u.data,()=>{var o;return(o=a.value)==null?void 0:o.min},()=>{var o;return(o=a.value)==null?void 0:o.max}],()=>setTimeout(()=>v(),0),{deep:!0,immediate:!0}),(o,l)=>(T(),M("div",{style:R({width:n.width,height:n.height})},[G("div",{ref_key:"chartRef",ref:s,class:"chart"},null,512)],4))}});const U=A(D,[["__scopeId","data-v-43e5ab10"]]);export{U as default};
