import{d as T,aJ as j,A as B,r as f,x as U,h as u,o as C,B as I,w as i,b as c,i as w,t as k,y as A,j as h,c as S,P as $,F as q,aA as K,aK as R}from"./main-dd669ad8.js";import{o as J,p as O,g as P}from"./control-95804351.js";import{_ as z}from"./_plugin-vue_export-helper-361d09b5.js";const G=T({__name:"EditCalendarDialog",props:{options:null},setup(y,{expose:x}){const V=y,{t:p}=j();B();const _=f(!1),g=f(),D=f("edit"),Y=U(()=>(D.value!="edit"?p("common.button.chakan"):p("common.button.bianji"))+p("views.control.timetable.components.EditCalendarDialog.rilifangan")),t=f({date:"",version:0,programList:[],programIDs:[],scheduleCalendarID:""}),E=async a=>{try{let e=await P();a.scheduleCalendarID=e.uuid}catch{}},m=f(),F=(a,e,v)=>{_.value=!0,g.value=a,R(()=>{m.value&&m.value.resetFields(),D.value=e,Object.assign(t.value,v),t.value.programIDs=v.programList.map(d=>d.programID)})},L=a=>{b(),a()},b=a=>{t.value={date:"",version:0,programList:[],programIDs:[],scheduleCalendarID:""},m.value&&m.value.resetFields(),a&&(_.value=!1)},M=a=>{m.value.validate(async(e,v)=>{if(e){let d=[];t.value.programIDs.forEach(l=>{V.options.forEach(o=>{l==o.programID&&d.push({programID:o.programID,programName:o.programName})})});let r={date:t.value.date,version:t.value.version,programList:d,scheduleCalendarID:t.value.scheduleCalendarID};if(r.scheduleCalendarID=="")try{await E(r);let l=await J(r);if(l.result&&l.result.resultCode==="0"){b(a);let o=new Date(r.date),n=o.getFullYear()+"-"+(o.getMonth()+1)+"-01";g.value&&g.value(n)}}catch{}else try{let l=await O(r);if(l.result&&l.result.resultCode==="0"){b(a);let o=new Date(r.date),n=o.getFullYear()+"-"+(o.getMonth()+1)+"-01";g.value&&g.value(n)}}catch{}}})};return x({openDialog:F}),(a,e)=>{const v=u("el-date-picker"),d=u("el-form-item"),r=u("el-option"),l=u("el-select"),o=u("el-form"),n=u("el-button"),N=u("el-dialog");return C(),I(N,{modelValue:_.value,"onUpdate:modelValue":e[3]||(e[3]=s=>_.value=s),title:h(Y),"before-close":L,"close-on-click-modal":!1,width:"480px",draggable:""},{footer:i(()=>[c(n,{onClick:b},{default:i(()=>[w(k(a.$t("common.button.quxiao")),1)]),_:1}),D.value==="edit"?(C(),I(n,{key:0,type:"primary",onClick:e[2]||(e[2]=s=>M(!0))},{default:i(()=>[w(k(a.$t("common.button.queren")),1)]),_:1})):A("",!0)]),default:i(()=>[c(o,{model:t.value,onSubmit:e[1]||(e[1]=K(()=>{},["prevent"])),ref_key:"formRef",ref:m,"label-width":"90px"},{default:i(()=>[c(d,{label:h(p)("views.control.timetable.components.EditCalendarDialog.shijian"),prop:"date"},{default:i(()=>[c(v,{readonly:"","model-value":t.value.date,style:{width:"calc(100% - 40px)"},format:"YYYY/MM/DD","value-format":"YYYY-MM-DD"},null,8,["model-value"])]),_:1},8,["label"]),c(d,{label:h(p)("views.control.timetable.components.EditCalendarDialog.fangan"),prop:"programList"},{default:i(()=>[c(l,{disabled:D.value!="edit",modelValue:t.value.programIDs,"onUpdate:modelValue":e[0]||(e[0]=s=>t.value.programIDs=s),clearable:"",filterable:"",multiple:"",class:"input"},{default:i(()=>[(C(!0),S(q,null,$(y.options,s=>(C(),I(r,{key:s.programID,label:s.programName,value:s.programID},null,8,["label","value"]))),128))]),_:1},8,["disabled","modelValue"])]),_:1},8,["label"])]),_:1},8,["model"])]),_:1},8,["modelValue","title"])}}});const X=z(G,[["__scopeId","data-v-c534e69a"]]);export{X as default};
