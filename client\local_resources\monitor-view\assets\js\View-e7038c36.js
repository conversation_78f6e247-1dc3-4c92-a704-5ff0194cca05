import{d as _,r as i,aK as u,N as p,O as v,o as d,c as f,a as n,p as m,e as h,Q as w}from"./main-dd669ad8.js";import{m as b}from"./mittBus-a5a7f363.js";import{_ as k}from"./_plugin-vue_export-helper-361d09b5.js";const x=o=>(m("data-v-27036428"),o=o(),h(),o),V={class:"view-box"},S=x(()=>n("i",{class:w("iconfont icon-tuichu")},null,-1)),y=[S],B=["src"],I=_({__name:"View",setup(o){const s=i(!1),t=i(""),e=i();b.on("openView",a=>{l(a.src)});const l=a=>{var c;e.value&&(e.value.src="about:blank",(c=e.value.contentWindow)==null||c.location.replace("about:blank"),u(()=>{s.value=!0,t.value=a}))},r=()=>{var a;s.value=!1,e.value&&(t.value="",e.value.src="about:blank",(a=e.value.contentWindow)==null||a.location.replace("about:blank"))};return(a,c)=>p((d(),f("div",V,[n("div",{class:"maximize",onClick:r},y),n("iframe",{ref_key:"iframeRef",ref:e,frameborder:"0",src:t.value,style:{width:"100%",height:"100%"}},null,8,B)],512)),[[v,s.value]])}});const N=k(I,[["__scopeId","data-v-27036428"]]);export{N as default};
