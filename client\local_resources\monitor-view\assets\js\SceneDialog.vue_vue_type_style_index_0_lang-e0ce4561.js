import{d as h,aJ as w,r as l,h as s,o as C,B as V,w as n,a as D,b as a,i as k,t as B,aK as F}from"./main-dd669ad8.js";import{f as S}from"./monitor-1739c0cb.js";import{m as I}from"./mittBus-a5a7f363.js";const N={class:"no-card table-box"},J=h({__name:"SceneDialog",emits:["editHandle"],setup(A,{expose:d,emit:T}){w();const t=l(!1),p=l("\u6240\u5C5E\u573A\u666F\u5217\u8868"),_=l(""),r=l([]),m=e=>{t.value=!0,F(()=>{_.value=e,g(e)})},f=e=>{u(),e&&e.Id&&I.emit("openView",{src:`/supaiot/api/monitor/proxy/supaiot/api/svgservice/scenes/visit/${e.Id}`})},g=async e=>{try{const o=await S(e);o&&(r.value=o.data)}catch(o){console.error("Error fetching scenes:",o)}},b=e=>{u(),e()},u=()=>{t.value=!1};return d({openDialog:m}),(e,o)=>{const i=s("el-table-column"),v=s("el-button"),y=s("el-table"),x=s("el-dialog");return C(),V(x,{modelValue:t.value,"onUpdate:modelValue":o[0]||(o[0]=c=>t.value=c),title:p.value,"before-close":b,"destroy-on-close":"",class:"scene-dialog","close-on-click-modal":!1,width:"840px",draggable:""},{default:n(()=>[D("div",N,[a(y,{data:r.value,style:{width:"100%",padding:"8px 0"}},{default:n(()=>[a(i,{prop:"categoryName",label:"\u573A\u666F\u5206\u7C7B"}),a(i,{prop:"name",label:"\u573A\u666F\u540D\u79F0"}),a(i,{fixed:"right",label:e.$t("common.table.caozuo"),width:"80"},{default:n(c=>[a(v,{link:"",type:"primary",size:"small",onClick:$=>f(c.row)},{default:n(()=>[k(B("\u6253\u5F00"))]),_:2},1032,["onClick"])]),_:1},8,["label"])]),_:1},8,["data"])])]),_:1},8,["modelValue","title"])}}});export{J as _};
