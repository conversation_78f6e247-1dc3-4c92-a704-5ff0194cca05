import{d as P,G as z,x as c,r as x,a3 as I,as as F,g as G,a0 as H,h as C,o as d,c as _,j as m,y as L,a as y,b as U,w as g,F as k,P as j,Q as D,B as E,i as Q,t as R}from"./main-dd669ad8.js";import{_ as q}from"./_plugin-vue_export-helper-361d09b5.js";const A={class:"tabs-menu"},J=P({__name:"index",props:{list:{default:[]}},emits:["tabClick"],setup(w,{emit:M}){var T;const b=w,V=z(),o=c(()=>V.themeConfig.maximize),p=x(),l=x(!1);let n=null;const $="ontouchstart"in window||navigator.maxTouchPoints>0||((T=window.matchMedia)==null?void 0:T.call(window,"(pointer: coarse)").matches),B=c(()=>b.list.length<=1||!$&&o.value&&!l.value),N=c(()=>o.value&&!l.value),v=()=>{!o.value||(n&&(clearTimeout(n),n=null),l.value=!0)},f=()=>{!o.value||(n=setTimeout(()=>{l.value=!1},500))};I(o,t=>{t?setTimeout(()=>{l.value=!1},500):l.value=!0}),F(()=>{n&&clearTimeout(n)});const a=c(()=>b.list);G(()=>{S()});const S=()=>{if(a.value.length>0){const t=a.value[0];let s=`${t.type}_0_${t.alias}`;const u=H().name;let e=a.value.findIndex(r=>r.src===u);e>-1&&(s=`${a.value[e].type}_${e}_${a.value[e].alias}`),p.value=s,h({props:{name:s}})}},h=t=>{var e;const i=t.props.name.split("_"),u=(e=a.value[Number(i[1])])!=null?e:{};M("tabClick",u)};return(t,s)=>{const i=C("el-tab-pane"),u=C("el-tabs");return d(),_(k,null,[m(N)?(d(),_("div",{key:0,class:"tabs-auto-hide-container",onMouseenter:v,onMouseleave:f},null,32)):L("",!0),y("div",{class:D(["temp-tabs-box",{"hide-tabs":m(B),fixed:m(o)}]),onMouseenter:v,onMouseleave:f},[y("div",A,[U(u,{modelValue:p.value,"onUpdate:modelValue":s[0]||(s[0]=e=>p.value=e),type:"border-card",onTabClick:h},{default:g(()=>[(d(!0),_(k,null,j(m(a),(e,r)=>(d(),E(i,{key:e.alias+r,label:e.alias,name:`${e.type}_${r}_${e.alias}`},{label:g(()=>[Q(R(e.alias),1)]),_:2},1032,["label","name"]))),128))]),_:1},8,["modelValue"])])],34)],64)}}});const W=q(J,[["__scopeId","data-v-c902a2a3"]]);export{W as default};
