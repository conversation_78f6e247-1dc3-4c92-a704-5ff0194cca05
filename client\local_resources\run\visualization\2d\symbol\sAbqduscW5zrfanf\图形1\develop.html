<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>图元调试</title>
    <link rel="icon" href="/supaiot/static/favicon.ico">
    <link rel="stylesheet" href="/supaiot/static/css/index.css">
    <link rel="stylesheet" href="/supaiot/static/css/symbolanimation.css">
    <link href="/supaiot/static/css/element.css" rel="stylesheet">
    <script src="/supaiot/static/js/jquery.min.js"></script>
    <script src="/supaiot/static/js/react.min.js"></script>
    <script src="/supaiot/static/js/react-dom.min.js"></script>
    <script src="/supaiot/static/js/JSXTransformer.js"></script>
</head>
<body>
<div id="svg_file" style="visibility: visible;width: 1px;height: 1px;overflow: hidden;"><svg id="20405785c0346478bbe26712d22f5c2dsymbolshow_root" xmlns="http://www.w3.org/2000/svg" xmlns:svg="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" style="overflow:visible;visibility:visible;" width="111.21151733398438" height="112.71150207519531" ecityosbackgroundcolor="#FFF" ecityosname="图形1" viewBox="0 0 111.21151733398438 112.71150207519531"><defs></defs><g id="20405785c0346478bbe26712d22f5c2dsymbol_sup" ecityosname="图形1"><title>图形1</title><g id="20405785c0346478bbe26712d22f5c2d__symbol_sup__graph4" ecityosgroup="yes" customid="20405785c0346478bbe26712d22f5c2d__symbol_sup__graph4" transform="translate(-276.3943 -105.8942)" xmlns="http://www.w3.org/2000/svg"><desc>状态graph4</desc><circle fill="#FF0000" stroke="#000000" stroke-width="5" cx="332" cy="163" r="55.60575485229492" id="20405785c0346478bbe26712d22f5c2d__symbol_sup__graph1" customid="20405785c0346478bbe26712d22f5c2d__symbol_sup__graph1" fill-opacity="1" stroke-opacity="1"></circle></g><g id="20405785c0346478bbe26712d22f5c2d__symbol_sup__graph5" ecityosgroup="yes" customid="20405785c0346478bbe26712d22f5c2d__symbol_sup__graph5" fill-opacity="1" transform="translate(-276.3943 -105.8942)" xmlns="http://www.w3.org/2000/svg"><desc>状态graph5</desc><circle fill="#00ff7f" stroke="#000000" stroke-width="5" cx="332" cy="161.50000381469727" r="55.60575485229492" id="20405785c0346478bbe26712d22f5c2d__symbol_sup__graph3" customid="20405785c0346478bbe26712d22f5c2d__symbol_sup__graph3" fill-opacity="1" stroke-opacity="1"></circle></g></g></svg></div>
<div id="devicePreview">
    <div class="table_wrap">
        <div class="table_title">
            <div class="name_title head">编码</div>
            <div class="desc_title head">名称</div>
            <div class="input_title head">点值</div>
        </div>
        <div id="pointList">
            <div class="table_con">
                
                <div class="table_list">
                    <div class="name_con con">_online</div>
                    <div class="desc_con con">在线状态</div>
                    
                    <select id="table0" class="input_con con">
                        <option value="false" selected="selected">false</option>
                        <option value="true">true</option>
                    </select>
                    
                </div>
                
                <div class="table_list">
                    <div class="name_con con">_scanoff</div>
                    <div class="desc_con con">脱离扫描</div>
                    
                    <select id="table1" class="input_con con">
                        <option value="false" selected="selected">false</option>
                        <option value="true">true</option>
                    </select>
                    
                </div>
                
                <div class="table_list">
                    <div class="name_con con">_inhibit</div>
                    <div class="desc_con con">控制抑制</div>
                    
                    <select id="table2" class="input_con con">
                        <option value="false" selected="selected">false</option>
                        <option value="true">true</option>
                    </select>
                    
                </div>
                
                <div class="table_list">
                    <div class="name_con con">AI1</div>
                    <div class="desc_con con">AI1</div>
                    
                    <input id="table3" type="text" class="input_con con" value="0">
                    
                </div>
                
                <div class="table_list">
                    <div class="name_con con">AI2</div>
                    <div class="desc_con con">AI2</div>
                    
                    <input id="table4" type="text" class="input_con con" value="0">
                    
                </div>
                
                <div class="table_list">
                    <div class="name_con con">DI1</div>
                    <div class="desc_con con">DI1</div>
                    
                    <select id="table5" class="input_con con">
                        <option value="false" selected="selected">false</option>
                        <option value="true">true</option>
                    </select>
                    
                </div>
                
                <div class="table_list">
                    <div class="name_con con">DI2</div>
                    <div class="desc_con con">DI2</div>
                    
                    <select id="table6" class="input_con con">
                        <option value="false" selected="selected">false</option>
                        <option value="true">true</option>
                    </select>
                    
                </div>
                
                <div class="table_list">
                    <div class="name_con con">CAL1</div>
                    <div class="desc_con con">CAL1</div>
                    
                    <select id="table7" class="input_con con">
                        <option value="false" selected="selected">false</option>
                        <option value="true">true</option>
                    </select>
                    
                </div>
                
            </div>
        </div>

        
        <div class="table_title">
            <div class="name_title head">编码</div>
            <div class="desc_title head">名称</div>
            <div class="input_title head">更新时间</div>
        </div>
        <div id="pointList">
            <div class="table_con">
                
                <div class="table_list">
                    <div class="name_con con">_online</div>
                    <div class="desc_con con">在线状态</div>
                    <input id="updatetime_table0" type="text" class="input_con con" value="0">
                </div>
                
                <div class="table_list">
                    <div class="name_con con">_scanoff</div>
                    <div class="desc_con con">脱离扫描</div>
                    <input id="updatetime_table1" type="text" class="input_con con" value="0">
                </div>
                
                <div class="table_list">
                    <div class="name_con con">_inhibit</div>
                    <div class="desc_con con">控制抑制</div>
                    <input id="updatetime_table2" type="text" class="input_con con" value="0">
                </div>
                
                <div class="table_list">
                    <div class="name_con con">AI1</div>
                    <div class="desc_con con">AI1</div>
                    <input id="updatetime_table3" type="text" class="input_con con" value="0">
                </div>
                
                <div class="table_list">
                    <div class="name_con con">AI2</div>
                    <div class="desc_con con">AI2</div>
                    <input id="updatetime_table4" type="text" class="input_con con" value="0">
                </div>
                
                <div class="table_list">
                    <div class="name_con con">DI1</div>
                    <div class="desc_con con">DI1</div>
                    <input id="updatetime_table5" type="text" class="input_con con" value="0">
                </div>
                
                <div class="table_list">
                    <div class="name_con con">DI2</div>
                    <div class="desc_con con">DI2</div>
                    <input id="updatetime_table6" type="text" class="input_con con" value="0">
                </div>
                
                <div class="table_list">
                    <div class="name_con con">CAL1</div>
                    <div class="desc_con con">CAL1</div>
                    <input id="updatetime_table7" type="text" class="input_con con" value="0">
                </div>
                
            </div>
        </div>

        <div class="table_title">
            <div class="name_title head">编码</div>
            <div class="desc_title head">名称</div>
            <div class="input_title head">质量码</div>
        </div>
        <div id="pointList">
            <div class="table_con">
                
                <div class="table_list">
                    <div class="name_con con">_online</div>
                    <div class="desc_con con">在线状态</div>
                    <input id="quality_table0" type="text" class="input_con con" value="">
                </div>
                
                <div class="table_list">
                    <div class="name_con con">_scanoff</div>
                    <div class="desc_con con">脱离扫描</div>
                    <input id="quality_table1" type="text" class="input_con con" value="">
                </div>
                
                <div class="table_list">
                    <div class="name_con con">_inhibit</div>
                    <div class="desc_con con">控制抑制</div>
                    <input id="quality_table2" type="text" class="input_con con" value="">
                </div>
                
                <div class="table_list">
                    <div class="name_con con">AI1</div>
                    <div class="desc_con con">AI1</div>
                    <input id="quality_table3" type="text" class="input_con con" value="">
                </div>
                
                <div class="table_list">
                    <div class="name_con con">AI2</div>
                    <div class="desc_con con">AI2</div>
                    <input id="quality_table4" type="text" class="input_con con" value="">
                </div>
                
                <div class="table_list">
                    <div class="name_con con">DI1</div>
                    <div class="desc_con con">DI1</div>
                    <input id="quality_table5" type="text" class="input_con con" value="">
                </div>
                
                <div class="table_list">
                    <div class="name_con con">DI2</div>
                    <div class="desc_con con">DI2</div>
                    <input id="quality_table6" type="text" class="input_con con" value="">
                </div>
                
                <div class="table_list">
                    <div class="name_con con">CAL1</div>
                    <div class="desc_con con">CAL1</div>
                    <input id="quality_table7" type="text" class="input_con con" value="">
                </div>
                
            </div>
        </div>

        <div class="table_title">
            <div class="name_title head">序号</div>
            <div class="desc_title head">名称</div>
            <div class="input_title head">CONFIG</div>
        </div>
        <div id="pointList">
            <div class="table_con">
                <div class="table_list">
                    <div class="name_con con">1</div>
                    <div class="desc_con con">CONFIG</div>
                    <input id="config_table1" type="text" class="input_con con" value="">
                </div>
            </div>
        </div>
    </div>

    <div class="pixel_wrap">
        <div class="pixel_title">
            <div class="pel_title head">图元</div>
        </div>
        <div class="pixel_con">
            <div class="pel_con">
                <div>
                    <svg id="svgDevice" xmlns="http://www.w3.org/2000/svg"
                         version="1.1">
                        <g id="TempDevice"></g>
                    </svg>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    let svgroot = $("#svg_file svg")
    let svgStyle = '';
    let backgroundattr = svgroot.attr('ecityosbackgroundcolor');
    let styleattr = svgroot.attr('style');
    let widthattr = svgroot.attr('width');
    let heightattr = svgroot.attr('height');
    if (styleattr) {
        svgStyle += styleattr;
    }
    if (backgroundattr) {
        svgStyle += "background:" + backgroundattr + ";";
    }
    if (widthattr) {
        svgStyle += "width:" + parseFloat(widthattr) + "px;";
    }
    if (heightattr) {
        svgStyle += "height:" + parseFloat(heightattr) + "px;";
    }
    svgStyle += "outline:none;";
    $("#svgDevice").attr("style",svgStyle)
    
</script>
<script type="text/jsx">
    var ReactObjectManager = [];
    function attchReactObject(device) {
        ReactObjectManager.push(device);
    }
    function dispatchReactObject(device) {
        ReactObjectManager.pop();
    }
    function startInteval() {
        for (let i = 0; i < ReactObjectManager.length; i++) {
            let config = document.getElementById("config_table1").value;
            if(config == "" || config == undefined)
            config = "{}";
			let attributes = [{"code":"_online","description":"设备的在线状态","history":{"change":false},"name":"在线状态","pointType":"internalState","type":"DI","value":{"OFF":"离线","ON":"在线"}},{"authorityLevel":999,"code":"_scanoff","description":"脱离扫描","history":{"change":false},"name":"脱离扫描","pointType":"internalStateControl","reference":["_scanoff"],"type":"DIO","value":{"OFF":"取消脱离扫描","ON":"设置脱离扫描"},"verification":[{"check":{"condition":[{"code":"_scanoff","description":"脱离扫描状态满足","operation":"==","value":"ON"}],"script":"","timeout":10,"type":"normal"},"controllable":{"condition":[{"code":"_scanoff","description":"脱离扫描状态不满足","operation":"==","value":"OFF"}],"script":"","type":"normal"},"value":"ON"},{"check":{"condition":[{"code":"_scanoff","description":"脱离扫描状态不满足","operation":"==","value":"OFF"}],"script":"","timeout":10,"type":"normal"},"controllable":{"condition":[{"code":"_scanoff","description":"脱离扫描状态不满足","operation":"==","value":"ON"}],"script":"","type":"normal"},"value":"OFF"}]},{"authorityLevel":999,"code":"_inhibit","description":"控制抑制","history":{"change":false},"name":"控制抑制","pointType":"internalStateControl","reference":["_inhibit"],"type":"DIO","value":{"OFF":"取消控制抑制","ON":"设置控制抑制"},"verification":[{"check":{"condition":[{"code":"_inhibit","description":"控制抑制状态满足","operation":"==","value":"ON"}],"script":"","timeout":10,"type":"normal"},"controllable":{"condition":[{"code":"_inhibit","description":"控制抑制状态不满足","operation":"==","value":"OFF"}],"type":"normal"},"script":"","value":"ON"},{"check":{"condition":[{"code":"_inhibit","description":"控制抑制状态满足","operation":"==","value":"OFF"}],"script":"","timeout":10,"type":"normal"},"controllable":{"condition":[{"code":"_inhibit","description":"控制抑制状态不满足","operation":"==","value":"ON"}],"script":"","type":"normal"},"value":"OFF"}]},{"alarm":[{"autoConfirm":false,"autoConfirmDelay":7,"checked":false,"deadZone":0,"delayTime":0,"level":"1","message":"","pushPicture":false,"type":"AI_H","value":""},{"autoConfirm":false,"autoConfirmDelay":7,"checked":false,"deadZone":0,"delayTime":0,"level":"1","message":"","pushPicture":false,"type":"AI_HH","value":""},{"autoConfirm":false,"autoConfirmDelay":7,"checked":false,"deadZone":0,"delayTime":0,"level":"1","message":"","pushPicture":false,"type":"AI_L","value":""},{"autoConfirm":false,"autoConfirmDelay":7,"checked":false,"deadZone":0,"delayTime":0,"level":"1","message":"","pushPicture":false,"type":"AI_LL","value":""},{"autoConfirm":false,"autoConfirmDelay":7,"checked":false,"cycle":1,"level":"1","message":"","pushPicture":false,"rate":"0","type":"AI_CR"},{"autoConfirm":false,"autoConfirmDelay":7,"checked":false,"deadZone":0,"delayTime":0,"deviation":"","level":"1","message":"","pushPicture":false,"setPoint":"","type":"AI_DA"}],"alarmGroup":0,"code":"AI1","description":"","name":"AI1","pointType":"state","type":"AI"},{"alarm":[{"autoConfirm":false,"autoConfirmDelay":7,"checked":false,"deadZone":0,"delayTime":0,"level":"1","message":"","pushPicture":false,"type":"AI_H","value":""},{"autoConfirm":false,"autoConfirmDelay":7,"checked":false,"deadZone":0,"delayTime":0,"level":"1","message":"","pushPicture":false,"type":"AI_HH","value":""},{"autoConfirm":false,"autoConfirmDelay":7,"checked":false,"deadZone":0,"delayTime":0,"level":"1","message":"","pushPicture":false,"type":"AI_L","value":""},{"autoConfirm":false,"autoConfirmDelay":7,"checked":false,"deadZone":0,"delayTime":0,"level":"1","message":"","pushPicture":false,"type":"AI_LL","value":""},{"autoConfirm":false,"autoConfirmDelay":7,"checked":false,"cycle":1,"level":"1","message":"","pushPicture":false,"rate":"0","type":"AI_CR"},{"autoConfirm":false,"autoConfirmDelay":7,"checked":false,"deadZone":0,"delayTime":0,"deviation":"","level":"1","message":"","pushPicture":false,"setPoint":"","type":"AI_DA"}],"alarmGroup":0,"code":"AI2","description":"","name":"AI2","pointType":"state","type":"AI"},{"alarm":[{"autoConfirm":false,"autoConfirmDelay":7,"checked":false,"level":"1","message":"","pushPicture":false,"type":"DI_ST","value":"OFF"},{"autoConfirm":false,"autoConfirmDelay":7,"checked":false,"level":"1","message":"","pushPicture":false,"type":"DI_OFF2ON"},{"autoConfirm":false,"autoConfirmDelay":7,"checked":false,"level":"1","message":"","pushPicture":false,"type":"DI_ON2OFF"}],"alarmGroup":0,"code":"DI1","description":"","name":"DI1","pointType":"state","type":"DI","value":{"OFF":"关状态","ON":"开状态"}},{"alarm":[{"autoConfirm":false,"autoConfirmDelay":7,"checked":false,"level":"1","message":"","pushPicture":false,"type":"DI_ST","value":"OFF"},{"autoConfirm":false,"autoConfirmDelay":7,"checked":false,"level":"1","message":"","pushPicture":false,"type":"DI_OFF2ON"},{"autoConfirm":false,"autoConfirmDelay":7,"checked":false,"level":"1","message":"","pushPicture":false,"type":"DI_ON2OFF"}],"alarmGroup":0,"code":"DI2","description":"","name":"DI2","pointType":"state","type":"DI","value":{"OFF":"关状态","ON":"开状态"}},{"alarm":[{"autoConfirm":false,"autoConfirmDelay":7,"checked":false,"level":"1","message":"","pushPicture":false,"type":"DI_ST","value":"OFF"},{"autoConfirm":false,"autoConfirmDelay":7,"checked":false,"level":"1","message":"","pushPicture":false,"type":"DI_OFF2ON"},{"autoConfirm":false,"autoConfirmDelay":7,"checked":false,"level":"1","message":"","pushPicture":false,"type":"DI_ON2OFF"}],"alarmGroup":0,"calculate":{"expression":"","mappingDevice":"","mappingPoint":"","script":"","type":"normal"},"code":"CAL1","description":"","devicePanelHidden":false,"history":{"change":false,"stateChange":false},"mark":{},"markIndex":[],"name":"CAL1","pointType":"calculation","type":"DI","value":{"OFF":"关状态","ON":"开状态"}}]
			let reactStateObj = {state:{},updatetime:{},quality:{},config:JSON.parse(config)};
			for (let i = 0; i < attributes.length; i++) {
				if (attributes[i].type == 'AI' || attributes[i].type == 'AIO' || attributes[i].type == 'EI' || attributes[i].type == 'EIO') {
					reactStateObj.state[attributes[i].code] = Number(document.getElementById("table"+i).value);
				} if (attributes[i].type == 'SI' || attributes[i].type == 'SIO') {
					reactStateObj.state[attributes[i].code] = document.getElementById("table"+i).value;
				} else if (attributes[i].type == 'DI' || attributes[i].type == 'DIO') {
					reactStateObj.state[attributes[i].code] = document.getElementById("table"+i).value==="false"?false:true;
				}
				reactStateObj.updatetime[attributes[i].code] = Number(document.getElementById("updatetime_table"+i).value);
				reactStateObj.quality[attributes[i].code] = document.getElementById("quality_table"+i).value;
			}
            ReactObjectManager[i].setState(reactStateObj)
        }
    }
    </script>
<script type="text/jsx">
class _visualization_2d_symbol_sAbqduscW5zrfanf_图形1 extends React.Component{
        constructor(props){
            super(props);
            this.state = {
                state:{},
                quality:{},
                updatetime:{},
                config:{
                    information:{}
                },
                userChanged:false,
                
            };
        }
        componentDidMount(){
            attchReactObject(this);
            this.loadStyles();
        }
        componentWillUnmount(){
            dispatchReactObject(this);
        }
        componentDidUpdate(prevProps, prevState) {
            if(!this.state.userChanged){
                let newState = {};
                if(Object.keys(this.state.state).length>0){
                    newState.userChanged = true;
                }
                
                Object.keys(newState).length && this.setState(newState)
            }
        }
        loadStyles() {
            const styleString = ``;

            if(styleString == ""){ return; }
            const styleElement = document.createElement('style');
            if (styleElement.styleSheet) {
               styleElement.styleSheet.cssText = styleString;
            } else {
               styleElement.appendChild(document.createTextNode(styleString));
            }
            document.head.appendChild(styleElement);
        }
        condition( state,  updatetime, quality, config, flaglist ) {
            try{if(state["DI1"] == false){flaglist[0] = true;}
else if(state["DI1"] == true){flaglist[1] = true;}
else{}} catch(e){ console.error(e); }

        }
        handleChange(stateKey,event) {
            this.setState({
                [stateKey]: event.target.value,
            })
        }
        buttonFunc(stateKey,control,input_data,timeOut) {
            let id = this.props.deviceid;
            if(!id){return}
            let json = {};
            json.id = id;
            json.control = control;
            json.value = input_data;

            console.log("json  ",json);
            this.setState({
                [stateKey + "Disabled"]:true
            });
            if(timeOut){
                setTimeout(()=>{
                   this.setState({
                       [stateKey + "Disabled"]:false
                   });
                },timeOut)
            }
            subfetch(json,"",()=> {
                this.setState({
                    [stateKey + "Disabled"]: false, // 解除 select 禁用
                });
            });
        }
        buttonClick(stateKey,control,input_data,config,event){
            if(control == ""){
                return;
            }
            let confirm = config.confirm,
                msg = config.confirmMsg,
                timeout = config.timeout;
            if(confirm){
                Elconfirm(msg,"提示").then(()=>{
                    this.buttonFunc(stateKey,control,input_data,timeout)
                }).catch(()=>{console.log("canel")})
            }else{
                this.buttonFunc(stateKey,control,input_data,timeout)
            }
        }
        processData(value,mapping,flag){
            if (value === undefined || value === null) {
                return "--";
            }
            if(typeof value === 'object'){
                try{
                    return JSON.stringify(value)
                } catch(e){
                    return value + ''
                }
            }else{
                if(flag && typeof value === 'boolean'){
                    return mapping[value ? "ON" : "OFF"]
                }else{
                    return mapping[value] || (value + '')
                }
            }
        }
        render(){
            let flaglist = [];
            flaglist[0] = false;
flaglist[1] = false;

            this.condition(this.state.state, this.state.updatetime, this.state.quality, this.state.config, flaglist);
            let temp = [];
            temp.push(React.createElement("use",{style:{visibility:flaglist[0]?"visible":"hidden"},xlinkHref:"#20405785c0346478bbe26712d22f5c2d__symbol_sup__graph4",key:"状态graph4"}));
temp.push(React.createElement("use",{style:{visibility:flaglist[1]?"visible":"hidden"},xlinkHref:"#20405785c0346478bbe26712d22f5c2d__symbol_sup__graph5",key:"状态graph5"}));

            return React.createElement("g", null, temp);
        }
    }

	ReactDOM.render(<_visualization_2d_symbol_sAbqduscW5zrfanf_图形1 deviceid="" />,document.getElementById('TempDevice'));
	setInterval(startInteval, 1000);
    </script>
<script src="/supaiot/static/js/preViewHypcon.js"></script>
</body>
</html>
