import{d as $,aJ as z,A as T,a0 as q,r as p,x as A,g as G,h as m,o as _,c as w,a as l,b as s,w as o,j as i,aM as P,i as h,t as u,B as j,aN as H,aO as K,y as B,ak as g}from"./main-dd669ad8.js";import{i as Q,j as M}from"./control-95804351.js";import{_ as W}from"./index.vue_vue_type_script_setup_true_name_SvgIcon_lang-c82a9164.js";import X from"./Trigger-b4bbff28.js";import Y from"./Group-2134e8d7.js";import"./serviceDict-f46c4ded.js";import"./device-b598f94e.js";import"./product-327919f5.js";import"./useHandleData-6b868160.js";import"./DeviceDialog.vue_vue_type_style_index_0_lang-0335bbef.js";import"./index.vue_vue_type_script_setup_true_name_ProTable_lang-ce2538eb.js";import"./useSelection-ccce70fc.js";import"./_plugin-vue_export-helper-361d09b5.js";const Z={class:"product-detail detail-page"},ee={class:"content"},te={class:"edit-btn-box"},ae={class:"basic"},se={class:"module-title"},oe={class:"is-title"},le={class:"is-value"},ie={class:"is-title"},ne={class:"is-value"},re={class:"custom-tabs-label"},ue={key:0,class:"is-edit"},ce={class:"custom-tabs-label"},de={key:0,class:"is-edit"},me=$({name:"LinkageDetail"}),xe=$({...me,setup(ge){var V;const{t:r}=z();T();const O=(V=q().params.id)!=null?V:"",v=p(),a=p({trigger:{script:"",taglist:[],reference:[]},version:0,linkageID:"",controlSet:[],description:"",linkageName:""}),f=p(),R=A(()=>{var t,e;return(e=(t=f.value)==null?void 0:t.triggerEdit)!=null?e:!1}),b=p(),U=A(()=>{var t,e;return(e=(t=b.value)==null?void 0:t.groupEdit)!=null?e:!1}),k=async()=>{var t,e;try{let d=await Q({linkageID:O});d.result&&d.result.resultCode==="0"&&(v.value=(t=JSON.parse(JSON.stringify(d.data)))!=null?t:{},a.value=(e=d.data)!=null?e:{})}catch{}},D=async()=>{f.value.submmitData(),b.value.submmitData();const t=Object.assign({},a.value);if(t.trigger.script==""){g.warning("\u89E6\u53D1\u6761\u4EF6\u4E0D\u80FD\u4E3A\u7A7A");return}if(t.controlSet.length===0){g.warning("\u6279\u91CF\u63A7\u5236\u9009\u62E9\u4E0D\u80FD\u4E3A\u7A7A");return}try{let e=await M(t);e.result&&e.result.resultCode==="0"?(g.success(r("common.message.saveMsg")),k()):g.error(e.result.resultError)}catch{}},c=p(!1),F=()=>{c.value=!0},L=()=>{c.value=!1,a.value.linkageName=v.value.linkageName,a.value.description=v.value.description},I=async()=>{if(a.value.linkageName==="")return g.warning(r("views.device.product.Detail.mingchengbunengweikong"));const t=Object.assign({},v.value);t.linkageName=a.value.linkageName,t.description=a.value.description;try{let e=await M(t);e.result&&e.result.resultCode==="0"?(g.success(r("common.message.saveMsg")),c.value=!1,k()):g.error(e.result.resultError)}catch{}},N=p("basic");return G(()=>{k()}),(t,e)=>{const d=m("el-icon"),y=m("el-button"),C=m("el-input"),x=m("el-col"),S=m("el-row"),E=m("el-tab-pane"),J=m("el-tabs");return _(),w("div",Z,[l("div",ee,[s(d,{class:"back-icon",onClick:e[0]||(e[0]=n=>t.$router.go(-1))},{default:o(()=>[s(W,{name:"rollback"})]),_:1}),s(J,{class:"detail-tab-card",modelValue:N.value,"onUpdate:modelValue":e[5]||(e[5]=n=>N.value=n)},{default:o(()=>[s(E,{name:"basic",label:i(r)("views.control.linkage.Edit.jibenxinxi")},{default:o(()=>[l("div",te,[s(y,{type:"primary",disabled:!c.value,icon:i(P),onClick:I},{default:o(()=>[h(u(t.$t("common.button.baocun")),1)]),_:1},8,["disabled","icon"]),c.value?(_(),j(y,{key:0,type:"default",icon:i(H),onClick:L},{default:o(()=>[h(u(t.$t("common.button.quxiao")),1)]),_:1},8,["icon"])):(_(),j(y,{key:1,type:"primary",icon:i(K),onClick:F},{default:o(()=>[h(u(t.$t("common.button.bianji")),1)]),_:1},8,["icon"]))]),l("div",ae,[l("div",se,[l("span",null,u(i(r)("views.control.linkage.Edit.jibenxinxi")),1)]),s(S,null,{default:o(()=>[s(x,{span:8},{default:o(()=>[l("div",oe,u(i(r)("views.control.linkage.Edit.liandongmingcheng")),1),l("div",le,[s(C,{modelValue:a.value.linkageName,"onUpdate:modelValue":e[1]||(e[1]=n=>a.value.linkageName=n),modelModifiers:{trim:!0},disabled:!c.value,maxlength:64,style:{width:"100%"}},null,8,["modelValue","disabled"])])]),_:1})]),_:1}),s(S,null,{default:o(()=>[s(x,{span:24},{default:o(()=>[l("div",ie,u(i(r)("views.control.linkage.Edit.miaoshu")),1),l("div",ne,[s(C,{type:"textarea",rows:2,modelValue:a.value.description,"onUpdate:modelValue":e[2]||(e[2]=n=>a.value.description=n),disabled:!c.value,maxlength:128,style:{width:"100%"}},null,8,["modelValue","disabled"])])]),_:1})]),_:1})])]),_:1},8,["label"]),s(E,{name:"trigger"},{label:o(()=>[l("span",re,[l("span",null,u(i(r)("views.control.linkage.Edit.chufatiaojian")),1),i(R)?(_(),w("i",ue)):B("",!0)])]),default:o(()=>[s(X,{ref_key:"triggerRef",ref:f,trigger:a.value.trigger,"onUpdate:trigger":e[3]||(e[3]=n=>a.value.trigger=n),onSave:D},null,8,["trigger"])]),_:1}),s(E,{name:"group"},{label:o(()=>[l("span",ce,[l("span",null,u(i(r)("views.control.linkage.Edit.zhixingzukong")),1),i(U)?(_(),w("i",de)):B("",!0)])]),default:o(()=>[s(Y,{ref_key:"groupRef",ref:b,group:a.value.controlSet,"onUpdate:group":e[4]||(e[4]=n=>a.value.controlSet=n),onSave:D},null,8,["group"])]),_:1})]),_:1},8,["modelValue"])])])}}});export{xe as default};
