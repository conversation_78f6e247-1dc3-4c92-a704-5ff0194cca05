import{_ as K}from"./useSelection-ccce70fc.js";import{d as x,aJ as Q,r as h,a3 as U,h as L,o as b,c as g,a as p,b as l,w as s,i as v,t as r,ao as W,F as y,y as D,j as X,aH as Y,aa as Z}from"./main-dd669ad8.js";import{u as R}from"./useHandleData-6b868160.js";import{a as ee}from"./product-327919f5.js";import{g as te}from"./device-b598f94e.js";import oe from"./EditActionGroupDialog-97f7c1ba.js";import ae from"./EditActionDialog-55b8c6cf.js";import ne from"./AddActionDrawer-636041c6.js";const ie={class:"table-main"},le={class:"table-header"},se={class:"header-button-lf"},re={key:0},ue={key:1,style:{color:"red"}},de={class:"table-empty"},ce=p("img",{src:K,alt:"notData"},null,-1),pe=x({name:"ControlActionList"}),ye=x({...pe,props:{actionGroups:{default:()=>[]}},emits:["update:actionGroups","save"],setup(G,{emit:u}){const d=G,{t:_}=Q(),H=h(),I=h([]),c=h({}),m=h(),k=h(),A=h(),C=h([]);U(()=>d.actionGroups,t=>{O(t)},{deep:!0});const O=async t=>{I.value=[];let n=[],o=[];t.forEach(a=>{a.actions&&a.actions.forEach(e=>{n.indexOf(e.classID)<0&&!c.value.hasOwnProperty(e.classID)&&n.push(e.classID),o.indexOf(e.deviceID)<0&&!c.value.hasOwnProperty(e.deviceID)&&o.push(e.deviceID)})}),await $(n,o),t.forEach(a=>{let e={name:a.groupName,id:a.groupID,level:1,point:"",value:"",classID:"",timeout:0,deviceID:"",fail_proc:0,pointType:"",retry_count:0,fatherGroupId:"",finished_wait:0,children:[]};a.actions&&a.actions.length>0&&a.actions.forEach((i,f)=>{var w;(w=e.children)==null||w.push({...i,name:"",level:2,bound:a.actions.length===1?"only":f===0?"up":f===a.actions.length-1?"down":void 0,className:c.value[i.classID]&&c.value[i.classID].name||"",deviceName:c.value[i.deviceID]&&c.value[i.deviceID].name||"",fatherGroupId:a.groupID})}),I.value.push(e)})},$=async(t,n)=>{try{if(t.length){let o=await ee(t,!0);o.result&&o.result.resultCode==="0"&&o.data&&o.data.forEach(a=>{c.value[a.classID]=a})}if(n.length){let o=await te(n,!0);o.result&&o.result.resultCode==="0"&&o.data&&o.data.forEach(a=>{c.value[a.ID]=a})}}catch{}};Z("setDeviceClassMap",$);const E=(t,n)=>{var e,i,f;let a=(i=((e=c.value[n])!=null?e:{attribute:[]}).attribute)==null?void 0:i.find(w=>w.code==t);return(f=a==null?void 0:a.name)!=null?f:""},N=(t,n,o)=>{var i,f;let e=(f=((i=c.value[o])!=null?i:{attribute:[]}).attribute)==null?void 0:f.find(w=>w.code==n);return e?e.type==="AO"||e.type==="AI"||e.type==="SO"||e.type==="SI"||e.type==="AIO"||e.type==="SIO"?t:e.value&&e.value[t]||"":""},B=()=>{var t;(t=m.value)==null||t.openDialog(-1)},j=t=>{var o;let n=d.actionGroups.findIndex(a=>a.groupID===t.id);n>-1&&((o=m.value)==null||o.openDialog(n,d.actionGroups[n]))},z=t=>{if(t.index===-1){let n=[...d.actionGroups];n.push(t.data),u("update:actionGroups",n),u("save")}else{let n=[...d.actionGroups];n[t.index].groupID===t.data.groupID&&(n[t.index].groupName=t.data.groupName),u("update:actionGroups",n),u("save")}},F=async t=>{const n=async()=>{let o=d.actionGroups.findIndex(a=>a.groupID===t.id);if(o>-1){let a=[...d.actionGroups];a.splice(o,1),u("update:actionGroups",a),u("save")}return Promise.resolve(!0)};try{await R(n,null,_("common.message.beforeDelete"),"warning",!0)}catch{return}},M=t=>{var o;let n=d.actionGroups.findIndex(a=>a.groupID===t.id);n>-1&&((o=A.value)==null||o.openDrawer(d.actionGroups[n]))},P=t=>{let n=[...d.actionGroups],o=n.find(a=>a.groupID===t[0].fatherGroupId);o&&(o.actions=[...o.actions,...t]),u("update:actionGroups",n),u("save")},S=t=>{let n=d.actionGroups.find(o=>o.groupID===t.fatherGroupId);if(n){let o=n.actions.findIndex(a=>a.id===t.id);o>-1&&(C.value=c.value[n.actions[o].classID].attribute,k.value.openDialog(o,n.actions[o]))}},T=t=>{let n=[...d.actionGroups],o=n.find(a=>a.groupID===t.data.fatherGroupId);o&&(o.actions[t.index]={...t.data}),u("update:actionGroups",n),u("save")},V=async t=>{const n=async()=>{let o=[...d.actionGroups],a=o.find(e=>e.groupID===t.fatherGroupId);if(a){let e=a.actions.findIndex(i=>i.id===t.id);if(e>-1)return a.actions.splice(e,1),u("update:actionGroups",o),u("save"),Promise.resolve(!0)}};try{await R(n,null,_("common.message.beforeDelete"),"warning",!0)}catch{return}},J=t=>{let n=[...d.actionGroups],o=n.find(a=>a.groupID===t.fatherGroupId);if(o){let a=o.actions.findIndex(e=>e.id===t.id);if(a>-1){let e=o.actions[a];o.actions.splice(a,1),o.actions.splice(a-1,0,e),u("update:actionGroups",n),u("save")}}},q=t=>{let n=[...d.actionGroups],o=n.find(a=>a.groupID===t.fatherGroupId);if(o){let a=o.actions.findIndex(e=>e.id===t.id);if(a>-1){let e=o.actions[a];o.actions.splice(a,1),o.actions.splice(a+1,0,e),u("update:actionGroups",n),u("save")}}};return(t,n)=>{const o=L("el-button"),a=L("el-table-column");return b(),g("div",ie,[p("div",le,[p("div",se,[l(o,{type:"primary",onClick:B},{default:s(()=>[v(r(t.$t("common.button.tianjia")),1)]),_:1})])]),l(X(Y),{ref_key:"tableRef",ref:H,data:I.value,"row-key":"id","default-expand-all":!0},{empty:s(()=>[p("div",de,[W(t.$slots,"empty",{},()=>[ce,p("div",null,r(t.$t("common.message.nodata")),1)])])]),default:s(()=>[l(a,{label:t.$t("views.control.group.ActionList.mingcheng"),width:"240",align:"left","show-overflow-tooltip":""},{default:s(e=>[p("span",null,r(e.row.name),1)]),_:1},8,["label"]),l(a,{label:t.$t("views.control.group.ActionList.shebeishili"),"show-overflow-tooltip":""},{default:s(e=>[e.row.level>1?(b(),g(y,{key:0},[e.row.deviceName?(b(),g("span",re,r(e.row.deviceName),1)):(b(),g("span",ue,r("\u8BE5\u8BBE\u5907\u5DF2\u88AB\u5220\u9664")))],64)):D("",!0)]),_:1},8,["label"]),l(a,{label:t.$t("views.control.group.ActionList.shebeimoxing"),"show-overflow-tooltip":""},{default:s(e=>[p("span",null,r(e.row.className),1)]),_:1},8,["label"]),l(a,{label:t.$t("views.control.group.ActionList.kongzhidian"),"show-overflow-tooltip":""},{default:s(e=>[p("span",null,r(E(e.row.point,e.row.classID)),1)]),_:1},8,["label"]),l(a,{label:t.$t("views.control.group.ActionList.kongzhizhi"),"show-overflow-tooltip":""},{default:s(e=>[p("span",null,r(N(e.row.value,e.row.point,e.row.classID)),1)]),_:1},8,["label"]),l(a,{label:t.$t("views.control.group.ActionList.shibaizhongshi"),align:"center",width:"160"},{default:s(e=>[p("span",null,r(e.row.level===1?"":e.row.retry_count),1)]),_:1},8,["label"]),l(a,{label:t.$t("views.control.group.ActionList.chaoshishijian"),align:"center",width:"160"},{default:s(e=>[p("span",null,r(e.row.level===1?"":e.row.timeout),1)]),_:1},8,["label"]),l(a,{label:t.$t("common.table.caozuo"),align:"center",width:"200"},{default:s(e=>[e.row.level===1?(b(),g(y,{key:0},[l(o,{type:"primary",link:"",onClick:i=>j(e.row)},{default:s(()=>[v(r(t.$t("common.button.bianji")),1)]),_:2},1032,["onClick"]),l(o,{type:"primary",link:"",onClick:i=>M(e.row)},{default:s(()=>[v(r(t.$t("views.control.group.ActionList.tianjiadongzuo")),1)]),_:2},1032,["onClick"]),l(o,{type:"primary",link:"",disabled:e.row.children&&e.row.children.length>0,onClick:i=>F(e.row)},{default:s(()=>[v(r(t.$t("common.button.shanchu")),1)]),_:2},1032,["disabled","onClick"])],64)):D("",!0),e.row.level===2?(b(),g(y,{key:1},[l(o,{type:"primary",link:"",onClick:i=>S(e.row)},{default:s(()=>[v(r(t.$t("common.button.bianji")),1)]),_:2},1032,["onClick"]),l(o,{type:"primary",link:"",onClick:i=>V(e.row)},{default:s(()=>[v(r(t.$t("common.button.shanchu")),1)]),_:2},1032,["onClick"]),l(o,{type:"primary",link:"",disabled:e.row.bound==="up"||e.row.bound==="only",onClick:i=>J(e.row)},{default:s(()=>[v(r(t.$t("views.control.group.ActionList.shangyi")),1)]),_:2},1032,["disabled","onClick"]),l(o,{type:"primary",link:"",disabled:e.row.bound==="down"||e.row.bound==="only",onClick:i=>q(e.row)},{default:s(()=>[v(r(t.$t("views.control.group.ActionList.xiayi")),1)]),_:2},1032,["disabled","onClick"])],64)):D("",!0)]),_:1},8,["label"])]),_:3},8,["data"]),l(oe,{ref_key:"editGroupRef",ref:m,"action-groups":G.actionGroups,onEditHandle:z},null,8,["action-groups"]),l(ae,{ref_key:"editActionRef",ref:k,attributes:C.value,onEditHandle:T},null,8,["attributes"]),l(ne,{ref_key:"addActionRef",ref:A,pointFilter:E,valueFilter:N,deviceClass:c.value,onEditHandle:P},null,8,["deviceClass"])])}}});export{ye as _};
